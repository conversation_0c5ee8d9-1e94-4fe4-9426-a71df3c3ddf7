<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YalaOffice - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .offline-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .offline-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: white;
        }

        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }

        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .offline-features {
            margin-top: 2rem;
            text-align: left;
        }

        .offline-features h3 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            text-align: center;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            opacity: 0.8;
            display: flex;
            align-items: center;
        }

        .feature-list li::before {
            content: "✓";
            margin-right: 0.5rem;
            color: #4ade80;
            font-weight: bold;
        }

        .connection-status {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .status-offline {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .status-online {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        @media (max-width: 768px) {
            .offline-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .offline-message {
                font-size: 1rem;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">
            📱
        </div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            YalaOffice requires an internet connection for real-time data synchronization. Please check your connection and try again.
        </p>

        <div class="connection-status status-offline" id="connectionStatus">
            🔴 No internet connection
        </div>

        <div style="margin: 2rem 0;">
            <button class="retry-button" onclick="window.location.reload()">
                🔄 Try Again
            </button>
            <a href="/" class="retry-button">
                🏠 Go Home
            </a>
        </div>

        <div class="offline-features">
            <h3>Real-time Features Require Connection:</h3>
            <ul class="feature-list">
                <li>Live inventory management</li>
                <li>Real-time order processing</li>
                <li>Instant customer updates</li>
                <li>Live transaction tracking</li>
                <li>Dynamic report generation</li>
                <li>Immediate data synchronization</li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.textContent = '🟢 Back online! Real-time sync active...';
                statusElement.className = 'connection-status status-online';

                // Auto-reload after a short delay when back online
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusElement.textContent = '🔴 No internet connection - Real-time sync unavailable';
                statusElement.className = 'connection-status status-offline';
            }
        }

        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(() => {
            // Try to fetch a small resource to check actual connectivity
            fetch('/manifest.json', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                if (!navigator.onLine) {
                    // Force online status update
                    window.dispatchEvent(new Event('online'));
                }
            })
            .catch(() => {
                if (navigator.onLine) {
                    // Force offline status update
                    window.dispatchEvent(new Event('offline'));
                }
            });
        }, 5000);

        // Service worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                console.log('Service Worker is ready');
                
                // Check for updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New version available
                            if (confirm('A new version of YalaOffice is available. Reload to update?')) {
                                window.location.reload();
                            }
                        }
                    });
                });
            });
        }
    </script>
</body>
</html>
