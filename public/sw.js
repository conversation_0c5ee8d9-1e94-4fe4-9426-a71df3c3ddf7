// YalaOffice Service Worker for PWA functionality (Real-time sync only)
const CACHE_NAME = 'yalaoffice-v2.0.0-' + Date.now(); // Force cache refresh
const OFFLINE_URL = '/offline.html';
const DEVELOPMENT_MODE = true; // Set to false for production
const REALTIME_SYNC_ONLY = true; // Pure real-time synchronization, no offline caching

// Minimal resources to cache (only essential PWA files)
const STATIC_CACHE_URLS = [
  '/offline.html',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// Note: API endpoints are NOT cached - pure real-time sync only

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static resources');
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => {
        console.log('Service Worker: Installation complete');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Installation failed', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activation complete');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle navigation requests (real-time only, no caching)
  if (request.mode === 'navigate') {
    event.respondWith(
      fetch(request).catch(() => {
        // Only show offline page if network fails
        return caches.match(OFFLINE_URL);
      })
    );
    return;
  }

  // Handle API requests (real-time only, no caching)
  if (url.pathname.startsWith('/api/') || request.url.includes('supabase.co')) {
    // All API requests go directly to network - no caching
    event.respondWith(
      fetch(request).catch(() => {
        // Return offline indicator for failed API requests
        return new Response(
          JSON.stringify({
            error: 'Offline',
            message: 'Real-time sync unavailable - please check your connection'
          }),
          {
            status: 503,
            statusText: 'Service Unavailable',
            headers: { 'Content-Type': 'application/json' }
          }
        );
      })
    );
    return;
  }

  // Handle static resources
  if (DEVELOPMENT_MODE) {
    // In development, always fetch fresh content
    event.respondWith(
      fetch(request).catch(() => {
        if (request.destination === 'document') {
          return caches.match(OFFLINE_URL);
        }
        return new Response('', { status: 404 });
      })
    );
  } else {
    // Handle static resources with cache-first strategy
    event.respondWith(
      caches.match(request)
        .then((cachedResponse) => {
          return cachedResponse || fetch(request)
            .then((response) => {
              // Cache successful responses
              if (response.status === 200) {
                const responseClone = response.clone();
                caches.open(CACHE_NAME)
                  .then((cache) => cache.put(request, responseClone));
              }
              return response;
            });
        })
        .catch(() => {
          // Return offline page for failed requests
          if (request.destination === 'document') {
            return caches.match(OFFLINE_URL);
          }
        })
    );
  }
});

// Note: Offline API request handling removed - using pure real-time sync only

// Note: IndexedDB offline storage removed - using pure real-time sync only

// Note: Background sync removed - using pure real-time sync only

// Note: IndexedDB helper functions removed - using pure real-time sync only

// Push notification event
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      vibrate: [100, 50, 100],
      data: data.data,
      actions: data.actions || []
    };

    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  if (event.action) {
    // Handle action button clicks
    handleNotificationAction(event.action, event.notification.data);
  } else {
    // Handle notification click
    event.waitUntil(
      clients.openWindow(event.notification.data?.url || '/')
    );
  }
});

function handleNotificationAction(action, data) {
  switch (action) {
    case 'view':
      clients.openWindow(data?.url || '/');
      break;
    case 'dismiss':
      // Just close the notification
      break;
    default:
      clients.openWindow('/');
  }
}
