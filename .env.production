# YalaOffice Production Environment Configuration
# DO NOT COMMIT THIS FILE TO VERSION CONTROL

# Application Configuration
NODE_ENV=production
VITE_APP_VERSION=1.0.0
VITE_APP_NAME=YalaOffice
VITE_APP_DESCRIPTION="Smart Supply Management System for Moroccan Businesses"

# API Configuration
VITE_API_BASE_URL=https://api.yalaoffice.com
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3

# Supabase Configuration (Production)
VITE_SUPABASE_URL=https://umzikqwughlzkiarldoa.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtemlrcXd1Z2hsemtpYXJsZG9hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMjczMjgsImV4cCI6MjA2NTcwMzMyOH0.3YYeZWUp3c0IIwlORbgmrHlcPoyk5iasRnmGOEHTvoY
SUPABASE_SERVICE_KEY=your-service-key-here
SUPABASE_JWT_SECRET=your-jwt-secret-here

# Database Configuration
DATABASE_URL=************************************/yalaoffice_prod
DATABASE_POOL_SIZE=20
DATABASE_CONNECTION_TIMEOUT=30000
DATABASE_IDLE_TIMEOUT=600000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
REDIS_MAX_CONNECTIONS=10

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-here

# CORS Configuration
CORS_ORIGIN=https://yalaoffice.com,https://www.yalaoffice.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# File Upload Configuration
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx
UPLOAD_PATH=/var/uploads/yalaoffice

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
EMAIL_FROM=YalaOffice <<EMAIL>>

# SMS Configuration (for Morocco)
SMS_PROVIDER=twilio
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+**********
SMS_FROM=YalaOffice

# Payment Gateway Configuration (Morocco)
# CMI (Centre Monétique Interbancaire)
CMI_MERCHANT_ID=your-cmi-merchant-id
CMI_ACCESS_KEY=your-cmi-access-key
CMI_SECRET_KEY=your-cmi-secret-key
CMI_ENVIRONMENT=production

# Stripe (International)
STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-key
STRIPE_SECRET_KEY=sk_live_your-stripe-secret
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# AWS Configuration (for backups and file storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=eu-west-1
AWS_S3_BUCKET=yalaoffice-production
AWS_CLOUDFRONT_DOMAIN=cdn.yalaoffice.com

# Monitoring Configuration
MONITORING_ENABLED=true
METRICS_ENDPOINT=https://metrics.yalaoffice.com
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=production

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/var/log/yalaoffice/app.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=10

# Performance Configuration
ENABLE_COMPRESSION=true
COMPRESSION_LEVEL=6
CACHE_TTL=3600
STATIC_CACHE_TTL=86400

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/yalaoffice.crt
SSL_KEY_PATH=/etc/ssl/private/yalaoffice.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt
FORCE_HTTPS=true

# Domain Configuration
DOMAIN_NAME=yalaoffice.com
SUBDOMAIN_API=api
SUBDOMAIN_CDN=cdn
SUBDOMAIN_ADMIN=admin

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=yalaoffice-backups
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key

# Notification Configuration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
SLACK_CHANNEL=#alerts
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/webhook

# Feature Flags
FEATURE_ANALYTICS=true
FEATURE_REPORTS=true
FEATURE_NOTIFICATIONS=true
FEATURE_MOBILE_APP=true
FEATURE_API_ACCESS=true
FEATURE_INTEGRATIONS=false

# Localization
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,fr,ar
DEFAULT_CURRENCY=MAD
DEFAULT_TIMEZONE=Africa/Casablanca

# Business Configuration
COMPANY_NAME=YalaOffice
COMPANY_ADDRESS=Casablanca, Morocco
COMPANY_PHONE=+212-XXX-XXXXXX
COMPANY_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# Legal Configuration
PRIVACY_POLICY_URL=https://yalaoffice.com/privacy
TERMS_OF_SERVICE_URL=https://yalaoffice.com/terms
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=2555

# Development Tools (disabled in production)
VITE_DEVTOOLS=false
VITE_DEBUG=false
VITE_MOCK_API=false

# Health Check Configuration
HEALTH_CHECK_ENDPOINT=/health
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Scaling Configuration
MAX_WORKERS=4
WORKER_TIMEOUT=30000
CLUSTER_MODE=true
PM2_INSTANCES=max

# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_ENABLED=true
XSS_PROTECTION=true
FRAME_OPTIONS=SAMEORIGIN

# API Documentation
API_DOCS_ENABLED=false
API_DOCS_PATH=/api/docs
SWAGGER_UI_ENABLED=false

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=YalaOffice is currently under maintenance. Please try again later.
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1
