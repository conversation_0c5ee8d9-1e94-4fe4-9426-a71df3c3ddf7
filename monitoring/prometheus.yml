# Prometheus Configuration for YalaOffice Production Monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'yalaoffice-production'
    replica: 'prometheus-1'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # YalaOffice Application
  - job_name: 'yalaoffice-app'
    static_configs:
      - targets: ['yalaoffice-app:80']
    scrape_interval: 10s
    metrics_path: /metrics
    scheme: http
    params:
      format: ['prometheus']

  # Node Exporter (System Metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # Redis Metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 15s
    metrics_path: /metrics

  # Nginx Metrics
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-lb:9113']
    scrape_interval: 15s
    metrics_path: /metrics

  # Docker Container Metrics
  - job_name: 'docker'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: /metrics

  # Supabase Database Metrics (if accessible)
  - job_name: 'supabase-postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics
    params:
      collect[]:
        - 'pg_stat_database'
        - 'pg_stat_user_tables'
        - 'pg_stat_activity'

  # Application-specific metrics
  - job_name: 'yalaoffice-business-metrics'
    static_configs:
      - targets: ['yalaoffice-app:80']
    scrape_interval: 30s
    metrics_path: /api/metrics
    scheme: http
    basic_auth:
      username: 'metrics'
      password: 'metrics_password'

  # SSL Certificate Monitoring
  - job_name: 'ssl-exporter'
    static_configs:
      - targets: ['ssl-exporter:9219']
    scrape_interval: 300s
    metrics_path: /metrics

  # Blackbox Exporter for External Monitoring
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - https://yalaoffice.com
        - https://yalaoffice.com/health
        - https://yalaoffice.com/api/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

# Remote write configuration (for long-term storage)
remote_write:
  - url: "http://cortex:9009/api/prom/push"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# Storage configuration
storage:
  tsdb:
    path: /prometheus
    retention.time: 15d
    retention.size: 10GB
    wal-compression: true
