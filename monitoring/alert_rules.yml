# YalaOffice Production Alert Rules
groups:
  # Application Health Alerts
  - name: yalaoffice.application
    rules:
      - alert: ApplicationDown
        expr: up{job="yalaoffice-app"} == 0
        for: 1m
        labels:
          severity: critical
          service: yalaoffice
        annotations:
          summary: "YalaOffice application is down"
          description: "YalaOffice application has been down for more than 1 minute"
          runbook_url: "https://docs.yalaoffice.com/runbooks/application-down"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="yalaoffice-app"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: yalaoffice
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for 5 minutes"

      - alert: HighErrorRate
        expr: rate(http_requests_total{job="yalaoffice-app",status=~"5.."}[5m]) / rate(http_requests_total{job="yalaoffice-app"}[5m]) > 0.05
        for: 3m
        labels:
          severity: critical
          service: yalaoffice
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for 3 minutes"

      - alert: LowSuccessRate
        expr: rate(http_requests_total{job="yalaoffice-app",status=~"2.."}[5m]) / rate(http_requests_total{job="yalaoffice-app"}[5m]) < 0.95
        for: 5m
        labels:
          severity: warning
          service: yalaoffice
        annotations:
          summary: "Low success rate detected"
          description: "Success rate is {{ $value | humanizePercentage }} for 5 minutes"

  # System Resource Alerts
  - name: yalaoffice.system
    rules:
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: LowDiskSpace
        expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "Low disk space detected"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} ({{ $labels.mountpoint }})"

      - alert: HighDiskIOWait
        expr: irate(node_cpu_seconds_total{mode="iowait"}[5m]) * 100 > 20
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High disk I/O wait detected"
          description: "I/O wait is {{ $value }}% on {{ $labels.instance }}"

  # Database Alerts
  - name: yalaoffice.database
    rules:
      - alert: DatabaseConnectionsHigh
        expr: pg_stat_activity_count > 80
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "High database connections"
          description: "Database has {{ $value }} active connections"

      - alert: DatabaseSlowQueries
        expr: pg_stat_activity_max_tx_duration > 300
        for: 2m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "Slow database queries detected"
          description: "Longest running query is {{ $value }}s"

      - alert: DatabaseDeadlocks
        expr: increase(pg_stat_database_deadlocks[5m]) > 0
        for: 0m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "Database deadlocks detected"
          description: "{{ $value }} deadlocks detected in the last 5 minutes"

  # Redis Alerts
  - name: yalaoffice.redis
    rules:
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value }}%"

      - alert: RedisHighConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis high connections"
          description: "Redis has {{ $value }} connected clients"

  # Business Logic Alerts
  - name: yalaoffice.business
    rules:
      - alert: HighOrderFailureRate
        expr: rate(yalaoffice_orders_failed_total[5m]) / rate(yalaoffice_orders_total[5m]) > 0.1
        for: 3m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "High order failure rate"
          description: "Order failure rate is {{ $value | humanizePercentage }}"

      - alert: LowInventoryAlert
        expr: yalaoffice_inventory_low_stock_items > 10
        for: 1m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "Multiple items with low inventory"
          description: "{{ $value }} items have low inventory levels"

      - alert: PaymentProcessingIssues
        expr: rate(yalaoffice_payments_failed_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
          service: business
        annotations:
          summary: "Payment processing issues detected"
          description: "Payment failure rate is {{ $value | humanizePercentage }}"

      - alert: UnusualOrderVolume
        expr: rate(yalaoffice_orders_total[1h]) > 2 * rate(yalaoffice_orders_total[24h] offset 24h)
        for: 10m
        labels:
          severity: info
          service: business
        annotations:
          summary: "Unusual order volume detected"
          description: "Current order rate is significantly higher than usual"

  # Security Alerts
  - name: yalaoffice.security
    rules:
      - alert: HighFailedLoginAttempts
        expr: rate(yalaoffice_auth_failed_attempts_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High failed login attempts"
          description: "Failed login rate is {{ $value }} attempts per second"

      - alert: SuspiciousAPIActivity
        expr: rate(http_requests_total{status="403"}[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Suspicious API activity detected"
          description: "High rate of 403 responses: {{ $value }} per second"

      - alert: SSLCertificateExpiring
        expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 7
        for: 1m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate expires in {{ $value | humanizeDuration }}"

  # Infrastructure Alerts
  - name: yalaoffice.infrastructure
    rules:
      - alert: ContainerRestarting
        expr: increase(container_start_time_seconds[5m]) > 0
        for: 0m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "Container restarting"
          description: "Container {{ $labels.name }} has restarted"

      - alert: HighNetworkTraffic
        expr: rate(node_network_receive_bytes_total[5m]) > 100 * 1024 * 1024
        for: 5m
        labels:
          severity: info
          service: infrastructure
        annotations:
          summary: "High network traffic"
          description: "Network traffic is {{ $value | humanizeBytes }}/s on {{ $labels.instance }}"

      - alert: LoadBalancerDown
        expr: up{job="nginx"} == 0
        for: 1m
        labels:
          severity: critical
          service: infrastructure
        annotations:
          summary: "Load balancer is down"
          description: "Nginx load balancer has been down for more than 1 minute"
