// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://umzikqwughlzkiarldoa.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtemlrcXd1Z2hsemtpYXJsZG9hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMjczMjgsImV4cCI6MjA2NTcwMzMyOH0.3YYeZWUp3c0IIwlORbgmrHlcPoyk5iasRnmGOEHTvoY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);