import { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface ComparisonContextType {
  comparisonList: string[];
  addToComparison: (productId: string) => void;
  removeFromComparison: (productId: string) => void;
  clearComparison: () => void;
  isInComparison: (productId: string) => boolean;
  comparisonCount: number;
  maxComparisons: number;
}

const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined);

interface ComparisonProviderProps {
  children: ReactNode;
  maxComparisons?: number;
}

export const ComparisonProvider = ({ children, maxComparisons = 4 }: ComparisonProviderProps) => {
  const [comparisonList, setComparisonList] = useState<string[]>([]);

  const addToComparison = useCallback((productId: string) => {
    setComparisonList(prev => {
      if (prev.includes(productId)) {
        return prev; // Already in comparison
      }
      
      if (prev.length >= maxComparisons) {
        // Remove the oldest item and add the new one
        return [...prev.slice(1), productId];
      }
      
      return [...prev, productId];
    });
  }, [maxComparisons]);

  const removeFromComparison = useCallback((productId: string) => {
    setComparisonList(prev => prev.filter(id => id !== productId));
  }, []);

  const clearComparison = useCallback(() => {
    setComparisonList([]);
  }, []);

  const isInComparison = useCallback((productId: string) => {
    return comparisonList.includes(productId);
  }, [comparisonList]);

  const value: ComparisonContextType = {
    comparisonList,
    addToComparison,
    removeFromComparison,
    clearComparison,
    isInComparison,
    comparisonCount: comparisonList.length,
    maxComparisons
  };

  return (
    <ComparisonContext.Provider value={value}>
      {children}
    </ComparisonContext.Provider>
  );
};

export const useComparison = () => {
  const context = useContext(ComparisonContext);
  if (context === undefined) {
    throw new Error('useComparison must be used within a ComparisonProvider');
  }
  return context;
};
