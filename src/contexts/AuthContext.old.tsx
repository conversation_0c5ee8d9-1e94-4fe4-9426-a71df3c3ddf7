
import * as React from 'react';
import { User } from '../types/user';
import { supabase } from '@/integrations/supabase/client';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (userData: User) => void;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  sessionExpiry: Date | null;
  refreshSession: () => void;
}

const AuthContext = React.createContext<AuthContextType | undefined>(undefined);

const ROLE_PERMISSIONS = {
  admin: ['*'], // Admin has all permissions
  manager: [
    'inventory.read', 'inventory.write', 'inventory.delete',
    'orders.read', 'orders.write', 'orders.update',
    'users.read', 'users.write',
    'branches.read', 'branches.write',
    'analytics.read', 'reports.read'
  ],
  delivery: [
    'orders.read', 'orders.update',
    'deliveries.read', 'deliveries.write'
  ],
  client: [
    'products.read', 'orders.create', 'orders.read',
    'profile.read', 'profile.write'
  ],
  reseller: [
    'products.read', 'orders.create', 'orders.read', 'orders.bulk',
    'profile.read', 'profile.write', 'wholesale.read'
  ]
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Initialize state with error boundaries
  const [user, setUser] = React.useState<User | null>(null);
  const [sessionExpiry, setSessionExpiry] = React.useState<Date | null>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    // Check for existing Supabase session
    const checkSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Session check error:', error);
          setLoading(false);
          return;
        }

        if (session?.user) {
          // Try to get user profile from database
          const { data: userProfile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (profileError) {
            console.warn('Profile not found, using auth metadata');
            // Fallback to auth metadata
            const userData: User = {
              id: session.user.id,
              fullName: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
              email: session.user.email || '',
              phone: session.user.user_metadata?.phone || '',
              city: session.user.user_metadata?.city || 'Tetouan',
              address: '',
              userType: session.user.user_metadata?.user_type || 'client',
              isAuthenticated: true
            };
            setUser(userData);
          } else {
            // Use profile data
            const userData: User = {
              id: userProfile.id,
              fullName: userProfile.full_name,
              email: userProfile.email,
              phone: userProfile.phone || '',
              city: userProfile.city || 'Tetouan',
              address: userProfile.company_address || '',
              userType: userProfile.user_type,
              isAuthenticated: true
            };
            setUser(userData);
          }

          // Set session expiry
          if (session.expires_at) {
            setSessionExpiry(new Date(session.expires_at * 1000));
          }
        }
      } catch (error) {
        console.error('Error checking session:', error);
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_OUT' || !session) {
        setUser(null);
        setSessionExpiry(null);
      } else if (event === 'SIGNED_IN' && session?.user) {
        // Handle sign in - user data will be set by the login function
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const login = (userData: User) => {
    const expiry = new Date();
    expiry.setHours(expiry.getHours() + 8); // 8 hour session
    
    setUser(userData);
    setSessionExpiry(expiry);
    
    localStorage.setItem('yala_user', JSON.stringify(userData));
    localStorage.setItem('yala_session_expiry', expiry.toISOString());
  };

  const logout = async () => {
    try {
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Logout error:', error);
    }

    setUser(null);
    setSessionExpiry(null);
    localStorage.removeItem('yala_user');
    localStorage.removeItem('yala_session_expiry');
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...updates };
      setUser(updatedUser);
      localStorage.setItem('yala_user', JSON.stringify(updatedUser));
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    const userPermissions = ROLE_PERMISSIONS[user.userType] || [];
    return userPermissions.includes('*') || userPermissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    return user?.userType === role;
  };

  const refreshSession = () => {
    if (user) {
      const expiry = new Date();
      expiry.setHours(expiry.getHours() + 8);
      setSessionExpiry(expiry);
      localStorage.setItem('yala_session_expiry', expiry.toISOString());
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      isAuthenticated: !!user,
      loading,
      login,
      logout,
      updateUser,
      hasPermission,
      hasRole,
      sessionExpiry,
      refreshSession
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = React.useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
