import { useState, useEffect } from 'react';

// Breakpoint definitions
const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
};

// Device type detection
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouch: boolean;
  orientation: 'portrait' | 'landscape';
  screenSize: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  width: number;
  height: number;
}

// PWA detection
export interface PWAInfo {
  isInstalled: boolean;
  isStandalone: boolean;
  canInstall: boolean;
  displayMode: 'browser' | 'standalone' | 'minimal-ui' | 'fullscreen';
}

// Combined responsive information
export interface ResponsiveInfo extends DeviceInfo, PWAInfo {
  isOnline: boolean;
  supportsTouch: boolean;
  supportsHover: boolean;
  prefersReducedMotion: boolean;
  colorScheme: 'light' | 'dark';
}

// Custom hook for responsive design
export const useResponsive = (): ResponsiveInfo => {
  const [responsiveInfo, setResponsiveInfo] = useState<ResponsiveInfo>({
    // Device info
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isTouch: false,
    orientation: 'landscape',
    screenSize: 'lg',
    width: 1024,
    height: 768,
    
    // PWA info
    isInstalled: false,
    isStandalone: false,
    canInstall: false,
    displayMode: 'browser',
    
    // Additional info
    isOnline: true,
    supportsTouch: false,
    supportsHover: true,
    prefersReducedMotion: false,
    colorScheme: 'light'
  });

  // Detect screen size category
  const getScreenSize = (width: number): 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' => {
    if (width >= breakpoints['2xl']) return '2xl';
    if (width >= breakpoints.xl) return 'xl';
    if (width >= breakpoints.lg) return 'lg';
    if (width >= breakpoints.md) return 'md';
    if (width >= breakpoints.sm) return 'sm';
    return 'xs';
  };

  // Detect device type
  const getDeviceType = (width: number, userAgent: string) => {
    const isMobileUA = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isTabletUA = /iPad|Android(?!.*Mobile)/i.test(userAgent);
    
    return {
      isMobile: width < breakpoints.md || (isMobileUA && !isTabletUA),
      isTablet: (width >= breakpoints.md && width < breakpoints.lg) || isTabletUA,
      isDesktop: width >= breakpoints.lg && !isMobileUA
    };
  };

  // Detect PWA status
  const getPWAInfo = (): PWAInfo => {
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                        (window.navigator as any).standalone === true;
    
    let displayMode: 'browser' | 'standalone' | 'minimal-ui' | 'fullscreen' = 'browser';
    
    if (window.matchMedia('(display-mode: standalone)').matches) {
      displayMode = 'standalone';
    } else if (window.matchMedia('(display-mode: minimal-ui)').matches) {
      displayMode = 'minimal-ui';
    } else if (window.matchMedia('(display-mode: fullscreen)').matches) {
      displayMode = 'fullscreen';
    }

    return {
      isInstalled: isStandalone,
      isStandalone,
      canInstall: false, // Will be updated by PWA service
      displayMode
    };
  };

  // Update responsive information
  const updateResponsiveInfo = () => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const userAgent = navigator.userAgent;
    
    const deviceType = getDeviceType(width, userAgent);
    const screenSize = getScreenSize(width);
    const pwaInfo = getPWAInfo();
    
    const orientation: 'portrait' | 'landscape' = height > width ? 'portrait' : 'landscape';
    
    // Feature detection
    const supportsTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const supportsHover = window.matchMedia('(hover: hover)').matches;
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    const colorScheme: 'light' | 'dark' = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    
    setResponsiveInfo({
      ...deviceType,
      isTouch: supportsTouch,
      orientation,
      screenSize,
      width,
      height,
      ...pwaInfo,
      isOnline: navigator.onLine,
      supportsTouch,
      supportsHover,
      prefersReducedMotion,
      colorScheme
    });
  };

  useEffect(() => {
    // Initial update
    updateResponsiveInfo();

    // Event listeners
    const handleResize = () => updateResponsiveInfo();
    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(updateResponsiveInfo, 100);
    };
    const handleOnlineStatusChange = () => updateResponsiveInfo();

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);

    // Media query listeners
    const mediaQueries = [
      window.matchMedia('(display-mode: standalone)'),
      window.matchMedia('(hover: hover)'),
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-color-scheme: dark)')
    ];

    const handleMediaQueryChange = () => updateResponsiveInfo();
    
    mediaQueries.forEach(mq => {
      if (mq.addEventListener) {
        mq.addEventListener('change', handleMediaQueryChange);
      } else {
        // Fallback for older browsers
        mq.addListener(handleMediaQueryChange);
      }
    });

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
      
      mediaQueries.forEach(mq => {
        if (mq.removeEventListener) {
          mq.removeEventListener('change', handleMediaQueryChange);
        } else {
          // Fallback for older browsers
          mq.removeListener(handleMediaQueryChange);
        }
      });
    };
  }, []);

  return responsiveInfo;
};

// Hook for specific breakpoint checks
export const useBreakpoint = (breakpoint: keyof typeof breakpoints): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(`(min-width: ${breakpoints[breakpoint]}px)`);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setMatches(e.matches);
    };

    setMatches(mediaQuery.matches);
    
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      // Fallback for older browsers
      mediaQuery.addListener(handleChange);
      return () => mediaQuery.removeListener(handleChange);
    }
  }, [breakpoint]);

  return matches;
};

// Hook for mobile-first responsive design
export const useMobileFirst = () => {
  const responsive = useResponsive();
  
  return {
    ...responsive,
    // Convenience methods
    showMobileLayout: responsive.isMobile,
    showTabletLayout: responsive.isTablet && !responsive.isMobile,
    showDesktopLayout: responsive.isDesktop,
    
    // Responsive utilities
    isSmallScreen: responsive.screenSize === 'xs' || responsive.screenSize === 'sm',
    isMediumScreen: responsive.screenSize === 'md',
    isLargeScreen: responsive.screenSize === 'lg' || responsive.screenSize === 'xl' || responsive.screenSize === '2xl',
    
    // Touch and interaction
    shouldUseTouchUI: responsive.isTouch || responsive.isMobile,
    shouldShowHoverEffects: responsive.supportsHover && !responsive.isTouch,
    
    // PWA specific
    shouldShowInstallPrompt: !responsive.isInstalled && responsive.canInstall,
    shouldUseNativeFeatures: responsive.isInstalled || responsive.isStandalone,
    
    // Performance
    shouldReduceAnimations: responsive.prefersReducedMotion,
    shouldOptimizeForTouch: responsive.isTouch || responsive.isMobile
  };
};

// Hook for PWA-specific functionality
export const usePWA = () => {
  const responsive = useResponsive();
  
  return {
    isInstalled: responsive.isInstalled,
    isStandalone: responsive.isStandalone,
    canInstall: responsive.canInstall,
    displayMode: responsive.displayMode,
    
    // PWA utilities
    shouldShowPWAFeatures: responsive.isInstalled || responsive.isStandalone,
    shouldShowInstallButton: !responsive.isInstalled && responsive.canInstall,
    isRunningAsPWA: responsive.isStandalone || responsive.displayMode === 'standalone',
    
    // Offline capabilities
    isOnline: responsive.isOnline,
    supportsOffline: 'serviceWorker' in navigator,
    supportsPushNotifications: 'Notification' in window && 'serviceWorker' in navigator,
    supportsBackgroundSync: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype
  };
};

export default useResponsive;
