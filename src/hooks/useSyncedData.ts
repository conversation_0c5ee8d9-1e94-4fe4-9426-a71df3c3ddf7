/**
 * Synchronized Data Hooks
 * React hooks that automatically sync with database and real-time updates
 */

import { useState, useEffect, useCallback } from 'react';
import { dataSyncService, SyncResult } from '../services/dataSync';
import { liveDataService } from '../services/liveDataService';

// Generic synchronized data hook
export const useSyncedData = <T>(
  table: string,
  fetchFunction: () => Promise<T[]>,
  dependencies: any[] = []
) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefetching, setIsRefetching] = useState(false);

  const fetchData = useCallback(async (isRefetch = false) => {
    try {
      if (!isRefetch) {
        setLoading(true);
      } else {
        setIsRefetching(true);
      }
      setError(null);

      const result = await fetchFunction();

      // Debug logging for categories
      if (table === 'categories') {
        console.log(`useSyncedData(${table}): Fetched result:`, result);
        if (Array.isArray(result) && result.length > 0) {
          console.log(`useSyncedData(${table}): Sample items:`, result.slice(0, 3).map(item => ({
            id: item.id,
            name: item.name,
            isActive: item.isActive,
            is_active: item.is_active
          })));
        }
      }

      // Ensure result is always an array
      const safeResult = Array.isArray(result) ? result : [];
      setData(safeResult);
    } catch (err: any) {
      console.error(`Error fetching ${table}:`, err);
      setError(err.message || `Failed to fetch ${table}`);

      // Don't clear data on error, keep previous data
      // setData([]);
    } finally {
      setLoading(false);
      setIsRefetching(false);
    }
  }, dependencies);

  useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      if (isMounted) {
        await fetchData(false);
      }
    };

    loadData();

    // Subscribe to real-time updates
    const unsubscribe = dataSyncService.subscribe(table, (change) => {
      console.log(`${table} changed:`, change);

      // Debounce rapid updates
      setTimeout(() => {
        if (isMounted) {
          fetchData(true); // Refetch data when changes occur
        }
      }, 100);
    });

    return () => {
      isMounted = false;
      unsubscribe();
    };
  }, [fetchData, table]);

  const refetch = useCallback(() => {
    fetchData(true);
  }, [fetchData]);

  return { data, loading, error, refetch, isRefetching };
};

// Specific hooks for each data type

export const useSyncedUsers = (userType?: string) => {
  return useSyncedData(
    'users',
    () => userType 
      ? liveDataService.getUsersByType(userType)
      : liveDataService.getAllUsers(),
    [userType]
  );
};

export const useSyncedProducts = (categoryId?: string) => {
  return useSyncedData(
    'products',
    () => categoryId 
      ? liveDataService.getProductsByCategory(categoryId)
      : liveDataService.getAllProducts(),
    [categoryId]
  );
};

export const useSyncedCategories = () => {
  return useSyncedData(
    'categories',
    () => liveDataService.getAllCategories(),
    []
  );
};

export const useSyncedBranches = () => {
  return useSyncedData(
    'branches',
    () => liveDataService.getAllBranches(),
    []
  );
};

export const useSyncedOrders = (status?: string, customerId?: string) => {
  return useSyncedData(
    'orders',
    () => {
      if (customerId) {
        return liveDataService.getOrdersByCustomer(customerId);
      } else if (status) {
        return liveDataService.getOrdersByStatus(status);
      } else {
        return liveDataService.getAllOrders();
      }
    },
    [status, customerId]
  );
};

// CRUD operation hooks with automatic sync

export const useUserOperations = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createUser = useCallback(async (userData: any): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.createUser(userData);
    
    if (!result.success) {
      setError(result.error || 'Failed to create user');
    }
    
    setLoading(false);
    return result;
  }, []);

  const updateUser = useCallback(async (userId: string, updates: any): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.updateUser(userId, updates);
    
    if (!result.success) {
      setError(result.error || 'Failed to update user');
    }
    
    setLoading(false);
    return result;
  }, []);

  const deleteUser = useCallback(async (userId: string): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.deleteUser(userId);
    
    if (!result.success) {
      setError(result.error || 'Failed to delete user');
    }
    
    setLoading(false);
    return result;
  }, []);

  return { createUser, updateUser, deleteUser, loading, error };
};

export const useProductOperations = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createProduct = useCallback(async (productData: any): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.createProduct(productData);
    
    if (!result.success) {
      setError(result.error || 'Failed to create product');
    }
    
    setLoading(false);
    return result;
  }, []);

  const updateProduct = useCallback(async (productId: string, updates: any): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.updateProduct(productId, updates);
    
    if (!result.success) {
      setError(result.error || 'Failed to update product');
    }
    
    setLoading(false);
    return result;
  }, []);

  const deleteProduct = useCallback(async (productId: string): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.deleteProduct(productId);
    
    if (!result.success) {
      setError(result.error || 'Failed to delete product');
    }
    
    setLoading(false);
    return result;
  }, []);

  return { createProduct, updateProduct, deleteProduct, loading, error };
};

export const useCategoryOperations = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createCategory = useCallback(async (categoryData: any): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.createCategory(categoryData);
    
    if (!result.success) {
      setError(result.error || 'Failed to create category');
    }
    
    setLoading(false);
    return result;
  }, []);

  const updateCategory = useCallback(async (categoryId: string, updates: any): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.updateCategory(categoryId, updates);
    
    if (!result.success) {
      setError(result.error || 'Failed to update category');
    }
    
    setLoading(false);
    return result;
  }, []);

  const deleteCategory = useCallback(async (categoryId: string): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.deleteCategory(categoryId);
    
    if (!result.success) {
      setError(result.error || 'Failed to delete category');
    }
    
    setLoading(false);
    return result;
  }, []);

  return { createCategory, updateCategory, deleteCategory, loading, error };
};

export const useBranchOperations = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createBranch = useCallback(async (branchData: any): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.createBranch(branchData);
    
    if (!result.success) {
      setError(result.error || 'Failed to create branch');
    }
    
    setLoading(false);
    return result;
  }, []);

  const updateBranch = useCallback(async (branchId: string, updates: any): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.updateBranch(branchId, updates);
    
    if (!result.success) {
      setError(result.error || 'Failed to update branch');
    }
    
    setLoading(false);
    return result;
  }, []);

  const deleteBranch = useCallback(async (branchId: string): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.deleteBranch(branchId);
    
    if (!result.success) {
      setError(result.error || 'Failed to delete branch');
    }
    
    setLoading(false);
    return result;
  }, []);

  return { createBranch, updateBranch, deleteBranch, loading, error };
};

export const useOrderOperations = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createOrder = useCallback(async (orderData: any): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.createOrder(orderData);
    
    if (!result.success) {
      setError(result.error || 'Failed to create order');
    }
    
    setLoading(false);
    return result;
  }, []);

  const updateOrderStatus = useCallback(async (orderId: string, status: string): Promise<SyncResult> => {
    setLoading(true);
    setError(null);
    
    const result = await dataSyncService.updateOrderStatus(orderId, status);
    
    if (!result.success) {
      setError(result.error || 'Failed to update order');
    }
    
    setLoading(false);
    return result;
  }, []);

  return { createOrder, updateOrderStatus, loading, error };
};

// Initialize real-time sync when hooks are first used
let syncInitialized = false;

export const initializeDataSync = () => {
  if (!syncInitialized) {
    dataSyncService.initializeRealTimeSync();
    syncInitialized = true;
  }
};

export default {
  useSyncedData,
  useSyncedUsers,
  useSyncedProducts,
  useSyncedCategories,
  useSyncedBranches,
  useSyncedOrders,
  useUserOperations,
  useProductOperations,
  useCategoryOperations,
  useBranchOperations,
  useOrderOperations,
  initializeDataSync
};
