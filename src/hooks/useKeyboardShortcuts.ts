
import { useEffect } from 'react';
import { KeyboardShortcut } from '../types/theme';

const shortcuts: KeyboardShortcut[] = [
  {
    id: 'search',
    action: 'global-search',
    key: 'k',
    modifiers: ['ctrl'],
    description: 'Open global search',
    category: 'search'
  },
  {
    id: 'dashboard',
    action: 'go-dashboard',
    key: 'd',
    modifiers: ['ctrl', 'shift'],
    description: 'Go to dashboard',
    category: 'navigation'
  },
  {
    id: 'inventory',
    action: 'go-inventory',
    key: 'i',
    modifiers: ['ctrl', 'shift'],
    description: 'Go to inventory',
    category: 'navigation'
  },
  {
    id: 'orders',
    action: 'go-orders',
    key: 'o',
    modifiers: ['ctrl', 'shift'],
    description: 'Go to orders',
    category: 'navigation'
  },
  {
    id: 'help',
    action: 'show-help',
    key: '?',
    modifiers: ['shift'],
    description: 'Show help',
    category: 'general'
  },
  {
    id: 'new-product',
    action: 'create-product',
    key: 'p',
    modifiers: ['ctrl', 'shift'],
    description: 'Create new product',
    category: 'actions'
  }
];

export const useKeyboardShortcuts = (onShortcut: (action: string) => void) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const matchedShortcut = shortcuts.find(shortcut => {
        const keyMatch = shortcut.key.toLowerCase() === event.key.toLowerCase();
        const ctrlMatch = shortcut.modifiers.includes('ctrl') === (event.ctrlKey || event.metaKey);
        const altMatch = shortcut.modifiers.includes('alt') === event.altKey;
        const shiftMatch = shortcut.modifiers.includes('shift') === event.shiftKey;

        return keyMatch && ctrlMatch && altMatch && shiftMatch;
      });

      if (matchedShortcut) {
        event.preventDefault();
        onShortcut(matchedShortcut.action);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onShortcut]);

  return shortcuts;
};
