
import { useState, useEffect } from 'react';
import { ThemeSettings } from '../types/theme';

const defaultTheme: ThemeSettings = {
  mode: 'light',
  primaryColor: 'teal',
  accentColor: 'orange',
  borderRadius: 'md',
  fontSize: 'base'
};

export const useTheme = () => {
  const [theme, setTheme] = useState<ThemeSettings>(() => {
    const saved = localStorage.getItem('yala-theme');
    return saved ? JSON.parse(saved) : defaultTheme;
  });

  useEffect(() => {
    localStorage.setItem('yala-theme', JSON.stringify(theme));
    
    // Apply theme to document
    const root = document.documentElement;
    
    if (theme.mode === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Apply custom CSS variables for theme
    root.style.setProperty('--primary-color', theme.primaryColor);
    root.style.setProperty('--accent-color', theme.accentColor);
    root.style.setProperty('--border-radius', theme.borderRadius);
    root.style.setProperty('--font-size', theme.fontSize);
  }, [theme]);

  const updateTheme = (updates: Partial<ThemeSettings>) => {
    setTheme(prev => ({ ...prev, ...updates }));
  };

  const resetTheme = () => {
    setTheme(defaultTheme);
  };

  return {
    theme,
    updateTheme,
    resetTheme,
    isDark: theme.mode === 'dark'
  };
};
