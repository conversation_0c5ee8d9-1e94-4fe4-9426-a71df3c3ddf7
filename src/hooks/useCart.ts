import { useState, useEffect, useCallback } from 'react';
import { cartService, CartState, CartItem } from '../services/cartService';

// React hook for cart management
export const useCart = (userType: 'client' | 'reseller' = 'client') => {
  const [cart, setCart] = useState<CartState>(cartService.getCart());
  
  useEffect(() => {
    const unsubscribe = cartService.subscribe(setCart);
    return unsubscribe;
  }, []);
  
  const addItem = useCallback((item: Omit<CartItem, 'quantity' | 'addedAt'>) => {
    return cartService.addItem(item, userType);
  }, [userType]);
  
  const removeItem = useCallback((itemId: number) => {
    return cartService.removeItem(itemId, userType);
  }, [userType]);
  
  const updateQuantity = useCallback((itemId: number, quantity: number) => {
    return cartService.updateQuantity(itemId, quantity, userType);
  }, [userType]);
  
  const clearCart = useCallback(() => {
    return cartService.clearCart();
  }, []);
  
  const saveCart = useCallback((name: string) => {
    return cartService.saveCart(name);
  }, []);
  
  const loadSavedCart = useCallback((cartId: string) => {
    return cartService.loadSavedCart(cartId, userType);
  }, [userType]);
  
  return {
    cart,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    saveCart,
    loadSavedCart,
    analytics: cartService.getCartAnalytics()
  };
};
