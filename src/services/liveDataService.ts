/**
 * Live Data Service for YalaOffice
 * Replaces all mock data with real Supabase database integration
 */

import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';
import { Product, Category } from '../types/inventory';

// Type definitions
type User = Database['public']['Tables']['users']['Row'];
type Product = Database['public']['Tables']['products']['Row'];
type Order = Database['public']['Tables']['orders']['Row'];
type Branch = Database['public']['Tables']['branches']['Row'];
type Category = Database['public']['Tables']['categories']['Row'];
type CustomerProfile = Database['public']['Tables']['customer_profiles']['Row'];

export class LiveDataService {
  private static instance: LiveDataService;

  private constructor() {}

  static getInstance(): LiveDataService {
    if (!LiveDataService.instance) {
      LiveDataService.instance = new LiveDataService();
    }
    return LiveDataService.instance;
  }

  // Helper function to transform user data from database format to frontend format
  private transformUserData(users: any[]): User[] {
    return users.map(user => ({
      ...user,
      userType: user.user_type, // Transform snake_case to camelCase
      fullName: user.full_name,
      isActive: user.is_active,
      isVerified: user.is_verified,
      companyName: user.company_name,
      iceNumber: user.ice_number,
      companyAddress: user.company_address,
      companyPhone: user.company_phone,
      companyCity: user.company_city,
      companyEmail: user.company_email,
      taxId: user.tax_id,
      legalForm: user.legal_form,
      isCompany: user.is_company,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      lastLogin: user.last_login
    }));
  }

  // =============================================
  // USER MANAGEMENT
  // =============================================

  async getAllUsers(): Promise<User[]> {
    console.log('LiveDataService: Fetching all users...');

    try {
      // First, try to get current user to check if they're admin/manager
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      console.log('Current user:', currentUser?.id);

      // Use RPC function to bypass RLS for admin users
      const { data, error } = await supabase.rpc('get_all_users_admin');

      if (error) {
        console.log('RPC failed, trying direct query:', error);
        // Fallback to direct query - fetch all users (hard delete means deleted users don't exist)
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('users')
          .select('*')
          .order('created_at', { ascending: false });

        console.log('LiveDataService: Fallback query result:', { data: fallbackData, error: fallbackError, dataLength: fallbackData?.length });

        if (fallbackError) {
          console.error('Error fetching users:', fallbackError);

          // Let's also check if there are any users at all with a simple count
          const { count, error: countError } = await supabase
            .from('users')
            .select('*', { count: 'exact', head: true });

          console.log('LiveDataService: Users count check:', { count, countError });

          throw fallbackError;
        }

        return this.transformUserData(fallbackData || []);
      }

      console.log('LiveDataService: RPC query result:', { data, error, dataLength: data?.length });

      // If RPC returns empty but no error, let's check if there are users in the table
      if ((data || []).length === 0) {
        const { count, error: countError } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });

        console.log('LiveDataService: Empty RPC result, checking users count:', { count, countError });
      }

      // Transform database format to frontend format
      const transformedData = this.transformUserData(data || []);
      console.log('LiveDataService: Transformed user data:', transformedData);
      return transformedData;
    } catch (err) {
      console.error('Error in getAllUsers:', err);
      throw err;
    }
  }

  async ensureCurrentUserInUsersTable(): Promise<void> {
    try {
      const { data: { user: currentAuthUser } } = await supabase.auth.getUser();

      if (!currentAuthUser) {
        console.log('No authenticated user found');
        return;
      }

      console.log('Checking if current user exists in users table:', currentAuthUser.id);

      // Use a more direct approach to avoid RLS issues
      try {
        // First try to get the user using RPC function
        const { data: allUsers, error: rpcError } = await supabase.rpc('get_all_users_admin');

        if (!rpcError && allUsers) {
          const existingUser = allUsers.find((user: any) => user.id === currentAuthUser.id);

          if (existingUser) {
            console.log('User found via RPC:', existingUser);
            return;
          }
        }
      } catch (rpcErr) {
        console.log('RPC approach failed, trying direct query:', rpcErr);
      }

      // If RPC fails or user not found, try direct query with temporary RLS bypass
      console.log('User not found, attempting to create record...');

      // Extract user metadata
      const metadata = currentAuthUser.user_metadata || {};
      const email = currentAuthUser.email || '';

      // Create user record with proper status fields
      const { data: newUser, error: insertError } = await supabase
        .from('users')
        .insert({
          id: currentAuthUser.id,
          email: email,
          full_name: metadata.full_name || metadata.fullName || email.split('@')[0],
          user_type: metadata.user_type || metadata.userType || 'admin', // Default to admin for now
          phone: metadata.phone || '+212 6 12 34 56 78',
          city: metadata.city || 'Tetouan',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (insertError) {
        console.error('Error creating user record:', insertError);
        // If insert fails due to RLS, the user might already exist
        console.log('Insert failed, user might already exist. This is OK if RLS is blocking the check.');
      } else {
        console.log('User record created successfully:', newUser);
      }
    } catch (err) {
      console.error('Error in ensureCurrentUserInUsersTable:', err);
      // Don't throw the error, just log it
    }
  }

  // New method for admin user status management
  async updateUserStatus(userId: string, updates: { is_active?: boolean; is_verified?: boolean; status?: string }): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('LiveDataService: Updating user status:', { userId, updates });

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (updates.is_active !== undefined) updateData.is_active = updates.is_active;
      if (updates.is_verified !== undefined) updateData.is_verified = updates.is_verified;
      if (updates.status !== undefined) updateData.status = updates.status;

      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating user status:', error);
        return { success: false, error: error.message };
      }

      console.log('LiveDataService: User status updated successfully:', data);
      return { success: true };
    } catch (err) {
      console.error('Error in updateUserStatus:', err);
      return { success: false, error: (err as Error).message };
    }
  }

  async getUsersByType(userType: string): Promise<User[]> {
    try {
      // Use RPC function for admin access
      const { data, error } = await supabase.rpc('get_all_users_admin');

      if (error) {
        console.log('RPC failed for getUsersByType, trying direct query:', error);
        // Fallback to direct query
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('users')
          .select('*')
          .eq('user_type', userType)
          .eq('is_active', true)
          .order('full_name');

        if (fallbackError) {
          console.error('Error fetching users by type:', fallbackError);
          throw fallbackError;
        }

        const filteredData = (fallbackData || []).filter(user => user.user_type === userType && user.is_active);
        return this.transformUserData(filteredData);
      }

      // Filter the results by type and active status
      const filteredData = (data || []).filter(user => user.user_type === userType && user.is_active);
      return this.transformUserData(filteredData);
    } catch (err) {
      console.error('Error in getUsersByType:', err);
      throw err;
    }
  }

  async getUserById(id: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching user by ID:', error);
      return null;
    }

    return data;
  }

  async getUserStatistics(): Promise<any> {
    try {
      console.log('LiveDataService: Fetching user statistics...');

      // First, let's check if current user exists in users table
      const { data: { user: currentAuthUser } } = await supabase.auth.getUser();
      console.log('LiveDataService: Current auth user:', currentAuthUser?.id);

      if (currentAuthUser) {
        const { data: currentUserRecord, error: currentUserError } = await supabase
          .from('users')
          .select('*')
          .eq('id', currentAuthUser.id)
          .single();

        console.log('LiveDataService: Current user in users table:', {
          found: !!currentUserRecord,
          user: currentUserRecord,
          error: currentUserError
        });
      }

      const { data, error } = await supabase.rpc('get_user_statistics');

      if (error) {
        console.log('RPC failed for getUserStatistics, calculating manually:', error);

        // Fallback: calculate statistics manually
        const { data: users, error: usersError } = await supabase
          .from('users')
          .select('user_type, is_active, created_at');

        console.log('LiveDataService: Direct users query result:', { users, usersError, count: users?.length });

        if (usersError) {
          console.error('Error fetching users for statistics:', usersError);
          throw usersError;
        }

        // Since we're doing hard deletes, all users in the database are active
        const allUsers = users || [];

        const stats = {
          total: allUsers.length,
          admins: allUsers.filter(u => u.user_type === 'admin').length,
          managers: allUsers.filter(u => u.user_type === 'manager').length,
          clients: allUsers.filter(u => u.user_type === 'client').length,
          resellers: allUsers.filter(u => u.user_type === 'reseller').length,
          delivery: allUsers.filter(u => u.user_type === 'delivery_person').length,
          active: allUsers.filter(u => u.is_active !== false).length, // Count active users
          inactive: allUsers.filter(u => u.is_active === false).length, // Count inactive users
          recentlyCreated: allUsers.filter(u => {
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return new Date(u.created_at) >= weekAgo;
          }).length
        };

        console.log('LiveDataService: Manual statistics result:', stats);
        return stats;
      }

      console.log('LiveDataService: RPC statistics result:', data);
      return data;
    } catch (err) {
      console.error('Error in getUserStatistics:', err);
      throw err;
    }
  }

  async createUser(userData: Partial<User>): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .insert([userData])
      .select()
      .single();

    if (error) {
      console.error('Error creating user:', error);
      throw error;
    }

    return data;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating user:', error);
      throw error;
    }

    return data;
  }

  async deleteUser(id: string): Promise<boolean> {
    try {
      console.log('LiveDataService: Hard deleting user with cascade handling:', id);

      // Clean up related records that don't have CASCADE constraints
      console.log('LiveDataService: Cleaning up related records...');

      // Delete notifications
      await supabase.from('notifications').delete().eq('user_id', id);

      // Delete wishlists
      await supabase.from('wishlists').delete().eq('customer_id', id);

      // Delete product reviews
      await supabase.from('product_reviews').delete().eq('customer_id', id);

      // Delete customer behavior tracking
      await supabase.from('customer_behavior').delete().eq('customer_id', id);

      // Delete order templates
      await supabase.from('order_templates').delete().eq('customer_id', id);

      console.log('LiveDataService: Related records cleaned up');

      // Now delete the user
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error hard deleting user:', error);
        return false;
      }

      // Delete from auth
      try {
        await supabase.auth.admin.deleteUser(id);
        console.log('LiveDataService: User deleted from auth successfully');
      } catch (authError) {
        console.warn('LiveDataService: Auth deletion failed (non-critical):', authError);
      }

      console.log('LiveDataService: User hard deleted successfully');
      return true;
    } catch (error) {
      console.error('Error in deleteUser:', error);
      return false;
    }
  }

  // =============================================
  // PRODUCT MANAGEMENT
  // =============================================

  async getAllProducts(): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        ),
        branch_inventory (
          branch_id,
          stock,
          min_stock,
          branches (
            name,
            code,
            address
          )
        )
      `)
      .eq('is_active', true)
      .order('title');

    if (error) {
      console.error('Error fetching products:', error);
      throw error;
    }

    // Transform raw Supabase data to Product objects with branch information
    return (data || []).map(productRow => this.convertToProductWithBranch(productRow));
  }

  // Helper function to convert database rows to Product objects
  private convertToProduct(productRow: any): Product {
    return {
      id: productRow.id,
      title: productRow.title,
      description: productRow.description || '',
      category: productRow.categories?.name || 'Uncategorized',
      brand: productRow.brand || '',
      price: Number(productRow.price),
      resellerPrice: Number(productRow.reseller_price || productRow.price),
      image: productRow.featured_image || '/placeholder.svg',
      featuredImage: productRow.featured_image || '/placeholder.svg',
      thumbnailImages: productRow.thumbnail_images || ['/placeholder.svg'],
      rating: Number(productRow.rating || 0),
      stock: productRow.stock || 0,
      minStock: productRow.min_stock || 0,
      isActive: productRow.is_active,
      isNew: productRow.is_new || false,
      sku: productRow.sku,
      weight: productRow.weight ? Number(productRow.weight) : undefined,
      dimensions: productRow.dimensions as Product['dimensions'],
      tags: productRow.tags || [],
      createdAt: productRow.created_at,
      updatedAt: productRow.updated_at,
    };
  }

  // Helper function to convert database rows to Product objects with branch information
  private convertToProductWithBranch(productRow: any): Product {
    const baseProduct = this.convertToProduct(productRow);

    // Add branch information if available
    if (productRow.branch_inventory && productRow.branch_inventory.length > 0) {
      const branchInfo = productRow.branch_inventory[0]; // Get first branch for display
      (baseProduct as any).branchId = branchInfo.branch_id;
      (baseProduct as any).branchName = branchInfo.branches?.name;
      (baseProduct as any).branchCode = branchInfo.branches?.code;
      (baseProduct as any).branchStock = branchInfo.stock;
      (baseProduct as any).branchMinStock = branchInfo.min_stock;
    }

    return baseProduct;
  }

  async getProductsByCategory(categoryId: string): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        ),
        branch_inventory (
          branch_id,
          stock,
          min_stock,
          branches (
            name,
            code,
            address
          )
        )
      `)
      .eq('category_id', categoryId)
      .eq('is_active', true)
      .order('title');

    if (error) {
      console.error('Error fetching products by category:', error);
      throw error;
    }

    // Transform raw Supabase data to Product objects with branch information
    return (data || []).map(productRow => this.convertToProductWithBranch(productRow));
  }

  async getProductById(id: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching product by ID:', error);
      return null;
    }

    return data ? this.convertToProduct(data) : null;
  }

  async searchProducts(query: string): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,sku.ilike.%${query}%`)
      .eq('is_active', true)
      .order('title')
      .limit(50);

    if (error) {
      console.error('Error searching products:', error);
      throw error;
    }

    // Transform raw Supabase data to Product objects
    return (data || []).map(productRow => this.convertToProduct(productRow));
  }

  async createProduct(productData: Partial<Product>): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .insert([productData])
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .single();

    if (error) {
      console.error('Error creating product:', error);
      throw error;
    }

    return data ? this.convertToProduct(data) : null;
  }

  async updateProduct(id: string, updates: Partial<Product>): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select(`
        *,
        categories (
          id,
          name,
          color,
          icon
        )
      `)
      .single();

    if (error) {
      console.error('Error updating product:', error);
      throw error;
    }

    return data ? this.convertToProduct(data) : null;
  }

  async updateProductStock(id: string, newStock: number): Promise<boolean> {
    const { error } = await supabase
      .from('products')
      .update({ 
        stock: newStock, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', id);

    if (error) {
      console.error('Error updating product stock:', error);
      return false;
    }

    return true;
  }

  // =============================================
  // CATEGORY MANAGEMENT
  // =============================================

  async getAllCategories(): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .order('sort_order');

      if (error) {
        console.error('Error fetching categories:', error);
        throw error;
      }

      // Convert database rows to proper Category objects
      return (data || []).map(categoryRow => this.convertToCategory(categoryRow));
    } catch (err) {
      console.error('Error in getAllCategories:', err);
      throw err;
    }
  }

  // Helper function to convert database category row to Category object
  private convertToCategory(categoryRow: any): any {
    return {
      id: categoryRow.id,
      name: categoryRow.name,
      description: categoryRow.description || '',
      isActive: categoryRow.is_active,
      parentId: categoryRow.parent_id || undefined,
      level: categoryRow.level || 0,
      sortOrder: categoryRow.sort_order || 0,
      icon: categoryRow.icon || 'folder',
      color: categoryRow.color || '#6B7280',
      productCount: 0, // Will be calculated separately if needed
      createdAt: categoryRow.created_at,
      updatedAt: categoryRow.updated_at,
      createdBy: categoryRow.created_by || 'system',
      updatedBy: categoryRow.updated_by || 'system'
    };
  }

  async getCategoryById(id: string): Promise<Category | null> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching category by ID:', error);
      return null;
    }

    return data;
  }

  // =============================================
  // BRANCH MANAGEMENT
  // =============================================

  async getAllBranches(): Promise<Branch[]> {
    const { data, error } = await supabase
      .from('branches')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching branches:', error);
      throw error;
    }

    return data || [];
  }

  async getBranchById(id: string): Promise<Branch | null> {
    const { data, error } = await supabase
      .from('branches')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching branch by ID:', error);
      return null;
    }

    return data;
  }

  async getMainBranch(): Promise<Branch | null> {
    const { data, error } = await supabase
      .from('branches')
      .select('*')
      .eq('is_main_branch', true)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching main branch:', error);
      return null;
    }

    return data;
  }

  // =============================================
  // BRANCH STATISTICS & ANALYTICS
  // =============================================

  async getBranchStatistics(): Promise<{
    totalBranches: number;
    activeBranches: number;
    inactiveBranches: number;
    pendingTransfers: number;
    totalInventoryItems: number;
    averagePerformance: number;
  }> {
    try {
      console.log('LiveDataService: Fetching branch statistics...');

      const [
        branchesResult,
        activeBranchesResult,
        transfersResult,
        inventoryResult
      ] = await Promise.all([
        supabase.from('branches').select('id', { count: 'exact' }),
        supabase.from('branches').select('id', { count: 'exact' }).eq('is_active', true),
        supabase.from('stock_transfers').select('id', { count: 'exact' }).eq('status', 'pending'),
        supabase.from('branch_inventory').select('id', { count: 'exact' })
      ]);

      const totalBranches = branchesResult.count || 0;
      const activeBranches = activeBranchesResult.count || 0;
      const inactiveBranches = totalBranches - activeBranches;
      const pendingTransfers = transfersResult.count || 0;
      const totalInventoryItems = inventoryResult.count || 0;

      // Calculate average performance (mock calculation for now)
      const averagePerformance = activeBranches > 0 ? 4.2 : 0;

      const stats = {
        totalBranches,
        activeBranches,
        inactiveBranches,
        pendingTransfers,
        totalInventoryItems,
        averagePerformance
      };

      console.log('LiveDataService: Branch statistics:', stats);
      return stats;
    } catch (error) {
      console.error('Error fetching branch statistics:', error);
      return {
        totalBranches: 0,
        activeBranches: 0,
        inactiveBranches: 0,
        pendingTransfers: 0,
        totalInventoryItems: 0,
        averagePerformance: 0
      };
    }
  }

  async getBranchAnalytics(branchId: string): Promise<{
    totalProducts: number;
    lowStockItems: number;
    outOfStockItems: number;
    totalItems: number;
    branchRevenue: number;
    totalOrders: number;
    customerCount: number;
    averageOrderValue: number;
    inventoryTurnover: number;
  }> {
    try {
      console.log('LiveDataService: Fetching branch analytics for:', branchId);

      const [
        inventoryResult,
        lowStockResult,
        outOfStockResult,
        ordersResult,
        customersResult
      ] = await Promise.all([
        supabase
          .from('branch_inventory')
          .select('stock, min_stock')
          .eq('branch_id', branchId),
        supabase
          .from('branch_inventory')
          .select('id', { count: 'exact' })
          .eq('branch_id', branchId)
          .filter('stock', 'lte', 'min_stock'),
        supabase
          .from('branch_inventory')
          .select('id', { count: 'exact' })
          .eq('branch_id', branchId)
          .eq('stock', 0),
        supabase
          .from('orders')
          .select('total, customer_id')
          .eq('branch_id', branchId)
          .eq('status', 'completed'),
        supabase
          .from('orders')
          .select('customer_id')
          .eq('branch_id', branchId)
          .eq('status', 'completed')
      ]);

      const inventory = inventoryResult.data || [];
      const totalProducts = inventory.length;
      const lowStockItems = lowStockResult.count || 0;
      const outOfStockItems = outOfStockResult.count || 0;
      const totalItems = inventory.reduce((sum, item) => sum + (item.stock || 0), 0);

      const orders = ordersResult.data || [];
      const totalOrders = orders.length;
      const branchRevenue = orders.reduce((sum, order) => sum + (order.total || 0), 0);
      const averageOrderValue = totalOrders > 0 ? branchRevenue / totalOrders : 0;

      // Calculate unique customers
      const uniqueCustomers = new Set(orders.map(order => order.customer_id));
      const customerCount = uniqueCustomers.size;

      // Calculate inventory turnover (simplified calculation)
      const inventoryTurnover = totalProducts > 0 ? (totalOrders / totalProducts) * 4 : 0;

      const analytics = {
        totalProducts,
        lowStockItems,
        outOfStockItems,
        totalItems,
        branchRevenue,
        totalOrders,
        customerCount,
        averageOrderValue,
        inventoryTurnover
      };

      console.log('LiveDataService: Branch analytics:', analytics);
      return analytics;
    } catch (error) {
      console.error('Error fetching branch analytics:', error);
      return {
        totalProducts: 0,
        lowStockItems: 0,
        outOfStockItems: 0,
        totalItems: 0,
        branchRevenue: 0,
        totalOrders: 0,
        customerCount: 0,
        averageOrderValue: 0,
        inventoryTurnover: 0
      };
    }
  }

  async getBranchSalesComparison(dateRange: { start: string; end: string }): Promise<{
    branchId: string;
    branchName: string;
    totalSales: number;
    totalOrders: number;
    averageOrderValue: number;
    growthRate: number;
  }[]> {
    try {
      console.log('LiveDataService: Fetching branch sales comparison for:', dateRange);

      // Get all active branches
      const branchesResult = await supabase
        .from('branches')
        .select('id, name')
        .eq('is_active', true);

      if (!branchesResult.data) return [];

      const salesComparison = await Promise.all(
        branchesResult.data.map(async (branch) => {
          const ordersResult = await supabase
            .from('orders')
            .select('total')
            .eq('branch_id', branch.id)
            .eq('status', 'completed')
            .gte('created_at', dateRange.start)
            .lte('created_at', dateRange.end);

          const orders = ordersResult.data || [];
          const totalOrders = orders.length;
          const totalSales = orders.reduce((sum, order) => sum + (order.total || 0), 0);
          const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

          // Calculate growth rate (simplified - comparing to previous period)
          const previousPeriodStart = new Date(dateRange.start);
          previousPeriodStart.setMonth(previousPeriodStart.getMonth() - 1);
          const previousPeriodEnd = new Date(dateRange.end);
          previousPeriodEnd.setMonth(previousPeriodEnd.getMonth() - 1);

          const previousOrdersResult = await supabase
            .from('orders')
            .select('total')
            .eq('branch_id', branch.id)
            .eq('status', 'completed')
            .gte('created_at', previousPeriodStart.toISOString())
            .lte('created_at', previousPeriodEnd.toISOString());

          const previousOrders = previousOrdersResult.data || [];
          const previousSales = previousOrders.reduce((sum, order) => sum + (order.total || 0), 0);
          const growthRate = previousSales > 0 ? ((totalSales - previousSales) / previousSales) * 100 : 0;

          return {
            branchId: branch.id,
            branchName: branch.name,
            totalSales,
            totalOrders,
            averageOrderValue,
            growthRate
          };
        })
      );

      console.log('LiveDataService: Branch sales comparison:', salesComparison);
      return salesComparison;
    } catch (error) {
      console.error('Error fetching branch sales comparison:', error);
      return [];
    }
  }

  async getStockTransferAnalytics(): Promise<{
    totalTransfers: number;
    pendingTransfers: number;
    completedTransfers: number;
    rejectedTransfers: number;
    totalItemsTransferred: number;
    averageTransferTime: number;
    recentTransfers: any[];
  }> {
    try {
      console.log('LiveDataService: Fetching stock transfer analytics...');

      const [
        allTransfersResult,
        pendingTransfersResult,
        completedTransfersResult,
        rejectedTransfersResult,
        recentTransfersResult
      ] = await Promise.all([
        supabase.from('stock_transfers').select('id', { count: 'exact' }),
        supabase.from('stock_transfers').select('id', { count: 'exact' }).eq('status', 'pending'),
        supabase.from('stock_transfers').select('id', { count: 'exact' }).eq('status', 'completed'),
        supabase.from('stock_transfers').select('id', { count: 'exact' }).eq('status', 'rejected'),
        supabase
          .from('stock_transfers')
          .select(`
            *,
            from_branch:branches!stock_transfers_from_branch_id_fkey(name),
            to_branch:branches!stock_transfers_to_branch_id_fkey(name),
            products(title, sku)
          `)
          .order('created_at', { ascending: false })
          .limit(10)
      ]);

      const totalTransfers = allTransfersResult.count || 0;
      const pendingTransfers = pendingTransfersResult.count || 0;
      const completedTransfers = completedTransfersResult.count || 0;
      const rejectedTransfers = rejectedTransfersResult.count || 0;

      // Calculate total items transferred from completed transfers
      const completedTransfersData = await supabase
        .from('stock_transfers')
        .select('quantity')
        .eq('status', 'completed');

      const totalItemsTransferred = completedTransfersData.data?.reduce(
        (sum, transfer) => sum + (transfer.quantity || 0), 0
      ) || 0;

      // Calculate average transfer time (simplified)
      const averageTransferTime = completedTransfers > 0 ? 2.5 : 0; // days

      const analytics = {
        totalTransfers,
        pendingTransfers,
        completedTransfers,
        rejectedTransfers,
        totalItemsTransferred,
        averageTransferTime,
        recentTransfers: recentTransfersResult.data || []
      };

      console.log('LiveDataService: Stock transfer analytics:', analytics);
      return analytics;
    } catch (error) {
      console.error('Error fetching stock transfer analytics:', error);
      return {
        totalTransfers: 0,
        pendingTransfers: 0,
        completedTransfers: 0,
        rejectedTransfers: 0,
        totalItemsTransferred: 0,
        averageTransferTime: 0,
        recentTransfers: []
      };
    }
  }

  async getBranchInventoryAnalytics(branchId: string): Promise<{
    totalProducts: number;
    totalStock: number;
    lowStockProducts: number;
    outOfStockProducts: number;
    overstockedProducts: number;
    inventoryValue: number;
    topProducts: any[];
    recentMovements: any[];
  }> {
    try {
      console.log('LiveDataService: Fetching branch inventory analytics for:', branchId);

      const inventoryResult = await supabase
        .from('branch_inventory')
        .select(`
          *,
          products(title, sku, price, featured_image)
        `)
        .eq('branch_id', branchId);

      const inventory = inventoryResult.data || [];
      const totalProducts = inventory.length;
      const totalStock = inventory.reduce((sum, item) => sum + (item.stock || 0), 0);
      const lowStockProducts = inventory.filter(item => item.stock <= item.min_stock).length;
      const outOfStockProducts = inventory.filter(item => item.stock === 0).length;
      const overstockedProducts = inventory.filter(item =>
        item.max_stock && item.stock > item.max_stock
      ).length;

      // Calculate inventory value
      const inventoryValue = inventory.reduce((sum, item) => {
        const price = item.products?.price || 0;
        return sum + (price * (item.stock || 0));
      }, 0);

      // Get top products by stock quantity
      const topProducts = inventory
        .sort((a, b) => (b.stock || 0) - (a.stock || 0))
        .slice(0, 5)
        .map(item => ({
          productId: item.product_id,
          productTitle: item.products?.title || 'Unknown Product',
          sku: item.products?.sku || 'N/A',
          stock: item.stock,
          minStock: item.min_stock,
          value: (item.products?.price || 0) * (item.stock || 0)
        }));

      // Get recent stock movements (from transfers)
      const recentMovementsResult = await supabase
        .from('stock_transfers')
        .select(`
          *,
          products(title, sku),
          from_branch:branches!stock_transfers_from_branch_id_fkey(name),
          to_branch:branches!stock_transfers_to_branch_id_fkey(name)
        `)
        .or(`from_branch_id.eq.${branchId},to_branch_id.eq.${branchId}`)
        .order('created_at', { ascending: false })
        .limit(10);

      const analytics = {
        totalProducts,
        totalStock,
        lowStockProducts,
        outOfStockProducts,
        overstockedProducts,
        inventoryValue,
        topProducts,
        recentMovements: recentMovementsResult.data || []
      };

      console.log('LiveDataService: Branch inventory analytics:', analytics);
      return analytics;
    } catch (error) {
      console.error('Error fetching branch inventory analytics:', error);
      return {
        totalProducts: 0,
        totalStock: 0,
        lowStockProducts: 0,
        outOfStockProducts: 0,
        overstockedProducts: 0,
        inventoryValue: 0,
        topProducts: [],
        recentMovements: []
      };
    }
  }

  async getBranchPerformance(period: string = 'monthly'): Promise<{
    branchId: string;
    branchName: string;
    period: string;
    totalSales: number;
    totalOrders: number;
    averageOrderValue: number;
    topProducts: {
      productId: string;
      productName: string;
      unitsSold: number;
      revenue: number;
    }[];
    customerCount: number;
    inventoryTurnover: number;
  }[]> {
    try {
      console.log('LiveDataService: Fetching branch performance for period:', period);

      // Get all active branches
      const branchesResult = await supabase
        .from('branches')
        .select('id, name')
        .eq('is_active', true);

      if (!branchesResult.data) return [];

      const performance = await Promise.all(
        branchesResult.data.map(async (branch) => {
          // Calculate date range based on period
          const endDate = new Date();
          const startDate = new Date();

          switch (period) {
            case 'weekly':
              startDate.setDate(startDate.getDate() - 7);
              break;
            case 'monthly':
              startDate.setMonth(startDate.getMonth() - 1);
              break;
            case 'quarterly':
              startDate.setMonth(startDate.getMonth() - 3);
              break;
            case 'yearly':
              startDate.setFullYear(startDate.getFullYear() - 1);
              break;
            default:
              startDate.setMonth(startDate.getMonth() - 1);
          }

          // Get orders for this branch in the period
          const ordersResult = await supabase
            .from('orders')
            .select('total, customer_id')
            .eq('branch_id', branch.id)
            .eq('status', 'completed')
            .gte('created_at', startDate.toISOString())
            .lte('created_at', endDate.toISOString());

          const orders = ordersResult.data || [];
          const totalOrders = orders.length;
          const totalSales = orders.reduce((sum, order) => sum + (order.total || 0), 0);
          const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

          // Calculate unique customers
          const uniqueCustomers = new Set(orders.map(order => order.customer_id));
          const customerCount = uniqueCustomers.size;

          // Get top products for this branch (simplified - would need order_items table)
          const inventoryResult = await supabase
            .from('branch_inventory')
            .select(`
              *,
              products(id, title, price)
            `)
            .eq('branch_id', branch.id)
            .order('stock', { ascending: false })
            .limit(5);

          const topProducts = (inventoryResult.data || []).map((item, index) => ({
            productId: item.products?.id || 'unknown',
            productName: item.products?.title || 'Unknown Product',
            unitsSold: Math.max(0, 50 - (index * 10)), // Mock units sold based on stock ranking
            revenue: (item.products?.price || 0) * Math.max(0, 50 - (index * 10))
          }));

          // Calculate inventory turnover (simplified)
          const inventoryCount = inventoryResult.data?.length || 0;
          const inventoryTurnover = inventoryCount > 0 ? (totalOrders / inventoryCount) * 4 : 0;

          return {
            branchId: branch.id,
            branchName: branch.name,
            period,
            totalSales,
            totalOrders,
            averageOrderValue,
            topProducts,
            customerCount,
            inventoryTurnover
          };
        })
      );

      console.log('LiveDataService: Branch performance loaded:', performance);
      return performance;
    } catch (error) {
      console.error('Error fetching branch performance:', error);
      return [];
    }
  }

  // =============================================
  // ORDER MANAGEMENT
  // =============================================

  async getAllOrders(): Promise<Order[]> {
    try {
      console.log('LiveDataService: Fetching all orders with proper permissions...');

      // Use the new RPC function that handles permissions properly
      const { data, error } = await supabase
        .rpc('get_orders_for_user');

      if (error) {
        console.error('LiveDataService: Error fetching orders:', error);
        // Fallback to direct query if RPC function doesn't exist yet
        return this.getAllOrdersFallback();
      }

      console.log('LiveDataService: Orders fetched successfully:', data?.length || 0);

      // Transform the data to match the expected Order interface
      const transformedOrders: Order[] = data?.map(order => ({
        id: order.id,
        order_number: order.order_number,
        customer_id: order.customer_id,
        status: order.status,
        payment_status: order.payment_status,
        payment_method: order.payment_method,
        subtotal: order.subtotal,
        delivery_fee: order.delivery_fee,
        discount_amount: order.discount_amount,
        tax_amount: order.tax_amount,
        total: order.total,
        created_at: order.created_at,
        updated_at: order.updated_at,
        created_by: order.created_by,
        branch_id: order.branch_id,
        users: {
          id: order.customer_id,
          full_name: order.customer_name,
          email: order.customer_email,
          phone: null // Not included in RPC function
        },
        branches: order.branch_name ? {
          id: order.branch_id,
          name: order.branch_name
        } : null,
        order_items: [], // Will be populated separately if needed
        item_count: order.item_count
      })) || [];

      return transformedOrders;
    } catch (error) {
      console.error('LiveDataService: Error in getAllOrders:', error);
      // Fallback to direct query
      return this.getAllOrdersFallback();
    }
  }

  // Fallback method for when RPC function is not available
  private async getAllOrdersFallback(): Promise<Order[]> {
    console.log('LiveDataService: Using fallback method for orders...');

    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        users!orders_customer_id_fkey (
          id,
          full_name,
          email,
          phone
        ),
        order_items (
          id,
          quantity,
          unit_price,
          total_price,
          products (
            id,
            title,
            sku,
            featured_image
          )
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching orders (fallback):', error);
      throw error;
    }

    return data || [];
  }

  async getOrdersByStatus(status: string): Promise<Order[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        users!orders_customer_id_fkey (
          id,
          full_name,
          email,
          phone
        ),
        order_items (
          id,
          quantity,
          unit_price,
          total_price,
          products (
            id,
            title,
            sku,
            featured_image
          )
        )
      `)
      .eq('status', status)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching orders by status:', error);
      throw error;
    }

    return data || [];
  }

  async getOrderById(id: string): Promise<Order | null> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        users!orders_customer_id_fkey (
          id,
          full_name,
          email,
          phone,
          city
        ),
        order_items (
          id,
          quantity,
          unit_price,
          total_price,
          products (
            id,
            title,
            sku,
            featured_image,
            brand
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching order by ID:', error);
      return null;
    }

    return data;
  }

  // =============================================
  // CUSTOMER MANAGEMENT
  // =============================================

  async getAllCustomers(): Promise<(User & { customer_profiles?: CustomerProfile })[]> {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        customer_profiles (
          id,
          discount_rate,
          credit_limit,
          total_orders,
          total_spent,
          last_order_date,
          loyalty_points,
          status
        )
      `)
      .in('user_type', ['client', 'reseller'])
      .eq('is_active', true)
      .order('full_name');

    if (error) {
      console.error('Error fetching customers:', error);
      throw error;
    }

    return data || [];
  }

  async getCustomerById(id: string): Promise<(User & { customer_profiles?: CustomerProfile }) | null> {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        customer_profiles (
          id,
          discount_rate,
          credit_limit,
          total_orders,
          total_spent,
          last_order_date,
          loyalty_points,
          status
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching customer by ID:', error);
      return null;
    }

    return data;
  }

  // =============================================
  // ANALYTICS & REPORTING
  // =============================================

  async getDashboardStats(): Promise<{
    totalProducts: number;
    totalOrders: number;
    totalCustomers: number;
    totalRevenue: number;
    lowStockProducts: number;
    pendingOrders: number;
  }> {
    try {
      const [
        productsCount,
        ordersCount,
        customersCount,
        revenueSum,
        lowStockCount,
        pendingOrdersCount
      ] = await Promise.all([
        supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),
        supabase.from('orders').select('id', { count: 'exact' }),
        supabase.from('users').select('id', { count: 'exact' }).in('user_type', ['client', 'reseller']).eq('is_active', true),
        supabase.from('orders').select('total').eq('status', 'completed'),
        supabase.from('products').select('id', { count: 'exact' }).lt('stock', 'min_stock').eq('is_active', true),
        supabase.from('orders').select('id', { count: 'exact' }).eq('status', 'pending')
      ]);

      const totalRevenue = revenueSum.data?.reduce((sum, order) => sum + (order.total || 0), 0) || 0;

      return {
        totalProducts: productsCount.count || 0,
        totalOrders: ordersCount.count || 0,
        totalCustomers: customersCount.count || 0,
        totalRevenue,
        lowStockProducts: lowStockCount.count || 0,
        pendingOrders: pendingOrdersCount.count || 0
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        totalProducts: 0,
        totalOrders: 0,
        totalCustomers: 0,
        totalRevenue: 0,
        lowStockProducts: 0,
        pendingOrders: 0
      };
    }
  }

  // =============================================
  // REAL-TIME SUBSCRIPTIONS
  // =============================================

  subscribeToProducts(callback: (payload: any) => void) {
    return supabase
      .channel('products-changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'products' }, 
        callback
      )
      .subscribe();
  }

  subscribeToOrders(callback: (payload: any) => void) {
    return supabase
      .channel('orders-changes')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'orders' }, 
        callback
      )
      .subscribe();
  }

  subscribeToUsers(callback: (payload: any) => void) {
    return supabase
      .channel('users-changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'users' },
        callback
      )
      .subscribe();
  }

  async createOrder(orderData: {
    customer_id: string;
    status: string;
    total: number;
    items: Array<{
      product_id: string;
      quantity: number;
      price: number;
    }>;
  }) {
    try {
      console.log('Creating order with data:', orderData);

      // Get current user for created_by field
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      console.log('Current user for order creation:', user?.id, user?.email);

      if (userError || !user) {
        console.error('User authentication error:', userError);
        throw new Error('User not authenticated');
      }

      // Generate order number
      const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      console.log('Generated order number:', orderNumber);

      // Prepare order data
      const orderInsertData = {
        order_number: orderNumber,
        customer_id: orderData.customer_id,
        status: orderData.status,
        subtotal: orderData.total,
        total: orderData.total,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('Order insert data:', orderInsertData);

      // Create the order
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert(orderInsertData)
        .select()
        .single();

      if (orderError) {
        console.error('Error creating order:', orderError);
        console.error('Order error details:', {
          code: orderError.code,
          message: orderError.message,
          details: orderError.details,
          hint: orderError.hint
        });

        // Provide more specific error messages
        if (orderError.code === '42501') {
          throw new Error('Permission denied: Please ensure you have the correct permissions to create orders. Contact your administrator.');
        } else if (orderError.message?.includes('row-level security')) {
          throw new Error('Database security policy violation: Please run the database update script or contact your administrator.');
        } else {
          throw new Error(`Order creation failed: ${orderError.message}`);
        }
      }

      console.log('Order created:', order);

      // Create order items with correct field mapping
      const orderItems = orderData.items.map(item => ({
        order_id: order.id,
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.price,  // ✅ Fixed: price → unit_price
        total_price: item.price * item.quantity,  // ✅ Added: total_price calculation
        created_at: new Date().toISOString()
      }));

      console.log('Order items to insert:', orderItems);

      const { data: createdItems, error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems)
        .select();

      if (itemsError) {
        console.error('Error creating order items:', itemsError);
        console.error('Order items error details:', {
          code: itemsError.code,
          message: itemsError.message,
          details: itemsError.details,
          hint: itemsError.hint
        });

        // Provide specific error message for schema issues
        if (itemsError.message?.includes('column') && itemsError.message?.includes('schema')) {
          throw new Error(`Database schema mismatch: ${itemsError.message}. Please check the order_items table structure.`);
        } else {
          throw new Error(`Failed to create order items: ${itemsError.message}`);
        }
      }

      console.log('Order items created successfully:', createdItems);

      // Return the complete order with items
      return {
        ...order,
        order_items: createdItems || orderItems
      };
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  async updateOrderDeliveryAssignment(orderId: string, deliveryPersonId: string) {
    try {
      console.log('Assigning delivery person:', deliveryPersonId, 'to order:', orderId);

      // First, get the delivery person's information
      const { data: deliveryPerson, error: personError } = await supabase
        .from('users')
        .select('full_name')
        .eq('id', deliveryPersonId)
        .single();

      if (personError) {
        console.error('Error fetching delivery person:', personError);
        throw personError;
      }

      // Update the order with delivery assignment information
      const { data, error } = await supabase
        .from('orders')
        .update({
          assigned_delivery_person: deliveryPersonId,
          assigned_delivery_person_id: deliveryPersonId, // Alternative field name for compatibility
          delivery_person_name: deliveryPerson?.full_name || 'Unknown',
          delivery_assigned_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        console.error('Error updating order delivery assignment:', error);
        throw error;
      }

      console.log('Order delivery assignment updated successfully:', {
        orderId: data.id,
        assignedDeliveryPerson: data.assigned_delivery_person,
        deliveryPersonName: data.delivery_person_name,
        assignedAt: data.delivery_assigned_at
      });
      return true;
    } catch (error) {
      console.error('Error updating order delivery assignment:', error);
      throw error;
    }
  }

  async updateOrderStatus(orderId: string, newStatus: string) {
    try {
      console.log('Updating order status:', orderId, 'to', newStatus);

      const { data, error } = await supabase
        .from('orders')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        console.error('Error updating order status:', error);
        throw error;
      }

      console.log('Order status updated:', data);
      return true;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  async deleteOrder(orderId: string): Promise<boolean> {
    try {
      console.log('Deleting order:', orderId);

      // First, delete order items (cascade should handle this, but let's be explicit)
      const { error: itemsError } = await supabase
        .from('order_items')
        .delete()
        .eq('order_id', orderId);

      if (itemsError) {
        console.error('Error deleting order items:', itemsError);
        throw itemsError;
      }

      // Then delete the order
      const { data, error } = await supabase
        .from('orders')
        .delete()
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        console.error('Error deleting order:', error);
        throw error;
      }

      console.log('Order deleted successfully:', data);
      return true;
    } catch (error) {
      console.error('Error deleting order:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const liveDataService = LiveDataService.getInstance();
export default liveDataService;
