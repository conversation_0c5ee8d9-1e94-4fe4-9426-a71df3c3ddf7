
import { Payment, PaymentTransaction, CreditAccount, CreditTransaction, PaymentStatus } from '../types/payment';
import { Order } from '../types/order';
import { generateId } from '../utils/inventoryUtils';

// Mock data storage
let payments: Payment[] = [];
let transactions: PaymentTransaction[] = [];
let creditAccounts: CreditAccount[] = [];
let creditTransactions: CreditTransaction[] = [];

export const processPayment = async (
  orderId: string,
  amount: number,
  method: Payment['method'],
  metadata?: Record<string, any>
): Promise<Payment> => {
  const payment: Payment = {
    id: generateId('PAY'),
    orderId,
    amount,
    currency: 'MAD',
    method,
    status: 'processing',
    createdAt: new Date().toISOString(),
    metadata
  };

  payments.push(payment);

  // Simulate payment processing based on method
  setTimeout(async () => {
    let success = false;
    
    switch (method) {
      case 'cash':
        success = true; // Cash payments are always successful when processed
        break;
      case 'bank_transfer':
        success = Math.random() > 0.05; // 95% success rate for bank transfers
        break;
      case 'check':
        success = Math.random() > 0.1; // 90% success rate for checks
        break;
      default:
        success = Math.random() > 0.1;
    }
    
    payment.status = success ? 'completed' : 'failed';
    payment.processedAt = new Date().toISOString();
    payment.transactionId = success ? generateId('TXN') : undefined;

    // Create transaction record
    const transaction: PaymentTransaction = {
      id: generateId('TRANS'),
      paymentId: payment.id,
      type: 'charge',
      amount: payment.amount,
      status: payment.status,
      gateway: `${method}_gateway`,
      gatewayTransactionId: payment.transactionId,
      createdAt: new Date().toISOString(),
      processedAt: payment.processedAt,
      failureReason: success ? undefined : `${method} payment processing failed`
    };

    transactions.push(transaction);
  }, 2000);

  return payment;
};

export const getPaymentHistory = async (customerId?: string): Promise<Payment[]> => {
  // In a real app, you'd filter by customer
  return payments.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};

export const getPaymentById = async (paymentId: string): Promise<Payment | null> => {
  return payments.find(p => p.id === paymentId) || null;
};

export const refundPayment = async (paymentId: string, amount?: number): Promise<PaymentTransaction> => {
  const payment = await getPaymentById(paymentId);
  if (!payment || payment.status !== 'completed') {
    throw new Error('Payment not found or cannot be refunded');
  }

  const refundAmount = amount || payment.amount;
  const transaction: PaymentTransaction = {
    id: generateId('TRANS'),
    paymentId,
    type: amount && amount < payment.amount ? 'partial_refund' : 'refund',
    amount: refundAmount,
    status: 'completed',
    gateway: 'mock_gateway',
    gatewayTransactionId: generateId('TXN'),
    createdAt: new Date().toISOString(),
    processedAt: new Date().toISOString()
  };

  transactions.push(transaction);
  return transaction;
};

// Credit Management
export const getCreditAccount = async (customerId: string): Promise<CreditAccount | null> => {
  return creditAccounts.find(acc => acc.customerId === customerId) || null;
};

export const createCreditAccount = async (
  customerId: string,
  creditLimit: number
): Promise<CreditAccount> => {
  const account: CreditAccount = {
    id: generateId('CREDIT'),
    customerId,
    balance: 0,
    creditLimit,
    availableCredit: creditLimit,
    currency: 'MAD',
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  creditAccounts.push(account);
  return account;
};

export const updateCreditBalance = async (
  customerId: string,
  amount: number,
  type: 'credit' | 'debit',
  description: string,
  referenceId?: string,
  referenceType?: string
): Promise<CreditTransaction> => {
  const account = await getCreditAccount(customerId);
  if (!account) {
    throw new Error('Credit account not found');
  }

  const transaction: CreditTransaction = {
    id: generateId('CTRANS'),
    creditAccountId: account.id,
    type,
    amount,
    description,
    referenceId,
    referenceType: referenceType as any,
    createdAt: new Date().toISOString(),
    createdBy: 'system'
  };

  // Update account balance
  if (type === 'credit') {
    account.balance += amount;
  } else {
    account.balance -= amount;
  }
  account.availableCredit = account.creditLimit - account.balance;
  account.updatedAt = new Date().toISOString();

  creditTransactions.push(transaction);
  return transaction;
};

export const getCreditTransactions = async (customerId: string): Promise<CreditTransaction[]> => {
  const account = await getCreditAccount(customerId);
  if (!account) return [];

  return creditTransactions
    .filter(t => t.creditAccountId === account.id)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};
