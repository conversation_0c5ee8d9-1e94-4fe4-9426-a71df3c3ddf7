
import { PromoCode, PromoCodeUsage } from '../types/promoCode';

// Mock data for promo codes
let promoCodes: PromoCode[] = [
  {
    id: 'PROMO-001',
    code: 'WELCOME10',
    description: '10% off for new customers',
    type: 'percentage',
    value: 10,
    minOrderAmount: 100,
    maxDiscount: 50,
    usageLimit: 100,
    usedCount: 25,
    isActive: true,
    validFrom: '2024-06-01T00:00:00Z',
    validUntil: '2024-12-31T23:59:59Z',
    createdBy: 'admin',
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  },
  {
    id: 'PROMO-002',
    code: 'FREESHIP',
    description: 'Free shipping on orders over 200 Dh',
    type: 'fixed',
    value: 30,
    minOrderAmount: 200,
    usageLimit: 500,
    usedCount: 150,
    isActive: true,
    validFrom: '2024-06-01T00:00:00Z',
    validUntil: '2024-12-31T23:59:59Z',
    createdBy: 'admin',
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  }
];

let promoCodeUsages: PromoCodeUsage[] = [];

export const getPromoCodes = async (): Promise<PromoCode[]> => {
  return promoCodes;
};

export const getPromoCodeById = async (id: string): Promise<PromoCode | null> => {
  return promoCodes.find(promo => promo.id === id) || null;
};

export const validatePromoCode = async (code: string, orderAmount: number): Promise<{
  isValid: boolean;
  promoCode?: PromoCode;
  error?: string;
  discountAmount?: number;
}> => {
  const promoCode = promoCodes.find(p => p.code.toUpperCase() === code.toUpperCase());
  
  if (!promoCode) {
    return { isValid: false, error: 'Promo code not found' };
  }
  
  if (!promoCode.isActive) {
    return { isValid: false, error: 'Promo code is not active' };
  }
  
  const now = new Date();
  const validFrom = new Date(promoCode.validFrom);
  const validUntil = new Date(promoCode.validUntil);
  
  if (now < validFrom || now > validUntil) {
    return { isValid: false, error: 'Promo code has expired' };
  }
  
  if (promoCode.usageLimit && promoCode.usedCount >= promoCode.usageLimit) {
    return { isValid: false, error: 'Promo code usage limit reached' };
  }
  
  if (promoCode.minOrderAmount && orderAmount < promoCode.minOrderAmount) {
    return { isValid: false, error: `Minimum order amount is ${promoCode.minOrderAmount} Dh` };
  }
  
  let discountAmount = 0;
  if (promoCode.type === 'percentage') {
    discountAmount = (orderAmount * promoCode.value) / 100;
    if (promoCode.maxDiscount && discountAmount > promoCode.maxDiscount) {
      discountAmount = promoCode.maxDiscount;
    }
  } else {
    discountAmount = promoCode.value;
  }
  
  return { isValid: true, promoCode, discountAmount };
};

export const createPromoCode = async (promoData: Omit<PromoCode, 'id' | 'createdAt' | 'updatedAt' | 'usedCount'>): Promise<PromoCode> => {
  const newPromo: PromoCode = {
    ...promoData,
    id: `PROMO-${Date.now().toString(36).toUpperCase()}`,
    usedCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  promoCodes.push(newPromo);
  console.log('New promo code created:', newPromo);
  return newPromo;
};

export const updatePromoCode = async (id: string, updates: Partial<PromoCode>): Promise<PromoCode | null> => {
  const index = promoCodes.findIndex(p => p.id === id);
  if (index === -1) return null;
  
  promoCodes[index] = {
    ...promoCodes[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };
  
  console.log('Promo code updated:', promoCodes[index]);
  return promoCodes[index];
};

export const deletePromoCode = async (id: string): Promise<boolean> => {
  const index = promoCodes.findIndex(p => p.id === id);
  if (index === -1) return false;
  
  promoCodes.splice(index, 1);
  console.log('Promo code deleted:', id);
  return true;
};

export const usePromoCode = async (promoCodeId: string, orderId: string, customerId: string, discountAmount: number): Promise<PromoCodeUsage> => {
  // Increment usage count
  const promoIndex = promoCodes.findIndex(p => p.id === promoCodeId);
  if (promoIndex !== -1) {
    promoCodes[promoIndex].usedCount += 1;
    promoCodes[promoIndex].updatedAt = new Date().toISOString();
  }
  
  // Record usage
  const usage: PromoCodeUsage = {
    id: `USAGE-${Date.now().toString(36).toUpperCase()}`,
    promoCodeId,
    orderId,
    customerId,
    discountAmount,
    usedAt: new Date().toISOString()
  };
  
  promoCodeUsages.push(usage);
  console.log('Promo code used:', usage);
  return usage;
};
