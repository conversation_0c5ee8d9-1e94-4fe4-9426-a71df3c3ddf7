import { realTimeService, syncUserData } from './realTimeService';

export interface CartItem {
  id: number;
  title: string;
  category: string;
  price: number;
  resellerPrice: number;
  image: string;
  featuredImage?: string;
  rating: number;
  inStock: boolean;
  stock: number;
  quantity: number;
  addedAt: string;
}

export interface SavedCart {
  id: string;
  name: string;
  items: CartItem[];
  createdAt: string;
  updatedAt: string;
  totalItems: number;
  totalPrice: number;
}

export interface CartState {
  items: CartItem[];
  savedCarts: SavedCart[];
  totalItems: number;
  totalPrice: number;
  lastUpdated: string;
}

class CartService {
  private storageKey = 'yala-office-cart';
  private savedCartsKey = 'yala-office-saved-carts';
  private listeners: Set<(cart: CartState) => void> = new Set();

  constructor() {
    // Subscribe to real-time cart updates
    realTimeService.subscribe('cart-updated', this.handleCartUpdate.bind(this));
    
    // Load cart from localStorage on initialization
    this.loadCartFromStorage();
  }

  private handleCartUpdate = (event: any) => {
    // Handle real-time cart updates from other devices/sessions
    if (event.data.userId && event.data.cart) {
      this.updateCartState(event.data.cart);
    }
  };

  private loadCartFromStorage(): CartState {
    try {
      const stored = localStorage.getItem(this.storageKey);
      const savedCarts = localStorage.getItem(this.savedCartsKey);
      
      const cart = stored ? JSON.parse(stored) : { items: [], totalItems: 0, totalPrice: 0, lastUpdated: new Date().toISOString() };
      const parsedSavedCarts = savedCarts ? JSON.parse(savedCarts) : [];
      
      return {
        ...cart,
        savedCarts: parsedSavedCarts
      };
    } catch (error) {
      console.error('Error loading cart from storage:', error);
      return {
        items: [],
        savedCarts: [],
        totalItems: 0,
        totalPrice: 0,
        lastUpdated: new Date().toISOString()
      };
    }
  }

  private saveCartToStorage(cart: CartState) {
    try {
      const { savedCarts, ...cartData } = cart;
      localStorage.setItem(this.storageKey, JSON.stringify(cartData));
      localStorage.setItem(this.savedCartsKey, JSON.stringify(savedCarts));
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  }

  private updateCartState(cart: CartState) {
    this.saveCartToStorage(cart);
    this.notifyListeners(cart);
    
    // Sync with real-time service
    realTimeService.emit('cart-updated', {
      cart,
      timestamp: new Date().toISOString()
    });
  }

  private notifyListeners(cart: CartState) {
    this.listeners.forEach(listener => listener(cart));
  }

  private calculateTotals(items: CartItem[], userType: 'client' | 'reseller' = 'client') {
    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = items.reduce((sum, item) => {
      const price = userType === 'reseller' ? item.resellerPrice : item.price;
      return sum + (price * item.quantity);
    }, 0);
    
    return { totalItems, totalPrice };
  }

  // Public methods
  getCart(): CartState {
    return this.loadCartFromStorage();
  }

  addItem(item: Omit<CartItem, 'quantity' | 'addedAt'>, userType: 'client' | 'reseller' = 'client'): CartState {
    const cart = this.loadCartFromStorage();
    const existingItemIndex = cart.items.findIndex(cartItem => cartItem.id === item.id);
    
    if (existingItemIndex >= 0) {
      // Update quantity of existing item
      cart.items[existingItemIndex].quantity += 1;
    } else {
      // Add new item
      const newItem: CartItem = {
        ...item,
        quantity: 1,
        addedAt: new Date().toISOString()
      };
      cart.items.push(newItem);
    }
    
    const totals = this.calculateTotals(cart.items, userType);
    const updatedCart = {
      ...cart,
      ...totals,
      lastUpdated: new Date().toISOString()
    };
    
    this.updateCartState(updatedCart);
    return updatedCart;
  }

  removeItem(itemId: number, userType: 'client' | 'reseller' = 'client'): CartState {
    const cart = this.loadCartFromStorage();
    cart.items = cart.items.filter(item => item.id !== itemId);
    
    const totals = this.calculateTotals(cart.items, userType);
    const updatedCart = {
      ...cart,
      ...totals,
      lastUpdated: new Date().toISOString()
    };
    
    this.updateCartState(updatedCart);
    return updatedCart;
  }

  updateQuantity(itemId: number, quantity: number, userType: 'client' | 'reseller' = 'client'): CartState {
    const cart = this.loadCartFromStorage();
    const itemIndex = cart.items.findIndex(item => item.id === itemId);
    
    if (itemIndex >= 0) {
      if (quantity <= 0) {
        cart.items.splice(itemIndex, 1);
      } else {
        cart.items[itemIndex].quantity = quantity;
      }
    }
    
    const totals = this.calculateTotals(cart.items, userType);
    const updatedCart = {
      ...cart,
      ...totals,
      lastUpdated: new Date().toISOString()
    };
    
    this.updateCartState(updatedCart);
    return updatedCart;
  }

  clearCart(): CartState {
    const cart = this.loadCartFromStorage();
    const clearedCart = {
      ...cart,
      items: [],
      totalItems: 0,
      totalPrice: 0,
      lastUpdated: new Date().toISOString()
    };
    
    this.updateCartState(clearedCart);
    return clearedCart;
  }

  saveCart(name: string): SavedCart {
    const cart = this.loadCartFromStorage();
    const savedCart: SavedCart = {
      id: `cart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      items: [...cart.items],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      totalItems: cart.totalItems,
      totalPrice: cart.totalPrice
    };
    
    cart.savedCarts.push(savedCart);
    this.updateCartState(cart);
    
    return savedCart;
  }

  loadSavedCart(cartId: string, userType: 'client' | 'reseller' = 'client'): CartState {
    const cart = this.loadCartFromStorage();
    const savedCart = cart.savedCarts.find(sc => sc.id === cartId);
    
    if (savedCart) {
      const totals = this.calculateTotals(savedCart.items, userType);
      const updatedCart = {
        ...cart,
        items: [...savedCart.items],
        ...totals,
        lastUpdated: new Date().toISOString()
      };
      
      this.updateCartState(updatedCart);
      return updatedCart;
    }
    
    return cart;
  }

  deleteSavedCart(cartId: string): CartState {
    const cart = this.loadCartFromStorage();
    cart.savedCarts = cart.savedCarts.filter(sc => sc.id !== cartId);
    
    this.updateCartState(cart);
    return cart;
  }

  getSavedCarts(): SavedCart[] {
    const cart = this.loadCartFromStorage();
    return cart.savedCarts;
  }

  // Real-time synchronization
  syncCart(userId: string, userType: string) {
    const cart = this.getCart();
    syncUserData(userId, { cart, userType });
  }

  // Event listeners
  subscribe(listener: (cart: CartState) => void): () => void {
    this.listeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  // Cart analytics
  getCartAnalytics(): {
    totalValue: number;
    averageItemPrice: number;
    mostExpensiveItem: CartItem | null;
    oldestItem: CartItem | null;
    categoryBreakdown: Record<string, number>;
  } {
    const cart = this.getCart();
    
    if (cart.items.length === 0) {
      return {
        totalValue: 0,
        averageItemPrice: 0,
        mostExpensiveItem: null,
        oldestItem: null,
        categoryBreakdown: {}
      };
    }
    
    const totalValue = cart.totalPrice;
    const averageItemPrice = totalValue / cart.totalItems;
    
    const mostExpensiveItem = cart.items.reduce((max, item) => 
      item.price > max.price ? item : max
    );
    
    const oldestItem = cart.items.reduce((oldest, item) => 
      new Date(item.addedAt) < new Date(oldest.addedAt) ? item : oldest
    );
    
    const categoryBreakdown = cart.items.reduce((breakdown, item) => {
      breakdown[item.category] = (breakdown[item.category] || 0) + item.quantity;
      return breakdown;
    }, {} as Record<string, number>);
    
    return {
      totalValue,
      averageItemPrice,
      mostExpensiveItem,
      oldestItem,
      categoryBreakdown
    };
  }
}

// Create singleton instance
export const cartService = new CartService();

// React hook for cart management - moved to separate file to avoid circular dependencies

export default cartService;
