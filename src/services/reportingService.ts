/**
 * Advanced Reporting Service for YalaOffice
 * Provides custom report generation, data export, and automated reporting
 */

import { supabase } from '../integrations/supabase/client';

// Report interfaces
export interface ReportConfig {
  id: string;
  name: string;
  description: string;
  type: 'sales' | 'inventory' | 'customers' | 'financial' | 'custom';
  parameters: ReportParameter[];
  schedule?: ReportSchedule;
  format: 'pdf' | 'excel' | 'csv' | 'json';
  createdBy: string;
  createdAt: string;
  isActive: boolean;
}

export interface ReportParameter {
  name: string;
  type: 'date' | 'dateRange' | 'select' | 'multiSelect' | 'number' | 'text';
  label: string;
  required: boolean;
  defaultValue?: any;
  options?: { value: string; label: string }[];
}

export interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  time: string; // HH:MM format
  dayOfWeek?: number; // 0-6 for weekly
  dayOfMonth?: number; // 1-31 for monthly
  recipients: string[];
  isActive: boolean;
}

export interface ReportData {
  reportId: string;
  generatedAt: string;
  parameters: { [key: string]: any };
  data: any[];
  summary: ReportSummary;
  charts?: ChartData[];
}

export interface ReportSummary {
  totalRecords: number;
  totalValue?: number;
  averageValue?: number;
  keyMetrics: { [key: string]: number | string };
}

export interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'area';
  title: string;
  data: any[];
  xAxis?: string;
  yAxis?: string;
}

// Advanced Reporting Service
export class AdvancedReportingService {
  
  // Generate sales report
  static async generateSalesReport(parameters: {
    dateFrom: string;
    dateTo: string;
    branchId?: string;
    customerId?: string;
    productId?: string;
  }): Promise<ReportData> {
    try {
      let query = supabase
        .from('orders')
        .select(`
          id,
          order_number,
          total_amount,
          status,
          created_at,
          customer_id,
          users!customer_id (
            full_name,
            email
          ),
          order_items (
            quantity,
            unit_price,
            products (
              name,
              sku
            )
          )
        `)
        .gte('created_at', parameters.dateFrom)
        .lte('created_at', parameters.dateTo)
        .eq('status', 'completed');

      if (parameters.customerId) {
        query = query.eq('customer_id', parameters.customerId);
      }

      const { data: orders, error } = await query;

      if (error) throw error;

      // Calculate summary
      const totalRecords = orders?.length || 0;
      const totalValue = orders?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0;
      const averageValue = totalRecords > 0 ? totalValue / totalRecords : 0;

      // Generate chart data
      const dailySales = this.groupOrdersByDate(orders || []);
      const topProducts = this.getTopProductsFromOrders(orders || []);

      const charts: ChartData[] = [
        {
          type: 'line',
          title: 'Daily Sales Trend',
          data: dailySales,
          xAxis: 'date',
          yAxis: 'sales'
        },
        {
          type: 'bar',
          title: 'Top Products',
          data: topProducts.slice(0, 10),
          xAxis: 'product',
          yAxis: 'revenue'
        }
      ];

      return {
        reportId: 'sales-' + Date.now(),
        generatedAt: new Date().toISOString(),
        parameters,
        data: orders || [],
        summary: {
          totalRecords,
          totalValue,
          averageValue,
          keyMetrics: {
            'Total Revenue': `${totalValue.toFixed(2)} MAD`,
            'Average Order Value': `${averageValue.toFixed(2)} MAD`,
            'Total Orders': totalRecords.toString()
          }
        },
        charts
      };
    } catch (error) {
      console.error('Error generating sales report:', error);
      throw error;
    }
  }

  // Generate inventory report
  static async generateInventoryReport(parameters: {
    categoryId?: string;
    lowStockOnly?: boolean;
    includeInactive?: boolean;
  }): Promise<ReportData> {
    try {
      let query = supabase
        .from('products')
        .select(`
          id,
          name,
          sku,
          price,
          cost,
          stock_quantity,
          reorder_point,
          is_active,
          categories (
            name
          )
        `);

      if (parameters.categoryId) {
        query = query.eq('category_id', parameters.categoryId);
      }

      if (!parameters.includeInactive) {
        query = query.eq('is_active', true);
      }

      const { data: products, error } = await query;

      if (error) throw error;

      let filteredProducts = products || [];

      if (parameters.lowStockOnly) {
        filteredProducts = filteredProducts.filter(p => 
          (p.stock_quantity || 0) <= (p.reorder_point || 10)
        );
      }

      // Calculate summary
      const totalRecords = filteredProducts.length;
      const totalValue = filteredProducts.reduce((sum, product) => 
        sum + (Number(product.price) * (product.stock_quantity || 0)), 0
      );
      const lowStockItems = filteredProducts.filter(p => 
        (p.stock_quantity || 0) <= (p.reorder_point || 10)
      ).length;
      const outOfStockItems = filteredProducts.filter(p => 
        (p.stock_quantity || 0) === 0
      ).length;

      // Generate chart data
      const categoryDistribution = this.getCategoryDistribution(filteredProducts);
      const stockLevels = this.getStockLevelDistribution(filteredProducts);

      const charts: ChartData[] = [
        {
          type: 'pie',
          title: 'Products by Category',
          data: categoryDistribution
        },
        {
          type: 'bar',
          title: 'Stock Level Distribution',
          data: stockLevels,
          xAxis: 'level',
          yAxis: 'count'
        }
      ];

      return {
        reportId: 'inventory-' + Date.now(),
        generatedAt: new Date().toISOString(),
        parameters,
        data: filteredProducts,
        summary: {
          totalRecords,
          totalValue,
          keyMetrics: {
            'Total Products': totalRecords.toString(),
            'Inventory Value': `${totalValue.toFixed(2)} MAD`,
            'Low Stock Items': lowStockItems.toString(),
            'Out of Stock': outOfStockItems.toString()
          }
        },
        charts
      };
    } catch (error) {
      console.error('Error generating inventory report:', error);
      throw error;
    }
  }

  // Generate customer report
  static async generateCustomerReport(parameters: {
    userType?: 'client' | 'reseller' | 'all';
    registrationDateFrom?: string;
    registrationDateTo?: string;
    minTotalSpent?: number;
  }): Promise<ReportData> {
    try {
      let query = supabase
        .from('users')
        .select(`
          id,
          full_name,
          email,
          user_type,
          city,
          created_at,
          customer_profiles (
            total_orders,
            total_spent,
            last_order_date,
            status
          )
        `)
        .in('user_type', ['client', 'reseller']);

      if (parameters.userType && parameters.userType !== 'all') {
        query = query.eq('user_type', parameters.userType);
      }

      if (parameters.registrationDateFrom) {
        query = query.gte('created_at', parameters.registrationDateFrom);
      }

      if (parameters.registrationDateTo) {
        query = query.lte('created_at', parameters.registrationDateTo);
      }

      const { data: customers, error } = await query;

      if (error) throw error;

      let filteredCustomers = customers || [];

      if (parameters.minTotalSpent) {
        filteredCustomers = filteredCustomers.filter(c => 
          Number(c.customer_profiles?.[0]?.total_spent || 0) >= parameters.minTotalSpent!
        );
      }

      // Calculate summary
      const totalRecords = filteredCustomers.length;
      const totalValue = filteredCustomers.reduce((sum, customer) => 
        sum + Number(customer.customer_profiles?.[0]?.total_spent || 0), 0
      );
      const averageValue = totalRecords > 0 ? totalValue / totalRecords : 0;

      // Generate chart data
      const customersByType = this.getCustomersByType(filteredCustomers);
      const customersByCity = this.getCustomersByCity(filteredCustomers);

      const charts: ChartData[] = [
        {
          type: 'pie',
          title: 'Customers by Type',
          data: customersByType
        },
        {
          type: 'bar',
          title: 'Customers by City',
          data: customersByCity.slice(0, 10),
          xAxis: 'city',
          yAxis: 'count'
        }
      ];

      return {
        reportId: 'customers-' + Date.now(),
        generatedAt: new Date().toISOString(),
        parameters,
        data: filteredCustomers,
        summary: {
          totalRecords,
          totalValue,
          averageValue,
          keyMetrics: {
            'Total Customers': totalRecords.toString(),
            'Total Revenue': `${totalValue.toFixed(2)} MAD`,
            'Average Customer Value': `${averageValue.toFixed(2)} MAD`,
            'Active Customers': filteredCustomers.filter(c => 
              c.customer_profiles?.[0]?.status === 'active'
            ).length.toString()
          }
        },
        charts
      };
    } catch (error) {
      console.error('Error generating customer report:', error);
      throw error;
    }
  }

  // Helper methods for data processing
  private static groupOrdersByDate(orders: any[]): any[] {
    const grouped: { [key: string]: { date: string; sales: number; orders: number } } = {};
    
    orders.forEach(order => {
      const date = order.created_at.split('T')[0];
      if (!grouped[date]) {
        grouped[date] = { date, sales: 0, orders: 0 };
      }
      grouped[date].sales += Number(order.total_amount);
      grouped[date].orders += 1;
    });

    return Object.values(grouped).sort((a, b) => a.date.localeCompare(b.date));
  }

  private static getTopProductsFromOrders(orders: any[]): any[] {
    const productSales: { [key: string]: { product: string; revenue: number; quantity: number } } = {};
    
    orders.forEach(order => {
      order.order_items?.forEach((item: any) => {
        const productName = item.products?.name || 'Unknown Product';
        if (!productSales[productName]) {
          productSales[productName] = { product: productName, revenue: 0, quantity: 0 };
        }
        productSales[productName].revenue += item.quantity * Number(item.unit_price);
        productSales[productName].quantity += item.quantity;
      });
    });

    return Object.values(productSales).sort((a, b) => b.revenue - a.revenue);
  }

  private static getCategoryDistribution(products: any[]): any[] {
    const distribution: { [key: string]: number } = {};
    
    products.forEach(product => {
      const category = product.categories?.name || 'Uncategorized';
      distribution[category] = (distribution[category] || 0) + 1;
    });

    return Object.entries(distribution).map(([name, value]) => ({ name, value }));
  }

  private static getStockLevelDistribution(products: any[]): any[] {
    const levels = {
      'Out of Stock': 0,
      'Low Stock': 0,
      'Normal Stock': 0,
      'High Stock': 0
    };

    products.forEach(product => {
      const stock = product.stock_quantity || 0;
      const reorderPoint = product.reorder_point || 10;
      
      if (stock === 0) levels['Out of Stock']++;
      else if (stock <= reorderPoint) levels['Low Stock']++;
      else if (stock <= reorderPoint * 3) levels['Normal Stock']++;
      else levels['High Stock']++;
    });

    return Object.entries(levels).map(([level, count]) => ({ level, count }));
  }

  private static getCustomersByType(customers: any[]): any[] {
    const types: { [key: string]: number } = {};
    
    customers.forEach(customer => {
      const type = customer.user_type === 'client' ? 'Clients' : 'Resellers';
      types[type] = (types[type] || 0) + 1;
    });

    return Object.entries(types).map(([name, value]) => ({ name, value }));
  }

  private static getCustomersByCity(customers: any[]): any[] {
    const cities: { [key: string]: number } = {};
    
    customers.forEach(customer => {
      const city = customer.city || 'Unknown';
      cities[city] = (cities[city] || 0) + 1;
    });

    return Object.entries(cities)
      .map(([city, count]) => ({ city, count }))
      .sort((a, b) => b.count - a.count);
  }

  // Export report data to different formats
  static async exportReport(reportData: ReportData, format: 'csv' | 'json'): Promise<string> {
    try {
      if (format === 'json') {
        return JSON.stringify(reportData, null, 2);
      }
      
      if (format === 'csv') {
        return this.convertToCSV(reportData.data);
      }
      
      throw new Error(`Unsupported export format: ${format}`);
    } catch (error) {
      console.error('Error exporting report:', error);
      throw error;
    }
  }

  private static convertToCSV(data: any[]): string {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
        }).join(',')
      )
    ].join('\n');
    
    return csvContent;
  }
}

export default AdvancedReportingService;
