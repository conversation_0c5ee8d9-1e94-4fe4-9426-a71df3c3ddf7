import { Branch, BranchInventory, StockTransfer, BranchPerformance } from '../types/branch';
import { Branch as ManagementBranch } from '../types/management';
import { Product } from '../types/inventory';
import { generateId } from '../utils/inventoryUtils';
import { realTimeService } from './realTimeService';
import { supabase } from '../integrations/supabase/client';
import { Database } from '../integrations/supabase/types';

// Type aliases for better readability
type BranchRow = Database['public']['Tables']['branches']['Row'];
type BranchInsert = Database['public']['Tables']['branches']['Insert'];
type BranchUpdate = Database['public']['Tables']['branches']['Update'];
type BranchInventoryRow = Database['public']['Tables']['branch_inventory']['Row'];
type BranchInventoryInsert = Database['public']['Tables']['branch_inventory']['Insert'];
type BranchInventoryUpdate = Database['public']['Tables']['branch_inventory']['Update'];
type StockTransferRow = Database['public']['Tables']['stock_transfers']['Row'];
type StockTransferInsert = Database['public']['Tables']['stock_transfers']['Insert'];

// Helper functions to convert database rows to our types
const convertToBranch = (branchRow: BranchRow): Branch => {
  const address = branchRow.address as any;
  const contact = branchRow.contact as any;
  const operatingHours = branchRow.operating_hours as any;

  return {
    id: branchRow.id,
    name: branchRow.name,
    code: branchRow.code,
    address: {
      street: address?.street || '',
      city: address?.city || '',
      state: address?.state || '',
      zipCode: address?.postal_code || '',
      country: address?.country || 'Morocco'
    },
    phone: contact?.phone || '',
    email: contact?.email || '',
    managerId: 'system', // Would need to be tracked separately
    managerName: contact?.manager || 'Unknown Manager',
    isActive: branchRow.is_active,
    operatingHours: {
      open: operatingHours?.monday?.open || '08:00',
      close: operatingHours?.monday?.close || '20:00',
      timezone: 'Africa/Casablanca'
    },
    createdAt: branchRow.created_at,
    updatedAt: branchRow.updated_at
  };
};

const convertToBranchInventory = (inventoryRow: BranchInventoryRow): BranchInventory => {
  return {
    branchId: inventoryRow.branch_id,
    productId: inventoryRow.product_id,
    stock: inventoryRow.stock,
    minStock: inventoryRow.min_stock,
    maxStock: inventoryRow.max_stock || 1000,
    reservedStock: inventoryRow.reserved_stock,
    lastRestocked: inventoryRow.last_restocked || new Date().toISOString()
  };
};

const convertToStockTransfer = (transferRow: StockTransferRow): StockTransfer => {
  return {
    id: transferRow.id,
    fromBranchId: transferRow.from_branch_id,
    toBranchId: transferRow.to_branch_id,
    productId: transferRow.product_id,
    quantity: transferRow.quantity,
    status: transferRow.status as StockTransfer['status'],
    requestedBy: transferRow.requested_by || 'system',
    approvedBy: transferRow.approved_by || undefined,
    notes: transferRow.notes || undefined,
    requestedAt: transferRow.requested_at,
    completedAt: transferRow.completed_at || undefined
  };
};
// Enhanced Branch Management with database integration
export const getBranches = async (activeOnly: boolean = true): Promise<Branch[]> => {
  try {
    let query = supabase
      .from('branches')
      .select('*')
      .order('created_at', { ascending: false });

    if (activeOnly) {
      query = query.eq('is_active', true);
    }

    const { data: branches, error } = await query;

    if (error) {
      console.error('Error fetching branches:', error);
      throw error;
    }

    return branches?.map(convertToBranch) || [];
  } catch (error) {
    console.error('Error in getBranches:', error);
    return [];
  }
};

export const getBranchById = async (id: string): Promise<Branch | null> => {
  try {
    const { data: branch, error } = await supabase
      .from('branches')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching branch:', error);
      return null;
    }

    return branch ? convertToBranch(branch) : null;
  } catch (error) {
    console.error('Error in getBranchById:', error);
    return null;
  }
};

export const getBranchInventory = async (branchId: string): Promise<BranchInventory[]> => {
  try {
    const { data: inventory, error } = await supabase
      .from('branch_inventory')
      .select('*')
      .eq('branch_id', branchId);

    if (error) {
      console.error('Error fetching branch inventory:', error);
      throw error;
    }

    return inventory?.map(convertToBranchInventory) || [];
  } catch (error) {
    console.error('Error in getBranchInventory:', error);
    return [];
  }
};
export const getProductAvailabilityByBranch = async (productId: string): Promise<BranchInventory[]> => {
  try {
    const { data: inventory, error } = await supabase
      .from('branch_inventory')
      .select('*')
      .eq('product_id', productId);

    if (error) {
      console.error('Error fetching product availability:', error);
      throw error;
    }

    return inventory?.map(convertToBranchInventory) || [];
  } catch (error) {
    console.error('Error in getProductAvailabilityByBranch:', error);
    return [];
  }
};

export const updateBranchStock = async (
  branchId: string,
  productId: string,
  newStock: number
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('branch_inventory')
      .update({ stock: newStock })
      .eq('branch_id', branchId)
      .eq('product_id', productId);

    if (error) {
      console.error('Error updating branch stock:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in updateBranchStock:', error);
    return false;
  }
};

// Stock Transfers with database integration
export const createStockTransfer = async (
  transferData: Omit<StockTransfer, 'id' | 'requestedAt'>
): Promise<StockTransfer | null> => {
  try {
    const transferInsert: StockTransferInsert = {
      from_branch_id: transferData.fromBranchId,
      to_branch_id: transferData.toBranchId,
      product_id: transferData.productId,
      quantity: transferData.quantity,
      status: transferData.status || 'pending',
      requested_by: transferData.requestedBy,
      notes: transferData.notes,
    };

    const { data: newTransfer, error } = await supabase
      .from('stock_transfers')
      .insert(transferInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating stock transfer:', error);
      throw error;
    }

    return newTransfer ? convertToStockTransfer(newTransfer) : null;
  } catch (error) {
    console.error('Error in createStockTransfer:', error);
    return null;
  }
};

export const getStockTransfers = async (branchId?: string): Promise<StockTransfer[]> => {
  try {
    let query = supabase
      .from('stock_transfers')
      .select('*')
      .order('requested_at', { ascending: false });

    if (branchId) {
      query = query.or(`from_branch_id.eq.${branchId},to_branch_id.eq.${branchId}`);
    }

    const { data: transfers, error } = await query;

    if (error) {
      console.error('Error fetching stock transfers:', error);
      throw error;
    }

    return transfers?.map(convertToStockTransfer) || [];
  } catch (error) {
    console.error('Error in getStockTransfers:', error);
    return [];
  }
};

export const updateTransferStatus = async (
  transferId: string,
  status: StockTransfer['status'],
  approvedBy?: string
): Promise<boolean> => {
  try {
    const updateData: any = { status };
    if (approvedBy) updateData.approved_by = approvedBy;
    if (status === 'completed') updateData.completed_at = new Date().toISOString();

    const { error } = await supabase
      .from('stock_transfers')
      .update(updateData)
      .eq('id', transferId);

    if (error) {
      console.error('Error updating transfer status:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in updateTransferStatus:', error);
    return false;
  }
};

// Branch Performance with database integration
export const getBranchPerformance = async (period: string = 'monthly'): Promise<BranchPerformance[]> => {
  try {
    // This would require complex queries joining orders, order_items, and products
    // For now, return basic performance data from branches
    const branches = await getBranches();

    // Mock performance data based on actual branches
    // In a real implementation, this would calculate from actual order data
    return branches.map((branch, index) => ({
      branchId: branch.id,
      branchName: branch.name,
      period,
      totalSales: 45000 - (index * 13000), // Mock decreasing sales
      totalOrders: 156 - (index * 58), // Mock decreasing orders
      averageOrderValue: 288.46 + (index * 38), // Mock varying AOV
      topProducts: [
        { productId: 'PRD-001', productName: 'Premium Ballpoint Pen Set', unitsSold: 45 - (index * 23), revenue: 2070 - (index * 1058) },
        { productId: 'PRD-002', productName: 'A4 Notebook Pack', unitsSold: 32 - (index * 10), revenue: 2880 - (index * 1366) }
      ],
      customerCount: 234 - (index * 67),
      inventoryTurnover: 4.2 - (index * 0.4)
    }));
  } catch (error) {
    console.error('Error in getBranchPerformance:', error);
    return [];
  }
};

export const createBranch = async (branchData: Omit<Branch, 'id' | 'createdAt' | 'updatedAt'>, userId: string): Promise<Branch | null> => {
  try {
    const branchInsert: BranchInsert = {
      name: branchData.name,
      code: branchData.code,
      address: {
        street: branchData.address.street,
        city: branchData.address.city,
        state: branchData.address.state,
        postal_code: branchData.address.zipCode,
        country: branchData.address.country
      },
      contact: {
        phone: branchData.phone,
        email: branchData.email,
        manager: branchData.managerName
      },
      is_active: branchData.isActive,
      operating_hours: {
        monday: {
          open: branchData.operatingHours.open,
          close: branchData.operatingHours.close,
          is_closed: false
        }
      },
      created_by: userId,
      updated_by: userId
    };

    const { data: newBranch, error } = await supabase
      .from('branches')
      .insert(branchInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating branch:', error);
      throw error;
    }

    if (newBranch) {
      const branch = convertToBranch(newBranch);

      // Emit real-time event
      realTimeService.emit('branch-created', {
        branch,
        userId
      });

      return branch;
    }

    return null;
  } catch (error) {
    console.error('Error in createBranch:', error);
    return null;
  }
};

export const updateBranch = async (id: string, updates: Partial<Branch>, userId: string): Promise<Branch | null> => {
  try {
    const branchUpdate: BranchUpdate = {
      name: updates.name,
      address: updates.address ? {
        street: updates.address.street,
        city: updates.address.city,
        state: updates.address.state,
        postal_code: updates.address.zipCode,
        country: updates.address.country
      } : undefined,
      contact: (updates.phone || updates.email || updates.managerName) ? {
        phone: updates.phone,
        email: updates.email,
        manager: updates.managerName
      } : undefined,
      is_active: updates.isActive,
      operating_hours: updates.operatingHours ? {
        monday: {
          open: updates.operatingHours.open,
          close: updates.operatingHours.close,
          is_closed: false
        }
      } : undefined,
      updated_by: userId
    };

    const { data: updatedBranch, error } = await supabase
      .from('branches')
      .update(branchUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating branch:', error);
      throw error;
    }

    if (updatedBranch) {
      const branch = convertToBranch(updatedBranch);

      // Emit real-time event
      realTimeService.emit('branch-updated', {
        branchId: id,
        newData: branch,
        userId
      });

      return branch;
    }

    return null;
  } catch (error) {
    console.error('Error in updateBranch:', error);
    return null;
  }
};

export const deleteBranch = async (id: string, userId: string): Promise<boolean> => {
  try {
    // Check if it's a main branch
    const branch = await getBranchById(id);
    if (!branch) return false;

    const { data: mainBranch } = await supabase
      .from('branches')
      .select('id')
      .eq('is_main_branch', true)
      .single();

    if (mainBranch && mainBranch.id === id) {
      throw new Error('Cannot delete the main branch. Please designate another branch as main first.');
    }

    // Soft delete by setting is_active to false
    const { error } = await supabase
      .from('branches')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      console.error('Error deleting branch:', error);
      throw error;
    }

    // Emit real-time event
    realTimeService.emit('branch-deleted', {
      branchId: id,
      branch,
      userId
    });

    return true;
  } catch (error) {
    console.error('Error in deleteBranch:', error);
    return false;
  }
};

// Search branches
export const searchBranches = async (query: string): Promise<Branch[]> => {
  try {
    const searchTerm = `%${query.toLowerCase()}%`;

    const { data: branches, error } = await supabase
      .from('branches')
      .select('*')
      .or(`name.ilike.${searchTerm},code.ilike.${searchTerm}`)
      .eq('is_active', true);

    if (error) {
      console.error('Error searching branches:', error);
      throw error;
    }

    return branches?.map(convertToBranch) || [];
  } catch (error) {
    console.error('Error in searchBranches:', error);
    return [];
  }
};
