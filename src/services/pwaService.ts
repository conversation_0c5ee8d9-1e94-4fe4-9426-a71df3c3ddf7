/**
 * PWA Service for YalaOffice
 * Manages Progressive Web App functionality, offline capabilities, and push notifications
 */

// PWA Installation and Management
export class PWAService {
  private static deferredPrompt: any = null;
  private static isInstalled = false;

  // Initialize PWA functionality
  static async initialize(): Promise<void> {
    try {
      // Register service worker
      if ('serviceWorker' in navigator) {
        await this.registerServiceWorker();
      }

      // Setup install prompt handling
      this.setupInstallPrompt();

      // Check if app is already installed
      this.checkInstallationStatus();

      // Setup push notifications
      await this.setupPushNotifications();

      // Setup real-time data synchronization (no offline caching)
      this.setupRealTimeSync();

      console.log('PWA Service initialized successfully');
    } catch (error) {
      console.error('PWA Service initialization failed:', error);
    }
  }

  // Register service worker
  private static async registerServiceWorker(): Promise<void> {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('Service Worker registered successfully:', registration);

      // Handle service worker updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              this.showUpdateNotification();
            }
          });
        }
      });

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        this.handleServiceWorkerMessage(event.data);
      });

    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }

  // Setup install prompt handling
  private static setupInstallPrompt(): void {
    window.addEventListener('beforeinstallprompt', (event) => {
      event.preventDefault();
      this.deferredPrompt = event;
      this.showInstallButton();
    });

    window.addEventListener('appinstalled', () => {
      this.isInstalled = true;
      this.hideInstallButton();
      this.trackInstallation();
    });
  }

  // Check if app is installed
  private static checkInstallationStatus(): void {
    // Check if running in standalone mode
    if (window.matchMedia('(display-mode: standalone)').matches) {
      this.isInstalled = true;
    }

    // Check if running as PWA on mobile
    if (window.navigator.standalone === true) {
      this.isInstalled = true;
    }
  }

  // Show install button
  private static showInstallButton(): void {
    const installButton = document.getElementById('pwa-install-button');
    if (installButton) {
      installButton.style.display = 'block';
      installButton.addEventListener('click', () => this.promptInstall());
    } else {
      // Create install button dynamically
      this.createInstallButton();
    }
  }

  // Hide install button
  private static hideInstallButton(): void {
    const installButton = document.getElementById('pwa-install-button');
    if (installButton) {
      installButton.style.display = 'none';
    }
  }

  // Create install button dynamically
  private static createInstallButton(): void {
    const button = document.createElement('button');
    button.id = 'pwa-install-button';
    button.innerHTML = '📱 Install App';
    button.className = 'fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors z-50';
    button.addEventListener('click', () => this.promptInstall());
    document.body.appendChild(button);
  }

  // Prompt user to install PWA
  static async promptInstall(): Promise<boolean> {
    if (!this.deferredPrompt) {
      return false;
    }

    try {
      this.deferredPrompt.prompt();
      const { outcome } = await this.deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
        return true;
      } else {
        console.log('User dismissed the install prompt');
        return false;
      }
    } catch (error) {
      console.error('Install prompt failed:', error);
      return false;
    } finally {
      this.deferredPrompt = null;
    }
  }

  // Setup push notifications
  private static async setupPushNotifications(): Promise<void> {
    if (!('Notification' in window) || !('serviceWorker' in navigator)) {
      console.log('Push notifications not supported');
      return;
    }

    try {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        console.log('Push notifications enabled');
        await this.subscribeToPushNotifications();
      }
    } catch (error) {
      console.error('Push notification setup failed:', error);
    }
  }

  // Subscribe to push notifications
  private static async subscribeToPushNotifications(): Promise<void> {
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(
          // Replace with your VAPID public key
          'BEl62iUYgUivxIkv69yViEuiBIa40HI80NqIUHI80NqIUHI80NqIUHI80NqIUHI80NqI'
        )
      });

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);
      console.log('Push notification subscription successful');
    } catch (error) {
      console.error('Push notification subscription failed:', error);
    }
  }

  // Convert VAPID key
  private static urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Send subscription to server
  private static async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    try {
      // In a real app, send this to your backend
      console.log('Push subscription:', JSON.stringify(subscription));
      
      // Store subscription in localStorage for now
      localStorage.setItem('pushSubscription', JSON.stringify(subscription));
    } catch (error) {
      console.error('Failed to send subscription to server:', error);
    }
  }

  // Setup real-time data synchronization (no offline caching)
  private static setupRealTimeSync(): void {
    // Listen for online/offline events for connection status only
    window.addEventListener('online', () => {
      console.log('Back online - real-time sync active');
      this.showSyncNotification('Connected - real-time sync active');
    });

    window.addEventListener('offline', () => {
      console.log('Gone offline - real-time sync paused');
      this.showSyncNotification('Offline - real-time sync paused', 'warning');
    });

    // Show initial connection status
    if (navigator.onLine) {
      console.log('Online - real-time sync active');
    } else {
      this.showSyncNotification('Offline - real-time sync paused', 'warning');
    }
  }

  // Real-time sync status (no offline data caching)
  private static async checkRealTimeSync(): Promise<void> {
    try {
      // Check if real-time connection is active
      if (navigator.onLine) {
        this.showSyncNotification('Real-time sync active');
      } else {
        this.showSyncNotification('Real-time sync paused - offline', 'warning');
      }
    } catch (error) {
      console.error('Real-time sync check failed:', error);
    }
  }

  // Handle offline mode (no caching, just status)
  private static handleOfflineMode(): void {
    // Show offline indicator only
    this.showOfflineIndicator();

    // Note: No offline caching - pure real-time sync only
    console.log('Offline mode: Real-time sync paused until connection restored');
  }

  // Show offline indicator
  private static showOfflineIndicator(): void {
    let indicator = document.getElementById('offline-indicator');
    if (!indicator) {
      indicator = document.createElement('div');
      indicator.id = 'offline-indicator';
      indicator.innerHTML = '📡 Offline Mode';
      indicator.className = 'fixed top-4 right-4 bg-orange-500 text-white px-3 py-1 rounded-lg text-sm z-50';
      document.body.appendChild(indicator);
    }
    indicator.style.display = 'block';
  }

  // Hide offline indicator
  private static hideOfflineIndicator(): void {
    const indicator = document.getElementById('offline-indicator');
    if (indicator) {
      indicator.style.display = 'none';
    }
  }

  // Note: Offline caching removed - using pure real-time synchronization
  // No local data storage, all data comes from live database connections

  // Handle service worker messages (real-time sync only)
  private static handleServiceWorkerMessage(data: any): void {
    switch (data.type) {
      case 'REALTIME_SYNC_ACTIVE':
        this.showSyncNotification('Real-time sync active');
        break;
      case 'REALTIME_SYNC_PAUSED':
        this.showSyncNotification('Real-time sync paused - offline', 'warning');
        break;
      case 'CACHE_UPDATED':
        this.showSyncNotification('App updated - restart to apply changes');
        break;
    }
  }

  // Show sync notification
  private static showSyncNotification(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.className = `fixed top-4 left-4 px-4 py-2 rounded-lg text-white text-sm z-50 ${
      type === 'success' ? 'bg-green-500' : 
      type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  // Show update notification
  private static showUpdateNotification(): void {
    const notification = document.createElement('div');
    notification.innerHTML = `
      <div class="flex items-center justify-between">
        <span>New version available!</span>
        <button onclick="window.location.reload()" class="ml-2 px-2 py-1 bg-white text-blue-600 rounded text-xs">
          Update
        </button>
      </div>
    `;
    notification.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm z-50';
    
    document.body.appendChild(notification);
  }

  // Track installation analytics
  private static trackInstallation(): void {
    console.log('PWA installed successfully');
    
    // Track installation event
    const installData = {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      platform: navigator.platform
    };
    
    localStorage.setItem('pwaInstallData', JSON.stringify(installData));
  }

  // Check if PWA is installed
  static isAppInstalled(): boolean {
    return this.isInstalled;
  }

  // Get installation status
  static getInstallationStatus(): {
    isInstalled: boolean;
    canInstall: boolean;
    isStandalone: boolean;
  } {
    return {
      isInstalled: this.isInstalled,
      canInstall: this.deferredPrompt !== null,
      isStandalone: window.matchMedia('(display-mode: standalone)').matches
    };
  }

  // Send push notification (for testing)
  static async sendTestNotification(): Promise<void> {
    if ('serviceWorker' in navigator && 'Notification' in window) {
      const registration = await navigator.serviceWorker.ready;
      
      registration.showNotification('YalaOffice Notification', {
        body: 'This is a test notification from YalaOffice PWA',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
          url: '/'
        },
        actions: [
          {
            action: 'view',
            title: 'View',
            icon: '/icons/view-action.png'
          },
          {
            action: 'dismiss',
            title: 'Dismiss',
            icon: '/icons/dismiss-action.png'
          }
        ]
      });
    }
  }
}

export default PWAService;
