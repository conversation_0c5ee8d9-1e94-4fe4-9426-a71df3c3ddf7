/**
 * Advanced Audit Logging Service for YalaOffice
 * Provides comprehensive audit trails, compliance reporting, and activity tracking
 */

import { supabase } from '../integrations/supabase/client';

// Audit interfaces
export interface AuditLog {
  id: string;
  userId: string;
  userEmail: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: any;
  newValues?: any;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'authentication' | 'authorization' | 'data_access' | 'data_modification' | 'system_change' | 'security';
  success: boolean;
  errorMessage?: string;
  sessionId?: string;
}

export interface ComplianceReport {
  id: string;
  reportType: 'gdpr' | 'sox' | 'hipaa' | 'custom';
  title: string;
  description: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  filters: {
    users?: string[];
    actions?: string[];
    resources?: string[];
    severity?: string[];
  };
  generatedBy: string;
  generatedAt: string;
  totalRecords: number;
  summary: {
    [key: string]: number;
  };
  data: AuditLog[];
}

export interface ActivitySummary {
  totalActions: number;
  uniqueUsers: number;
  topActions: { action: string; count: number }[];
  topUsers: { userId: string; userEmail: string; count: number }[];
  actionsByCategory: { [category: string]: number };
  actionsBySeverity: { [severity: string]: number };
  dailyActivity: { date: string; count: number }[];
  securityEvents: number;
  failedActions: number;
}

// Advanced Audit Service
export class AdvancedAuditService {
  
  // Log audit event
  static async logAuditEvent(auditData: Omit<AuditLog, 'id' | 'timestamp'>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('audit_logs')
        .insert({
          user_id: auditData.userId,
          user_email: auditData.userEmail,
          action: auditData.action,
          resource: auditData.resource,
          resource_id: auditData.resourceId,
          old_values: auditData.oldValues,
          new_values: auditData.newValues,
          ip_address: auditData.ipAddress,
          user_agent: auditData.userAgent,
          severity: auditData.severity,
          category: auditData.category,
          success: auditData.success,
          error_message: auditData.errorMessage,
          session_id: auditData.sessionId
        });

      if (error) {
        console.error('Error logging audit event:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in logAuditEvent:', error);
      return false;
    }
  }

  // Get audit logs with advanced filtering
  static async getAuditLogs(filters?: {
    userId?: string;
    action?: string;
    resource?: string;
    category?: string;
    severity?: string;
    success?: boolean;
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ logs: AuditLog[]; totalCount: number }> {
    try {
      let query = supabase
        .from('audit_logs')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false });

      if (filters?.userId) {
        query = query.eq('user_id', filters.userId);
      }

      if (filters?.action) {
        query = query.ilike('action', `%${filters.action}%`);
      }

      if (filters?.resource) {
        query = query.eq('resource', filters.resource);
      }

      if (filters?.category) {
        query = query.eq('category', filters.category);
      }

      if (filters?.severity) {
        query = query.eq('severity', filters.severity);
      }

      if (filters?.success !== undefined) {
        query = query.eq('success', filters.success);
      }

      if (filters?.dateFrom) {
        query = query.gte('created_at', filters.dateFrom);
      }

      if (filters?.dateTo) {
        query = query.lte('created_at', filters.dateTo);
      }

      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      if (filters?.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching audit logs:', error);
        return { logs: [], totalCount: 0 };
      }

      const logs = data?.map(log => ({
        id: log.id,
        userId: log.user_id,
        userEmail: log.user_email,
        action: log.action,
        resource: log.resource,
        resourceId: log.resource_id,
        oldValues: log.old_values,
        newValues: log.new_values,
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        timestamp: log.created_at,
        severity: log.severity,
        category: log.category,
        success: log.success,
        errorMessage: log.error_message,
        sessionId: log.session_id
      })) || [];

      return { logs, totalCount: count || 0 };
    } catch (error) {
      console.error('Error in getAuditLogs:', error);
      return { logs: [], totalCount: 0 };
    }
  }

  // Generate compliance report
  static async generateComplianceReport(
    reportType: 'gdpr' | 'sox' | 'hipaa' | 'custom',
    dateRange: { startDate: string; endDate: string },
    filters?: {
      users?: string[];
      actions?: string[];
      resources?: string[];
      severity?: string[];
    },
    generatedBy: string
  ): Promise<ComplianceReport> {
    try {
      // Build query based on compliance requirements
      const auditFilters = {
        dateFrom: dateRange.startDate,
        dateTo: dateRange.endDate,
        ...filters
      };

      const { logs, totalCount } = await this.getAuditLogs(auditFilters);

      // Generate summary statistics
      const summary: { [key: string]: number } = {};
      const actionCounts: { [action: string]: number } = {};
      const userCounts: { [userId: string]: number } = {};
      const categoryCounts: { [category: string]: number } = {};

      logs.forEach(log => {
        actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;
        userCounts[log.userId] = (userCounts[log.userId] || 0) + 1;
        categoryCounts[log.category] = (categoryCounts[log.category] || 0) + 1;
      });

      summary['Total Actions'] = totalCount;
      summary['Unique Users'] = Object.keys(userCounts).length;
      summary['Failed Actions'] = logs.filter(log => !log.success).length;
      summary['Security Events'] = logs.filter(log => log.category === 'security').length;

      const report: ComplianceReport = {
        id: `report-${Date.now()}`,
        reportType,
        title: `${reportType.toUpperCase()} Compliance Report`,
        description: `Audit trail report for compliance with ${reportType.toUpperCase()} requirements`,
        dateRange,
        filters: filters || {},
        generatedBy,
        generatedAt: new Date().toISOString(),
        totalRecords: totalCount,
        summary,
        data: logs
      };

      // Store report in database
      await supabase
        .from('compliance_reports')
        .insert({
          report_type: reportType,
          title: report.title,
          description: report.description,
          date_range: dateRange,
          filters: filters || {},
          generated_by: generatedBy,
          total_records: totalCount,
          summary,
          data: logs
        });

      return report;
    } catch (error) {
      console.error('Error generating compliance report:', error);
      throw error;
    }
  }

  // Get activity summary
  static async getActivitySummary(dateRange?: { startDate: string; endDate: string }): Promise<ActivitySummary> {
    try {
      const filters = dateRange ? {
        dateFrom: dateRange.startDate,
        dateTo: dateRange.endDate
      } : undefined;

      const { logs } = await this.getAuditLogs(filters);

      // Calculate summary statistics
      const uniqueUsers = new Set(logs.map(log => log.userId)).size;
      const actionCounts: { [action: string]: number } = {};
      const userCounts: { [userId: string]: { email: string; count: number } } = {};
      const categoryCounts: { [category: string]: number } = {};
      const severityCounts: { [severity: string]: number } = {};
      const dailyActivity: { [date: string]: number } = {};

      logs.forEach(log => {
        actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;
        
        if (!userCounts[log.userId]) {
          userCounts[log.userId] = { email: log.userEmail, count: 0 };
        }
        userCounts[log.userId].count++;

        categoryCounts[log.category] = (categoryCounts[log.category] || 0) + 1;
        severityCounts[log.severity] = (severityCounts[log.severity] || 0) + 1;

        const date = log.timestamp.split('T')[0];
        dailyActivity[date] = (dailyActivity[date] || 0) + 1;
      });

      // Get top actions
      const topActions = Object.entries(actionCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([action, count]) => ({ action, count }));

      // Get top users
      const topUsers = Object.entries(userCounts)
        .sort(([, a], [, b]) => b.count - a.count)
        .slice(0, 10)
        .map(([userId, data]) => ({ userId, userEmail: data.email, count: data.count }));

      // Convert daily activity to array
      const dailyActivityArray = Object.entries(dailyActivity)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([date, count]) => ({ date, count }));

      return {
        totalActions: logs.length,
        uniqueUsers,
        topActions,
        topUsers,
        actionsByCategory: categoryCounts,
        actionsBySeverity: severityCounts,
        dailyActivity: dailyActivityArray,
        securityEvents: logs.filter(log => log.category === 'security').length,
        failedActions: logs.filter(log => !log.success).length
      };
    } catch (error) {
      console.error('Error getting activity summary:', error);
      return {
        totalActions: 0,
        uniqueUsers: 0,
        topActions: [],
        topUsers: [],
        actionsByCategory: {},
        actionsBySeverity: {},
        dailyActivity: [],
        securityEvents: 0,
        failedActions: 0
      };
    }
  }

  // Get stored compliance reports
  static async getComplianceReports(limit: number = 10): Promise<ComplianceReport[]> {
    try {
      const { data, error } = await supabase
        .from('compliance_reports')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching compliance reports:', error);
        return [];
      }

      return data?.map(report => ({
        id: report.id,
        reportType: report.report_type,
        title: report.title,
        description: report.description,
        dateRange: report.date_range,
        filters: report.filters,
        generatedBy: report.generated_by,
        generatedAt: report.created_at,
        totalRecords: report.total_records,
        summary: report.summary,
        data: report.data
      })) || [];
    } catch (error) {
      console.error('Error in getComplianceReports:', error);
      return [];
    }
  }

  // Export audit logs to CSV
  static async exportAuditLogs(filters?: any): Promise<string> {
    try {
      const { logs } = await this.getAuditLogs(filters);
      
      const headers = [
        'Timestamp',
        'User Email',
        'Action',
        'Resource',
        'Resource ID',
        'IP Address',
        'Severity',
        'Category',
        'Success',
        'Error Message'
      ];

      const csvContent = [
        headers.join(','),
        ...logs.map(log => [
          log.timestamp,
          log.userEmail,
          log.action,
          log.resource,
          log.resourceId || '',
          log.ipAddress,
          log.severity,
          log.category,
          log.success.toString(),
          log.errorMessage || ''
        ].map(field => `"${field}"`).join(','))
      ].join('\n');

      return csvContent;
    } catch (error) {
      console.error('Error exporting audit logs:', error);
      throw error;
    }
  }

  // Helper method to log common actions
  static async logUserAction(
    userId: string,
    userEmail: string,
    action: string,
    resource: string,
    resourceId?: string,
    oldValues?: any,
    newValues?: any,
    success: boolean = true,
    errorMessage?: string
  ): Promise<boolean> {
    return this.logAuditEvent({
      userId,
      userEmail,
      action,
      resource,
      resourceId,
      oldValues,
      newValues,
      ipAddress: '127.0.0.1', // Would get from request in real app
      userAgent: navigator.userAgent,
      severity: success ? 'low' : 'medium',
      category: 'data_modification',
      success,
      errorMessage
    });
  }

  // Helper method to log security events
  static async logSecurityEvent(
    userId: string,
    userEmail: string,
    action: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    success: boolean = true,
    errorMessage?: string
  ): Promise<boolean> {
    return this.logAuditEvent({
      userId,
      userEmail,
      action,
      resource: 'security',
      ipAddress: '127.0.0.1', // Would get from request in real app
      userAgent: navigator.userAgent,
      severity,
      category: 'security',
      success,
      errorMessage
    });
  }
}

export default AdvancedAuditService;
