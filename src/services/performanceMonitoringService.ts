/**
 * Performance Monitoring Service for YalaOffice Production
 * Tracks application performance, user interactions, and system metrics
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  unit?: string;
}

interface UserInteractionMetric {
  action: string;
  component: string;
  duration: number;
  timestamp: number;
  userId?: string;
  sessionId: string;
}

interface ErrorMetric {
  error: string;
  stack?: string;
  component: string;
  timestamp: number;
  userId?: string;
  sessionId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface BusinessMetric {
  metric: string;
  value: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

export class PerformanceMonitoringService {
  private static instance: PerformanceMonitoringService;
  private metricsBuffer: PerformanceMetric[] = [];
  private interactionsBuffer: UserInteractionMetric[] = [];
  private errorsBuffer: ErrorMetric[] = [];
  private businessMetricsBuffer: BusinessMetric[] = [];
  private sessionId: string;
  private userId?: string;
  private flushInterval: number = 30000; // 30 seconds
  private maxBufferSize: number = 100;
  private isProduction: boolean;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.isProduction = process.env.NODE_ENV === 'production';
    this.initializeMonitoring();
  }

  static getInstance(): PerformanceMonitoringService {
    if (!PerformanceMonitoringService.instance) {
      PerformanceMonitoringService.instance = new PerformanceMonitoringService();
    }
    return PerformanceMonitoringService.instance;
  }

  // Initialize monitoring
  private initializeMonitoring(): void {
    if (!this.isProduction) return;

    // Set up periodic flushing
    setInterval(() => {
      this.flushMetrics();
    }, this.flushInterval);

    // Set up performance observer
    this.setupPerformanceObserver();

    // Set up error tracking
    this.setupErrorTracking();

    // Set up page visibility tracking
    this.setupVisibilityTracking();

    // Set up network monitoring
    this.setupNetworkMonitoring();
  }

  // Generate unique session ID
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Set user ID for tracking
  setUserId(userId: string): void {
    this.userId = userId;
  }

  // Track performance metrics
  trackMetric(name: string, value: number, tags?: Record<string, string>, unit?: string): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      tags,
      unit
    };

    this.metricsBuffer.push(metric);
    this.checkBufferSize();
  }

  // Track user interactions
  trackInteraction(action: string, component: string, startTime?: number): void {
    const endTime = performance.now();
    const duration = startTime ? endTime - startTime : 0;

    const interaction: UserInteractionMetric = {
      action,
      component,
      duration,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId
    };

    this.interactionsBuffer.push(interaction);
    this.checkBufferSize();
  }

  // Track errors
  trackError(error: string, component: string, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium', stack?: string): void {
    const errorMetric: ErrorMetric = {
      error,
      stack,
      component,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId,
      severity
    };

    this.errorsBuffer.push(errorMetric);
    
    // Immediately flush critical errors
    if (severity === 'critical') {
      this.flushErrors();
    } else {
      this.checkBufferSize();
    }
  }

  // Track business metrics
  trackBusinessMetric(metric: string, value: number, metadata?: Record<string, any>): void {
    const businessMetric: BusinessMetric = {
      metric,
      value,
      timestamp: Date.now(),
      metadata
    };

    this.businessMetricsBuffer.push(businessMetric);
    this.checkBufferSize();
  }

  // Setup performance observer
  private setupPerformanceObserver(): void {
    if (!('PerformanceObserver' in window)) return;

    // Navigation timing
    const navObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          this.trackMetric('page_load_time', navEntry.loadEventEnd - navEntry.fetchStart, { type: 'navigation' }, 'ms');
          this.trackMetric('dom_content_loaded', navEntry.domContentLoadedEventEnd - navEntry.fetchStart, { type: 'navigation' }, 'ms');
          this.trackMetric('first_paint', navEntry.responseEnd - navEntry.fetchStart, { type: 'navigation' }, 'ms');
        }
      }
    });

    navObserver.observe({ entryTypes: ['navigation'] });

    // Resource timing
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming;
          this.trackMetric('resource_load_time', resourceEntry.responseEnd - resourceEntry.fetchStart, {
            type: 'resource',
            name: resourceEntry.name,
            initiatorType: resourceEntry.initiatorType
          }, 'ms');
        }
      }
    });

    resourceObserver.observe({ entryTypes: ['resource'] });

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.trackMetric('largest_contentful_paint', entry.startTime, { type: 'lcp' }, 'ms');
      }
    });

    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.trackMetric('first_input_delay', (entry as any).processingStart - entry.startTime, { type: 'fid' }, 'ms');
      }
    });

    fidObserver.observe({ entryTypes: ['first-input'] });
  }

  // Setup error tracking
  private setupErrorTracking(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.trackError(
        event.message,
        'global',
        'high',
        event.error?.stack
      );
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError(
        event.reason?.message || 'Unhandled promise rejection',
        'promise',
        'high',
        event.reason?.stack
      );
    });
  }

  // Setup visibility tracking
  private setupVisibilityTracking(): void {
    let visibilityStart = Date.now();

    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        const visibilityDuration = Date.now() - visibilityStart;
        this.trackMetric('page_visibility_duration', visibilityDuration, { type: 'visibility' }, 'ms');
      } else {
        visibilityStart = Date.now();
      }
    });
  }

  // Setup network monitoring
  private setupNetworkMonitoring(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      
      this.trackMetric('network_effective_type', 0, {
        type: 'network',
        effectiveType: connection.effectiveType,
        downlink: connection.downlink?.toString(),
        rtt: connection.rtt?.toString()
      });

      connection.addEventListener('change', () => {
        this.trackMetric('network_change', Date.now(), {
          type: 'network',
          effectiveType: connection.effectiveType,
          downlink: connection.downlink?.toString(),
          rtt: connection.rtt?.toString()
        });
      });
    }
  }

  // Check buffer size and flush if needed
  private checkBufferSize(): void {
    if (this.metricsBuffer.length >= this.maxBufferSize ||
        this.interactionsBuffer.length >= this.maxBufferSize ||
        this.errorsBuffer.length >= this.maxBufferSize ||
        this.businessMetricsBuffer.length >= this.maxBufferSize) {
      this.flushMetrics();
    }
  }

  // Flush all metrics to backend
  private async flushMetrics(): Promise<void> {
    if (!this.isProduction) return;

    const payload = {
      sessionId: this.sessionId,
      userId: this.userId,
      timestamp: Date.now(),
      metrics: [...this.metricsBuffer],
      interactions: [...this.interactionsBuffer],
      errors: [...this.errorsBuffer],
      businessMetrics: [...this.businessMetricsBuffer]
    };

    // Clear buffers
    this.metricsBuffer = [];
    this.interactionsBuffer = [];
    this.errorsBuffer = [];
    this.businessMetricsBuffer = [];

    try {
      await fetch('/api/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
    } catch (error) {
      console.error('Failed to send metrics:', error);
      // Could implement retry logic here
    }
  }

  // Flush only errors (for critical errors)
  private async flushErrors(): Promise<void> {
    if (!this.isProduction || this.errorsBuffer.length === 0) return;

    const payload = {
      sessionId: this.sessionId,
      userId: this.userId,
      timestamp: Date.now(),
      errors: [...this.errorsBuffer]
    };

    this.errorsBuffer = [];

    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });
    } catch (error) {
      console.error('Failed to send error metrics:', error);
    }
  }

  // Get current performance metrics
  getCurrentMetrics(): {
    memory?: PerformanceMemory;
    timing: PerformanceTiming;
    navigation: PerformanceNavigation;
  } {
    return {
      memory: (performance as any).memory,
      timing: performance.timing,
      navigation: performance.navigation
    };
  }

  // Track page view
  trackPageView(page: string, referrer?: string): void {
    this.trackInteraction('page_view', 'navigation');
    this.trackBusinessMetric('page_view', 1, {
      page,
      referrer,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    });
  }

  // Track feature usage
  trackFeatureUsage(feature: string, metadata?: Record<string, any>): void {
    this.trackBusinessMetric('feature_usage', 1, {
      feature,
      ...metadata
    });
  }

  // Track business events
  trackBusinessEvent(event: string, value?: number, metadata?: Record<string, any>): void {
    this.trackBusinessMetric(event, value || 1, metadata);
  }

  // Manual flush
  flush(): Promise<void> {
    return this.flushMetrics();
  }

  // Cleanup on page unload
  cleanup(): void {
    this.flushMetrics();
  }
}

// Export singleton instance
export const performanceMonitoring = PerformanceMonitoringService.getInstance();

// Setup cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    performanceMonitoring.cleanup();
  });
}

export default performanceMonitoring;
