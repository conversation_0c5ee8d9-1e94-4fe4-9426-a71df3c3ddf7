import { liveDashboardService } from './liveDashboardService';
import { liveDataService } from './liveDataService';

export interface MobileStats {
  revenue: { value: number; change: number; trend: 'up' | 'down' };
  orders: { value: number; change: number; trend: 'up' | 'down' };
  customers: { value: number; change: number; trend: 'up' | 'down' };
  products: { value: number; change: number; trend: 'up' | 'down' };
}

export interface MobileActivity {
  id: string;
  type: 'order' | 'customer' | 'product' | 'alert';
  title: string;
  description: string;
  time: string;
  status: 'success' | 'warning' | 'error' | 'info';
}

class MobileDashboardService {
  async getMobileStats(): Promise<MobileStats> {
    try {
      console.log('MobileDashboardService: Loading mobile stats...');
      
      // Get dashboard stats from live service
      const dashboardStats = await liveDashboardService.getDashboardStats();
      
      // Transform to mobile format
      const mobileStats: MobileStats = {
        revenue: {
          value: dashboardStats.revenue.value,
          change: dashboardStats.revenue.change,
          trend: dashboardStats.revenue.trend
        },
        orders: {
          value: dashboardStats.orders.value,
          change: dashboardStats.orders.change,
          trend: dashboardStats.orders.trend
        },
        customers: {
          value: dashboardStats.customers.value,
          change: dashboardStats.customers.change,
          trend: dashboardStats.customers.trend
        },
        products: {
          value: dashboardStats.products.value,
          change: dashboardStats.products.change,
          trend: dashboardStats.products.trend
        }
      };

      console.log('MobileDashboardService: Mobile stats loaded:', mobileStats);
      return mobileStats;
    } catch (error) {
      console.error('MobileDashboardService: Error loading mobile stats:', error);
      
      // Return fallback stats
      return {
        revenue: { value: 0, change: 0, trend: 'up' },
        orders: { value: 0, change: 0, trend: 'up' },
        customers: { value: 0, change: 0, trend: 'up' },
        products: { value: 0, change: 0, trend: 'up' }
      };
    }
  }

  async getMobileActivity(): Promise<MobileActivity[]> {
    try {
      console.log('MobileDashboardService: Loading mobile activity...');
      
      // Get recent orders for activity
      const orders = await liveDataService.getAllOrders();
      const recentOrders = orders.slice(0, 3);
      
      // Get recent users for activity
      const users = await liveDataService.getAllUsers();
      const recentUsers = users.slice(0, 2);
      
      // Get products for stock alerts
      const products = await liveDataService.getAllProducts();
      const lowStockProducts = products.filter(product => product.stock < 10).slice(0, 2);
      
      const activities: MobileActivity[] = [];
      
      // Add order activities
      recentOrders.forEach((order, index) => {
        activities.push({
          id: `order-${order.id}`,
          type: 'order',
          title: `Order #${order.id.slice(0, 8)}`,
          description: `${order.users?.full_name || 'Customer'} - ${order.total} MAD`,
          time: this.getRelativeTime(order.created_at),
          status: order.status === 'delivered' ? 'success' : 'info'
        });
      });
      
      // Add customer activities
      recentUsers.forEach((user, index) => {
        if (user.role === 'client' || user.role === 'reseller') {
          activities.push({
            id: `customer-${user.id}`,
            type: 'customer',
            title: 'New Customer',
            description: `${user.full_name} registered`,
            time: this.getRelativeTime(user.created_at),
            status: 'info'
          });
        }
      });
      
      // Add stock alerts
      lowStockProducts.forEach((product, index) => {
        activities.push({
          id: `alert-${product.id}`,
          type: 'alert',
          title: 'Low Stock Alert',
          description: `${product.title} - Only ${product.stock} left`,
          time: '1 hour ago',
          status: 'warning'
        });
      });
      
      // Sort by time (most recent first) and limit to 5
      const sortedActivities = activities.slice(0, 5);
      
      console.log('MobileDashboardService: Mobile activity loaded:', sortedActivities);
      return sortedActivities;
    } catch (error) {
      console.error('MobileDashboardService: Error loading mobile activity:', error);
      
      // Return fallback activity
      return [
        {
          id: 'fallback-1',
          type: 'info' as any,
          title: 'System Status',
          description: 'YalaOffice is running',
          time: 'now',
          status: 'success'
        }
      ];
    }
  }

  private getRelativeTime(dateString: string): string {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      
      if (diffInMinutes < 1) return 'just now';
      if (diffInMinutes < 60) return `${diffInMinutes} min ago`;
      
      const diffInHours = Math.floor(diffInMinutes / 60);
      if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
      
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    } catch (error) {
      return 'recently';
    }
  }

  async refreshMobileData(): Promise<{ stats: MobileStats; activity: MobileActivity[] }> {
    try {
      console.log('MobileDashboardService: Refreshing mobile data...');
      
      const [stats, activity] = await Promise.all([
        this.getMobileStats(),
        this.getMobileActivity()
      ]);
      
      return { stats, activity };
    } catch (error) {
      console.error('MobileDashboardService: Error refreshing mobile data:', error);
      throw error;
    }
  }
}

export const mobileDashboardService = new MobileDashboardService();
