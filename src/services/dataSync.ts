/**
 * Centralized Data Synchronization Service
 * Ensures all CRUD operations are synchronized across the system and database
 */

import { supabase } from '@/integrations/supabase/client';
import { liveDataService } from './liveDataService';
import { liveDashboardService } from './liveDashboardService';
import { realTimeService } from './realTimeService';

export interface SyncResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export class DataSyncService {
  private static instance: DataSyncService;
  private subscribers: Map<string, ((data: any) => void)[]> = new Map();

  private constructor() {}

  static getInstance(): DataSyncService {
    if (!DataSyncService.instance) {
      DataSyncService.instance = new DataSyncService();
    }
    return DataSyncService.instance;
  }

  /**
   * Subscribe to data changes for real-time updates
   */
  subscribe(table: string, callback: (data: any) => void): () => void {
    if (!this.subscribers.has(table)) {
      this.subscribers.set(table, []);
    }
    this.subscribers.get(table)!.push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.subscribers.get(table);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  /**
   * Notify all subscribers of data changes
   */
  private notifySubscribers(table: string, data: any) {
    try {
      const callbacks = this.subscribers.get(table);
      if (callbacks && callbacks.length > 0) {
        callbacks.forEach(callback => {
          try {
            callback(data);
          } catch (error) {
            console.error(`Error in subscriber callback for ${table}:`, error);
          }
        });
      }
    } catch (error) {
      console.error(`Error notifying subscribers for ${table}:`, error);
    }
  }

  // ==================== USER OPERATIONS ====================

  /**
   * Create a new user with full synchronization
   * Uses a different approach that doesn't require admin privileges
   */
  async createUser(userData: {
    email: string;
    password: string;
    full_name: string;
    user_type: string;
    phone?: string;
    city?: string;
    company_name?: string;
    company_address?: string;
  }): Promise<SyncResult> {
    try {
      // Generate a unique user ID
      const userId = crypto.randomUUID();

      // 1. Create user profile in database first
      const { data: profileData, error: profileError } = await supabase
        .from('users')
        .insert({
          id: userId,
          email: userData.email,
          full_name: userData.full_name,
          user_type: userData.user_type,
          phone: userData.phone,
          city: userData.city,
          company_name: userData.company_name,
          company_address: userData.company_address,
          is_active: true // User is active by default
        })
        .select()
        .single();

      if (profileError) throw profileError;

      // 2. Create customer profile if needed
      if (['client', 'reseller'].includes(userData.user_type)) {
        await supabase.from('customer_profiles').insert({
          user_id: userId,
          customer_type: userData.user_type,
          credit_limit: userData.user_type === 'reseller' ? 10000 : 5000,
          discount_percentage: userData.user_type === 'reseller' ? 15 : 5,
          loyalty_points: 0
        });
      }

      // 3. Create a notification for the user to set up their account
      await supabase.from('notifications').insert({
        user_id: userId,
        title: 'Account Created',
        message: `Your account has been created. Please contact an administrator to activate your account and set up your password.`,
        type: 'info',
        is_read: false
      });

      // 4. Notify subscribers
      this.notifySubscribers('users', { action: 'create', data: profileData });

      return { success: true, data: profileData };
    } catch (error: any) {
      console.error('Error creating user:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update user with full synchronization
   */
  async updateUser(userId: string, updates: any): Promise<SyncResult> {
    try {
      console.log('DataSync: Updating user:', { userId, updates });

      // Add updated_at timestamp
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      // 1. Update user profile in database
      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('DataSync: Database error during user update:', error);
        throw error;
      }

      console.log('DataSync: User updated successfully:', data);

      // 2. Update auth metadata if needed
      if (updates.full_name || updates.user_type || updates.phone) {
        try {
          await supabase.auth.admin.updateUserById(userId, {
            user_metadata: {
              full_name: updates.full_name,
              user_type: updates.user_type,
              phone: updates.phone
            }
          });
        } catch (authError) {
          console.warn('DataSync: Auth metadata update failed (non-critical):', authError);
        }
      }

      // 3. Emit real-time event for immediate UI updates
      const { realTimeService } = await import('./realTimeService');
      if (updates.is_active !== undefined) {
        realTimeService.emit('user-status-changed', {
          userId,
          isActive: updates.is_active,
          updatedUser: data
        });
      } else {
        realTimeService.emit('user-updated', { userId, updatedUser: data });
      }

      // 4. Notify subscribers
      this.notifySubscribers('users', { action: 'update', data });

      console.log('DataSync: User update completed with real-time sync');
      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating user:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete user with full synchronization (HARD DELETE with CASCADE handling)
   */
  async deleteUser(userId: string): Promise<SyncResult> {
    try {
      console.log('DataSync: Hard deleting user with cascade handling:', userId);

      // 1. Clean up related records that don't have CASCADE constraints
      console.log('DataSync: Cleaning up related records...');

      // Delete notifications
      await supabase.from('notifications').delete().eq('user_id', userId);

      // Delete wishlists
      await supabase.from('wishlists').delete().eq('customer_id', userId);

      // Delete product reviews
      await supabase.from('product_reviews').delete().eq('customer_id', userId);

      // Delete customer behavior tracking
      await supabase.from('customer_behavior').delete().eq('customer_id', userId);

      // Delete order templates
      await supabase.from('order_templates').delete().eq('customer_id', userId);

      console.log('DataSync: Related records cleaned up');

      // 2. HARD DELETE from database
      const { data, error } = await supabase
        .from('users')
        .delete()
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('DataSync: Database error during user deletion:', error);
        throw error;
      }

      console.log('DataSync: User hard-deleted successfully:', data);

      // 3. Delete from auth
      try {
        await supabase.auth.admin.deleteUser(userId);
        console.log('DataSync: User deleted from auth successfully');
      } catch (authError) {
        console.warn('DataSync: Auth deletion failed (non-critical):', authError);
      }

      // 4. Emit real-time event for immediate UI updates
      const { realTimeService } = await import('./realTimeService');
      realTimeService.emit('user-deleted', { userId, deletedUser: data });

      // 5. Notify subscribers
      this.notifySubscribers('users', { action: 'delete', data });

      console.log('DataSync: User deletion completed with real-time sync');
      return { success: true, data };
    } catch (error: any) {
      console.error('Error deleting user:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== PRODUCT OPERATIONS ====================

  /**
   * Create product with full synchronization
   */
  async createProduct(productData: {
    title: string;
    description?: string;
    price: number;
    stock: number;
    min_stock: number;
    category_id: string;
    featured_image?: string;
    sku?: string;
    branchId?: string;
  }): Promise<SyncResult> {
    try {
      console.log('DataSync: Creating product:', productData);

      // Separate branchId from product data since it's not a product table column
      const { branchId, ...productFields } = productData;

      const { data, error } = await supabase
        .from('products')
        .insert({
          ...productFields,
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select(`
          *,
          categories (
            id,
            name,
            color
          )
        `)
        .single();

      if (error) {
        console.error('DataSync: Database error during product creation:', error);
        throw error;
      }

      console.log('DataSync: Product created successfully:', data);

      // Create branch inventory record if branchId is provided
      if (branchId) {
        try {
          const { createBranchInventory } = await import('./branchInventoryService');
          await createBranchInventory(
            branchId,
            data.id,
            productFields.stock || 0,
            productFields.min_stock || 0
          );
          console.log('DataSync: Branch inventory created for product:', data.id, 'in branch:', branchId);
        } catch (branchError) {
          console.error('DataSync: Error creating branch inventory:', branchError);
          // Don't fail the entire operation if branch inventory creation fails
        }
      }

      // Emit real-time event for immediate UI updates
      const { realTimeService } = await import('./realTimeService');
      realTimeService.emit('product-added', { product: data });

      // Notify subscribers
      this.notifySubscribers('products', { action: 'create', data });

      console.log('DataSync: Product creation completed with real-time sync');
      return { success: true, data };
    } catch (error: any) {
      console.error('Error creating product:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update product with full synchronization
   */
  async updateProduct(productId: string, updates: any): Promise<SyncResult> {
    try {
      console.log('DataSync: Updating product:', { productId, updates });

      // Separate branchId from product updates since it's not a product table column
      const { branchId, ...productUpdates } = updates;

      const { data, error } = await supabase
        .from('products')
        .update({
          ...productUpdates,
          updated_at: new Date().toISOString()
        })
        .eq('id', productId)
        .select(`
          *,
          categories (
            id,
            name,
            color
          )
        `)
        .single();

      if (error) {
        console.error('DataSync: Database error during product update:', error);
        throw error;
      }

      console.log('DataSync: Product updated successfully:', data);

      // Handle branch inventory update if branchId is provided
      if (branchId && (productUpdates.stock !== undefined || productUpdates.min_stock !== undefined)) {
        try {
          const { updateBranchInventory } = await import('./branchInventoryService');
          await updateBranchInventory({
            branchId: branchId,
            productId: productId,
            stock: productUpdates.stock,
            minStock: productUpdates.min_stock
          });
          console.log('DataSync: Branch inventory updated for product:', productId, 'in branch:', branchId);
        } catch (branchError) {
          console.error('DataSync: Error updating branch inventory:', branchError);
          // Don't fail the entire operation if branch inventory update fails
        }
      }

      // Emit real-time event for immediate UI updates
      const { realTimeService } = await import('./realTimeService');
      realTimeService.emit('product-updated', { productId, product: data });

      // Notify subscribers
      this.notifySubscribers('products', { action: 'update', data });

      console.log('DataSync: Product update completed with real-time sync');
      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating product:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete product with full synchronization
   */
  async deleteProduct(productId: string): Promise<SyncResult> {
    try {
      console.log('DataSync: Soft deleting product:', productId);

      const { data, error } = await supabase
        .from('products')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', productId)
        .select()
        .single();

      if (error) {
        console.error('DataSync: Database error during product deletion:', error);
        throw error;
      }

      console.log('DataSync: Product soft-deleted successfully:', data);

      // Emit real-time event for immediate UI updates
      const { realTimeService } = await import('./realTimeService');
      realTimeService.emit('product-deleted', { productId, deletedProduct: data });

      // Notify subscribers
      this.notifySubscribers('products', { action: 'delete', data });

      console.log('DataSync: Product deletion completed with real-time sync');
      return { success: true, data };
    } catch (error: any) {
      console.error('Error deleting product:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== CATEGORY OPERATIONS ====================

  /**
   * Create category with full synchronization
   */
  async createCategory(categoryData: {
    name: string;
    description?: string;
    color: string;
    icon?: string;
  }): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .insert({
          ...categoryData,
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('categories', { action: 'create', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error creating category:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update category with full synchronization
   */
  async updateCategory(categoryId: string, updates: any): Promise<SyncResult> {
    try {
      console.log('dataSyncService.updateCategory: Starting update:', { categoryId, updates });

      // Map TypeScript field names to database column names
      const categoryUpdate: any = {
        updated_at: new Date().toISOString()
      };

      // Map common fields
      if (updates.name !== undefined) categoryUpdate.name = updates.name;
      if (updates.description !== undefined) categoryUpdate.description = updates.description;
      if (updates.color !== undefined) categoryUpdate.color = updates.color;
      if (updates.icon !== undefined) categoryUpdate.icon = updates.icon;
      if (updates.level !== undefined) categoryUpdate.level = updates.level;
      if (updates.sortOrder !== undefined) categoryUpdate.sort_order = updates.sortOrder;
      if (updates.parentId !== undefined) categoryUpdate.parent_id = updates.parentId;

      // Map isActive to is_active
      if (updates.isActive !== undefined) categoryUpdate.is_active = updates.isActive;

      console.log('dataSyncService.updateCategory: Database update payload:', categoryUpdate);

      const { data, error } = await supabase
        .from('categories')
        .update(categoryUpdate)
        .eq('id', categoryId)
        .select()
        .single();

      if (error) {
        console.error('dataSyncService.updateCategory: Database error:', error);
        throw error;
      }

      console.log('dataSyncService.updateCategory: Database update successful:', data);

      // Notify subscribers
      this.notifySubscribers('categories', { action: 'update', data });

      // Emit real-time event for immediate UI updates
      realTimeService.emit('category-updated', {
        categoryId,
        updates,
        data,
        timestamp: new Date().toISOString()
      });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating category:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete category with full synchronization
   */
  async deleteCategory(categoryId: string): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .update({ is_active: false })
        .eq('id', categoryId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('categories', { action: 'delete', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error deleting category:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== BRANCH OPERATIONS ====================

  /**
   * Create branch with full synchronization
   */
  async createBranch(branchData: {
    name: string;
    code: string;
    address: string;
    phone: string;
    email?: string;
    manager_id?: string;
    is_main_branch?: boolean;
  }): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .insert({
          ...branchData,
          is_active: true,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('branches', { action: 'create', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error creating branch:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update branch with full synchronization
   */
  async updateBranch(branchId: string, updates: any): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', branchId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('branches', { action: 'update', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating branch:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete branch with full synchronization
   */
  async deleteBranch(branchId: string): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .update({ is_active: false })
        .eq('id', branchId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('branches', { action: 'delete', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error deleting branch:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== ORDER OPERATIONS ====================

  /**
   * Create order with full synchronization
   */
  async createOrder(orderData: {
    customer_id: string;
    items: Array<{
      product_id: string;
      quantity: number;
      unit_price: number;
    }>;
    total: number;
    status?: string;
  }): Promise<SyncResult> {
    try {
      // Start transaction
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          customer_id: orderData.customer_id,
          order_number: `ORD-${Date.now()}`,
          total: orderData.total,
          status: orderData.status || 'pending',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      const orderItems = orderData.items.map(item => ({
        order_id: order.id,
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.quantity * item.unit_price
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) throw itemsError;

      // Update product stock
      for (const item of orderData.items) {
        await supabase.rpc('update_product_stock', {
          product_id: item.product_id,
          quantity_change: -item.quantity
        });
      }

      // Notify subscribers
      this.notifySubscribers('orders', { action: 'create', data: order });

      return { success: true, data: order };
    } catch (error: any) {
      console.error('Error creating order:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update order status with full synchronization
   */
  async updateOrderStatus(orderId: string, status: string): Promise<SyncResult> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) throw error;

      // Notify subscribers
      this.notifySubscribers('orders', { action: 'update', data });

      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating order:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== REAL-TIME SETUP ====================

  /**
   * Initialize real-time subscriptions for all tables
   */
  initializeRealTimeSync() {
    const tables = ['users', 'products', 'categories', 'branches', 'orders'];

    tables.forEach(table => {
      try {
        const channel = supabase
          .channel(`${table}-changes`)
          .on('postgres_changes',
            { event: '*', schema: 'public', table },
            (payload) => {
              try {
                console.log(`Real-time change in ${table}:`, payload);
                this.notifySubscribers(table, {
                  action: payload.eventType,
                  data: payload.new || payload.old
                });
              } catch (error) {
                console.error(`Error processing real-time change for ${table}:`, error);
              }
            }
          )
          .subscribe((status) => {
            console.log(`Subscription status for ${table}:`, status);
            if (status === 'SUBSCRIPTION_ERROR') {
              console.error(`Subscription error for ${table}`);
              // Attempt to resubscribe after a delay
              setTimeout(() => {
                console.log(`Attempting to resubscribe to ${table}...`);
                this.initializeTableSubscription(table);
              }, 5000);
            }
          });
      } catch (error) {
        console.error(`Error setting up subscription for ${table}:`, error);
      }
    });
  }

  /**
   * Initialize subscription for a single table (for retry logic)
   */
  private initializeTableSubscription(table: string) {
    try {
      supabase
        .channel(`${table}-changes-retry`)
        .on('postgres_changes',
          { event: '*', schema: 'public', table },
          (payload) => {
            try {
              console.log(`Real-time change in ${table} (retry):`, payload);
              this.notifySubscribers(table, {
                action: payload.eventType,
                data: payload.new || payload.old
              });
            } catch (error) {
              console.error(`Error processing real-time change for ${table} (retry):`, error);
            }
          }
        )
        .subscribe();
    } catch (error) {
      console.error(`Error in retry subscription for ${table}:`, error);
    }
  }
}

// Export singleton instance
export const dataSyncService = DataSyncService.getInstance();
export default dataSyncService;
