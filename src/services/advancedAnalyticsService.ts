
import { 
  CustomerSegment, 
  CustomerLifetimeValue, 
  ProfitMarginAnalysis, 
  RegionProfitability, 
  CategoryProfitability 
} from '../types/advancedAnalytics';

// Mock data for customer segments
const mockCustomerSegments: CustomerSegment[] = [
  {
    id: 'vip',
    name: 'VIP Customers',
    description: 'High-value customers with frequent purchases',
    customerCount: 45,
    totalRevenue: 125000,
    averageOrderValue: 890,
    criteria: { minSpend: 500, orderFrequency: 'weekly' },
    color: '#8B5CF6'
  },
  {
    id: 'regular',
    name: 'Regular Customers',
    description: 'Consistent customers with moderate spending',
    customerCount: 234,
    totalRevenue: 98000,
    averageOrderValue: 420,
    criteria: { minSpend: 100, maxSpend: 500, orderFrequency: 'monthly' },
    color: '#06B6D4'
  },
  {
    id: 'new',
    name: 'New Customers',
    description: 'Recently acquired customers',
    customerCount: 89,
    totalRevenue: 15000,
    averageOrderValue: 168,
    criteria: { lastOrderDays: 30 },
    color: '#10B981'
  },
  {
    id: 'at-risk',
    name: 'At-Risk Customers',
    description: 'Customers with declining engagement',
    customerCount: 67,
    totalRevenue: 23000,
    averageOrderValue: 343,
    criteria: { lastOrderDays: 90 },
    color: '#F59E0B'
  }
];

// Mock data for customer lifetime value
const mockCustomerLTV: CustomerLifetimeValue[] = [
  {
    customerId: 'CUST-001',
    customerName: 'Ahmed Mansouri',
    segment: 'VIP Customers',
    totalRevenue: 4250,
    totalOrders: 18,
    averageOrderValue: 236,
    firstOrderDate: '2023-01-15',
    lastOrderDate: '2024-06-10',
    predictedLifetimeValue: 8500,
    churnRisk: 'low',
    acquisitionCost: 45,
    profitMargin: 32.5
  },
  {
    customerId: 'CUST-002',
    customerName: 'Fatima El Amrani',
    segment: 'Regular Customers',
    totalRevenue: 2890,
    totalOrders: 12,
    averageOrderValue: 241,
    firstOrderDate: '2023-03-22',
    lastOrderDate: '2024-06-08',
    predictedLifetimeValue: 5200,
    churnRisk: 'low',
    acquisitionCost: 38,
    profitMargin: 28.7
  },
  {
    customerId: 'CUST-003',
    customerName: 'Youssef Benali',
    segment: 'At-Risk Customers',
    totalRevenue: 1650,
    totalOrders: 9,
    averageOrderValue: 183,
    firstOrderDate: '2023-06-10',
    lastOrderDate: '2024-03-15',
    predictedLifetimeValue: 2100,
    churnRisk: 'high',
    acquisitionCost: 52,
    profitMargin: 18.9
  }
];

// Mock data for profit margin analysis
const mockProfitMarginData: ProfitMarginAnalysis[] = [
  {
    productId: 'PRD-001',
    productName: 'Premium Ballpoint Pen Set',
    category: 'Writing Instruments',
    region: 'Casablanca',
    revenue: 2070,
    cost: 1242,
    grossProfit: 828,
    grossMargin: 40.0,
    unitsSold: 45,
    averageSellingPrice: 46,
    averageCost: 27.6
  },
  {
    productId: 'PRD-002',
    productName: 'A4 Notebook Pack',
    category: 'Paper & Notebooks',
    region: 'Rabat',
    revenue: 2880,
    cost: 1728,
    grossProfit: 1152,
    grossMargin: 40.0,
    unitsSold: 32,
    averageSellingPrice: 90,
    averageCost: 54
  },
  {
    productId: 'PRD-003',
    productName: 'Desk Organizer Set',
    category: 'Office Accessories',
    region: 'Marrakech',
    revenue: 3514,
    cost: 2460,
    grossProfit: 1054,
    grossMargin: 30.0,
    unitsSold: 28,
    averageSellingPrice: 125.5,
    averageCost: 87.9
  }
];

// Mock regional profitability data
const mockRegionalProfitability: RegionProfitability[] = [
  {
    region: 'Casablanca',
    totalRevenue: 45600,
    totalCost: 27360,
    grossProfit: 18240,
    grossMargin: 40.0,
    customerCount: 234,
    orderCount: 456
  },
  {
    region: 'Rabat',
    totalRevenue: 32400,
    totalCost: 20736,
    grossProfit: 11664,
    grossMargin: 36.0,
    customerCount: 167,
    orderCount: 312
  },
  {
    region: 'Marrakech',
    totalRevenue: 28900,
    totalCost: 19546,
    grossProfit: 9354,
    grossMargin: 32.4,
    customerCount: 145,
    orderCount: 278
  }
];

// Mock category profitability data
const mockCategoryProfitability: CategoryProfitability[] = [
  {
    category: 'Writing Instruments',
    totalRevenue: 35420,
    totalCost: 21252,
    grossProfit: 14168,
    grossMargin: 40.0,
    productCount: 45,
    unitsSold: 1256
  },
  {
    category: 'Paper & Notebooks',
    totalRevenue: 28900,
    totalCost: 18785,
    grossProfit: 10115,
    grossMargin: 35.0,
    productCount: 38,
    unitsSold: 987
  },
  {
    category: 'Office Accessories',
    totalRevenue: 22670,
    totalCost: 15869,
    grossProfit: 6801,
    grossMargin: 30.0,
    productCount: 52,
    unitsSold: 734
  }
];

export const getCustomerSegments = async (): Promise<CustomerSegment[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockCustomerSegments;
};

export const getCustomerLifetimeValue = async (): Promise<CustomerLifetimeValue[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockCustomerLTV;
};

export const getProfitMarginAnalysis = async (): Promise<ProfitMarginAnalysis[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockProfitMarginData;
};

export const getRegionalProfitability = async (): Promise<RegionProfitability[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockRegionalProfitability;
};

export const getCategoryProfitability = async (): Promise<CategoryProfitability[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockCategoryProfitability;
};
