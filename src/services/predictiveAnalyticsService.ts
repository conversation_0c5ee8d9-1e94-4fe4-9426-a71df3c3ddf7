
import { DemandForecast, ProfitabilityAnalysis } from '../types/predictiveAnalytics';

// Mock predictive analytics data
export const getDemandForecast = async (timeframe: 'week' | 'month' | 'quarter' = 'month'): Promise<DemandForecast[]> => {
  await new Promise(resolve => setTimeout(resolve, 1000));

  return [
    {
      productId: 'PRD-001',
      productName: 'Premium Ballpoint Pen Set',
      category: 'Writing Instruments',
      currentStock: 150,
      predictedDemand: {
        nextWeek: 45,
        nextMonth: 180,
        nextQuarter: 520
      },
      confidence: 0.85,
      seasonality: {
        pattern: 'seasonal',
        peakMonths: [8, 9, 1], // Back to school and New Year
        lowMonths: [6, 7, 12],
        seasonalityStrength: 0.7
      },
      trends: {
        direction: 'increasing',
        strength: 0.6,
        trendDuration: 6,
        changeRate: 15.2
      },
      recommendations: [
        'Increase stock by 25% before back-to-school season',
        'Consider bulk pricing for educational institutions',
        'Monitor competitor pricing trends'
      ]
    },
    {
      productId: 'PRD-002',
      productName: 'A4 Notebook Pack',
      category: 'Paper & Notebooks',
      currentStock: 8,
      predictedDemand: {
        nextWeek: 25,
        nextMonth: 95,
        nextQuarter: 280
      },
      confidence: 0.92,
      seasonality: {
        pattern: 'seasonal',
        peakMonths: [8, 9, 1, 2],
        lowMonths: [6, 7, 11, 12],
        seasonalityStrength: 0.8
      },
      trends: {
        direction: 'stable',
        strength: 0.3,
        trendDuration: 12,
        changeRate: 2.1
      },
      recommendations: [
        'Urgent restock needed - current stock below predicted demand',
        'Negotiate better supplier terms for regular orders',
        'Consider offering subscription model for regular customers'
      ]
    }
  ];
};

export const getProfitabilityAnalysis = async (period: string = 'monthly'): Promise<ProfitabilityAnalysis> => {
  await new Promise(resolve => setTimeout(resolve, 800));

  return {
    period,
    totalRevenue: 125430,
    totalCost: 78650,
    grossProfit: 46780,
    profitMargin: 37.3,
    byProduct: [
      {
        productId: 'PRD-001',
        productName: 'Premium Ballpoint Pen Set',
        revenue: 15420,
        cost: 8950,
        profit: 6470,
        margin: 42.0,
        unitsSold: 145,
        averageSellingPrice: 106.34
      },
      {
        productId: 'PRD-002',
        productName: 'A4 Notebook Pack',
        revenue: 12890,
        cost: 7890,
        profit: 5000,
        margin: 38.8,
        unitsSold: 89,
        averageSellingPrice: 144.84
      }
    ],
    byBranch: [
      {
        branchId: 'BR-001',
        branchName: 'Casablanca Main',
        revenue: 45670,
        cost: 28450,
        profit: 17220,
        margin: 37.7,
        operatingExpenses: 8500
      },
      {
        branchId: 'BR-002',
        branchName: 'Rabat Center',
        revenue: 38920,
        cost: 24200,
        profit: 14720,
        margin: 37.8,
        operatingExpenses: 7200
      }
    ],
    byCategory: [
      {
        category: 'Writing Instruments',
        revenue: 28450,
        cost: 16200,
        profit: 12250,
        margin: 43.1,
        unitsSold: 456,
        marketShare: 35.2
      },
      {
        category: 'Paper & Notebooks',
        revenue: 24890,
        cost: 15600,
        profit: 9290,
        margin: 37.3,
        unitsSold: 234,
        marketShare: 28.7
      }
    ]
  };
};

export const generatePredictiveReport = async (type: 'demand' | 'profitability' | 'inventory', format: 'pdf' | 'excel' | 'csv') => {
  console.log(`Generating ${type} report in ${format} format...`);
  
  // Simulate report generation
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  return {
    reportId: `RPT-${Date.now()}`,
    type,
    format,
    downloadUrl: `/reports/${type}-${Date.now()}.${format}`,
    generatedAt: new Date().toISOString(),
    fileSize: '2.4 MB'
  };
};
