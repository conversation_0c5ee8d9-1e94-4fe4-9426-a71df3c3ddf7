
import { APIEndpoint, Webhook, ImportExportJob, ERPIntegration } from '../types/integration';
import { generateId } from '../utils/inventoryUtils';

// Mock integration data
let apiEndpoints: APIEndpoint[] = [
  {
    id: 'API-001',
    name: 'Get Products',
    path: '/api/products',
    method: 'GET',
    description: 'Retrieve all products with optional filtering',
    authentication: 'api_key',
    parameters: [
      { name: 'category', type: 'string', required: false, description: 'Filter by category' },
      { name: 'limit', type: 'number', required: false, description: 'Maximum number of results' }
    ],
    responses: [
      { statusCode: 200, description: 'Success' },
      { statusCode: 401, description: 'Unauthorized' }
    ],
    isActive: true,
    rateLimit: 1000,
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  }
];

let webhooks: Webhook[] = [
  {
    id: 'WEBHOOK-001',
    name: 'Order Created',
    url: 'https://example.com/webhooks/order-created',
    events: [
      { type: 'order.created', description: 'Triggered when a new order is created', samplePayload: {} }
    ],
    isActive: true,
    retryAttempts: 3,
    timeout: 30,
    successCount: 45,
    failureCount: 2,
    createdAt: '2024-06-01T10:00:00Z'
  }
];

export const getAPIEndpoints = async (): Promise<APIEndpoint[]> => {
  return [...apiEndpoints];
};

export const createAPIEndpoint = async (endpointData: Omit<APIEndpoint, 'id' | 'createdAt' | 'updatedAt'>): Promise<APIEndpoint> => {
  const newEndpoint: APIEndpoint = {
    ...endpointData,
    id: generateId('API'),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  apiEndpoints.push(newEndpoint);
  return newEndpoint;
};

export const getWebhooks = async (): Promise<Webhook[]> => {
  return [...webhooks];
};

export const createWebhook = async (webhookData: Omit<Webhook, 'id' | 'createdAt' | 'successCount' | 'failureCount'>): Promise<Webhook> => {
  const newWebhook: Webhook = {
    ...webhookData,
    id: generateId('WEBHOOK'),
    successCount: 0,
    failureCount: 0,
    createdAt: new Date().toISOString()
  };

  webhooks.push(newWebhook);
  return newWebhook;
};

export const triggerWebhook = async (webhookId: string, payload: Record<string, any>): Promise<boolean> => {
  const webhook = webhooks.find(w => w.id === webhookId && w.isActive);
  if (!webhook) return false;

  try {
    console.log(`Triggering webhook ${webhook.name} to ${webhook.url}`, payload);
    
    // Simulate webhook call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    webhook.successCount++;
    webhook.lastTriggered = new Date().toISOString();
    return true;
  } catch (error) {
    webhook.failureCount++;
    return false;
  }
};

export const createImportJob = async (jobData: Omit<ImportExportJob, 'id' | 'createdAt' | 'recordsProcessed' | 'errors'>): Promise<ImportExportJob> => {
  const newJob: ImportExportJob = {
    ...jobData,
    id: generateId('IMPORT'),
    recordsProcessed: 0,
    errors: [],
    createdAt: new Date().toISOString()
  };

  console.log(`Starting ${jobData.type} job for ${jobData.entity}:`, newJob);
  return newJob;
};

export const createExportJob = async (entity: string, format: string, filters?: Record<string, any>): Promise<ImportExportJob> => {
  const job: ImportExportJob = {
    id: generateId('EXPORT'),
    type: 'export',
    entity: entity as any,
    format: format as any,
    status: 'pending',
    fileName: `${entity}_export_${Date.now()}.${format}`,
    recordsProcessed: 0,
    totalRecords: 100,
    errors: [],
    createdAt: new Date().toISOString()
  };

  console.log('Creating export job:', job);
  
  // Simulate processing
  setTimeout(() => {
    job.status = 'completed';
    job.recordsProcessed = 100;
    job.completedAt = new Date().toISOString();
  }, 3000);

  return job;
};

export const getERPIntegrations = async (): Promise<ERPIntegration[]> => {
  return [
    {
      id: 'ERP-001',
      name: 'SAP Integration',
      type: 'sap',
      connectionString: 'sap://server:port/client',
      isConnected: true,
      lastSync: '2024-06-14T10:00:00Z',
      syncFrequency: 'daily',
      mappings: [
        { localField: 'productId', erpField: 'MATNR', isRequired: true },
        { localField: 'productName', erpField: 'MAKTX', isRequired: true }
      ],
      settings: {
        timeout: 30,
        batchSize: 1000
      }
    }
  ];
};
