
import { Notification } from '../types/notification';
import { generateId } from '../utils/inventoryUtils';
import { supabase } from '../integrations/supabase/client';

// Real-time notifications service using Supabase
export const getUserNotifications = async (userId: string): Promise<Notification[]> => {
  try {
    console.log('NotificationService: Fetching notifications for user:', userId);

    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching notifications:', error);
      // Return sample notifications for now
      return getSampleNotifications(userId);
    }

    console.log('NotificationService: Fetched notifications:', data?.length);

    // Transform Supabase data to Notification interface
    return (data || []).map(row => ({
      id: row.id,
      userId: row.user_id,
      type: row.type,
      title: row.title,
      message: row.message,
      isRead: row.is_read,
      priority: row.priority,
      actionUrl: row.action_url,
      metadata: row.metadata,
      createdAt: row.created_at,
      readAt: row.read_at
    }));
  } catch (err) {
    console.error('Error in getUserNotifications:', err);
    // Return sample notifications as fallback
    return getSampleNotifications(userId);
  }
};

// Sample notifications for fallback
const getSampleNotifications = (userId: string): Notification[] => {
  const now = new Date();
  const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);
  const oneHourAgo = new Date(now.getTime() - 1 * 60 * 60 * 1000);
  const thirtyMinAgo = new Date(now.getTime() - 30 * 60 * 1000);

  return [
    {
      id: 'sample-001',
      userId: userId,
      type: 'order',
      title: 'New Order Received',
      message: 'Order #ORD-2025-001 has been placed by Ahmed Mansouri',
      isRead: false,
      priority: 'medium',
      actionUrl: '/orders/ORD-2025-001',
      metadata: { orderId: 'ORD-2025-001' },
      createdAt: thirtyMinAgo.toISOString()
    },
    {
      id: 'sample-002',
      userId: userId,
      type: 'stock',
      title: 'Low Stock Alert',
      message: 'Premium Pen Set is running low (3 units remaining)',
      isRead: false,
      priority: 'high',
      metadata: { productId: 'PROD-001', stockLevel: 3 },
      createdAt: oneHourAgo.toISOString()
    },
    {
      id: 'sample-003',
      userId: userId,
      type: 'system',
      title: 'System Update',
      message: 'YalaOffice system has been updated with new features',
      isRead: true,
      priority: 'low',
      metadata: {},
      createdAt: twoHoursAgo.toISOString(),
      readAt: oneHourAgo.toISOString()
    }
  ];
};

export const createNotification = async (notificationData: Omit<Notification, 'id' | 'createdAt'>): Promise<Notification> => {
  try {
    console.log('NotificationService: Creating notification:', notificationData);

    const { data, error } = await supabase
      .from('notifications')
      .insert({
        user_id: notificationData.userId,
        type: notificationData.type,
        title: notificationData.title,
        message: notificationData.message,
        is_read: notificationData.isRead || false,
        priority: notificationData.priority || 'medium',
        action_url: notificationData.actionUrl,
        metadata: notificationData.metadata || {},
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating notification:', error);
      throw error;
    }

    console.log('NotificationService: Notification created:', data);

    // Transform back to Notification interface
    return {
      id: data.id,
      userId: data.user_id,
      type: data.type,
      title: data.title,
      message: data.message,
      isRead: data.is_read,
      priority: data.priority,
      actionUrl: data.action_url,
      metadata: data.metadata,
      createdAt: data.created_at
    };
  } catch (err) {
    console.error('Error in createNotification:', err);
    throw err;
  }
};

export const markNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    console.log('NotificationService: Marking notification as read:', notificationId);

    const { error } = await supabase
      .from('notifications')
      .update({
        is_read: true,
        read_at: new Date().toISOString()
      })
      .eq('id', notificationId);

    if (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }

    console.log('NotificationService: Notification marked as read');
    return true;
  } catch (err) {
    console.error('Error in markNotificationAsRead:', err);
    return false;
  }
};

export const markAllNotificationsAsRead = async (userId: string): Promise<boolean> => {
  try {
    console.log('NotificationService: Marking all notifications as read for user:', userId);

    const { error } = await supabase
      .from('notifications')
      .update({
        is_read: true,
        read_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('is_read', false);

    if (error) {
      console.error('Error marking all notifications as read:', error);
      return false;
    }

    console.log('NotificationService: All notifications marked as read');
    return true;
  } catch (err) {
    console.error('Error in markAllNotificationsAsRead:', err);
    return false;
  }
};

export const deleteNotification = async (notificationId: string): Promise<boolean> => {
  const index = notifications.findIndex(n => n.id === notificationId);
  if (index === -1) return false;

  notifications.splice(index, 1);
  return true;
};

// Utility functions for creating specific notification types
export const createOrderNotification = async (userId: string, orderId: string, status: string) => {
  const titles = {
    confirmed: 'Order Confirmed',
    shipped: 'Order Shipped',
    delivered: 'Order Delivered',
    cancelled: 'Order Cancelled'
  };

  const messages = {
    confirmed: `Your order #${orderId} has been confirmed and is being processed.`,
    shipped: `Your order #${orderId} has been shipped and is on the way.`,
    delivered: `Your order #${orderId} has been delivered successfully.`,
    cancelled: `Your order #${orderId} has been cancelled.`
  };

  return createNotification({
    userId,
    type: 'order',
    title: titles[status as keyof typeof titles] || 'Order Update',
    message: messages[status as keyof typeof messages] || `Order #${orderId} status updated.`,
    isRead: false,
    priority: 'medium',
    actionUrl: `/orders/${orderId}`,
    metadata: { orderId }
  });
};

export const createStockAlert = async (userId: string, productTitle: string, stockLevel: number, productId: string) => {
  return createNotification({
    userId,
    type: 'stock',
    title: 'Low Stock Alert',
    message: `${productTitle} is running low (${stockLevel} units remaining)`,
    isRead: false,
    priority: stockLevel < 5 ? 'urgent' : 'high',
    metadata: { productId, stockLevel }
  });
};
