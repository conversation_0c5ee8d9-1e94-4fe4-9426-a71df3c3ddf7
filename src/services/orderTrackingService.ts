
import { OrderTracking, OrderTrackingStatus, BulkOrderOperation } from '../types/orderTracking';

let orderTrackings: OrderTracking[] = [];
let bulkOperations: BulkOrderOperation[] = [];

export const addOrderTracking = async (
  orderId: string,
  status: OrderTrackingStatus,
  location: string,
  updatedBy: string,
  notes?: string
): Promise<OrderTracking> => {
  const tracking: OrderTracking = {
    id: `TRK-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    orderId,
    status,
    location,
    updatedBy,
    notes,
    timestamp: new Date().toISOString()
  };

  orderTrackings.push(tracking);
  console.log('Order tracking added:', tracking);
  return tracking;
};

export const getOrderTracking = async (orderId: string): Promise<OrderTracking[]> => {
  return orderTrackings.filter(t => t.orderId === orderId);
};

export const createBulkOperation = async (
  type: BulkOrderOperation['type'],
  orderIds: string[],
  createdBy: string
): Promise<BulkOrderOperation> => {
  const operation: BulkOrderOperation = {
    id: `BULK-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
    type,
    orderIds,
    status: 'pending',
    createdBy,
    createdAt: new Date().toISOString()
  };

  bulkOperations.push(operation);
  console.log('Bulk operation created:', operation);
  return operation;
};

export const getBulkOperations = async (): Promise<BulkOrderOperation[]> => {
  return bulkOperations;
};
