
import { createNotification } from './notificationService';

interface EmailTemplate {
  subject: string;
  body: string;
}

interface EmailNotificationData {
  to: string[];
  template: EmailTemplate;
  data: Record<string, any>;
}

// Mock email templates
const EMAIL_TEMPLATES = {
  newOrder: {
    subject: 'New Order Received - Order #{orderId}',
    body: `
      <h2>New Order Notification</h2>
      <p>A new order has been placed:</p>
      <ul>
        <li><strong>Order ID:</strong> {orderId}</li>
        <li><strong>Customer:</strong> {customerName}</li>
        <li><strong>Total:</strong> {total} Dh</li>
        <li><strong>Payment Method:</strong> {paymentMethod}</li>
      </ul>
      <p>Please process this order promptly.</p>
    `
  },
  newUser: {
    subject: 'New User Registration - {userName}',
    body: `
      <h2>New User Registration</h2>
      <p>A new user has registered:</p>
      <ul>
        <li><strong>Name:</strong> {userName}</li>
        <li><strong>Email:</strong> {userEmail}</li>
        <li><strong>Type:</strong> {userType}</li>
        <li><strong>Registration Date:</strong> {registrationDate}</li>
      </ul>
    `
  },
  lowStock: {
    subject: 'Low Stock Alert - {productName}',
    body: `
      <h2>Low Stock Alert</h2>
      <p>The following product is running low on stock:</p>
      <ul>
        <li><strong>Product:</strong> {productName}</li>
        <li><strong>Current Stock:</strong> {currentStock}</li>
        <li><strong>Minimum Stock:</strong> {minStock}</li>
        <li><strong>Category:</strong> {category}</li>
      </ul>
      <p>Please restock this item as soon as possible.</p>
    `
  },
  paymentReceived: {
    subject: 'Payment Received - Order #{orderId}',
    body: `
      <h2>Payment Confirmation</h2>
      <p>Payment has been received for the following order:</p>
      <ul>
        <li><strong>Order ID:</strong> {orderId}</li>
        <li><strong>Amount:</strong> {amount} Dh</li>
        <li><strong>Payment Method:</strong> {paymentMethod}</li>
        <li><strong>Transaction ID:</strong> {transactionId}</li>
      </ul>
    `
  }
};

// Admin and manager email addresses (in a real app, these would come from user management)
const ADMIN_EMAILS = ['<EMAIL>', '<EMAIL>'];

const sendEmailNotification = async (notificationData: EmailNotificationData): Promise<boolean> => {
  try {
    // In a real application, this would integrate with an email service like SendGrid, Mailgun, etc.
    console.log('📧 Email Notification Sent:');
    console.log('To:', notificationData.to);
    console.log('Subject:', notificationData.template.subject);
    console.log('Body:', notificationData.template.body);
    
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return true;
  } catch (error) {
    console.error('Failed to send email:', error);
    return false;
  }
};

const replaceTemplateVariables = (template: string, data: Record<string, any>): string => {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return data[key] || match;
  });
};

export const sendNewOrderNotification = async (orderData: {
  orderId: string;
  customerName: string;
  total: number;
  paymentMethod: string;
}) => {
  const template = EMAIL_TEMPLATES.newOrder;
  const processedTemplate = {
    subject: replaceTemplateVariables(template.subject, orderData),
    body: replaceTemplateVariables(template.body, orderData)
  };

  await sendEmailNotification({
    to: ADMIN_EMAILS,
    template: processedTemplate,
    data: orderData
  });

  // Also create in-app notifications
  for (const email of ADMIN_EMAILS) {
    await createNotification({
      userId: 'admin-user', // In real app, get user ID from email
      type: 'order',
      title: 'New Order Received',
      message: `Order ${orderData.orderId} from ${orderData.customerName} - ${orderData.total} Dh`,
      isRead: false,
      priority: 'medium',
      metadata: { orderId: orderData.orderId }
    });
  }
};

export const sendNewUserNotification = async (userData: {
  userName: string;
  userEmail: string;
  userType: string;
  registrationDate: string;
}) => {
  const template = EMAIL_TEMPLATES.newUser;
  const processedTemplate = {
    subject: replaceTemplateVariables(template.subject, userData),
    body: replaceTemplateVariables(template.body, userData)
  };

  await sendEmailNotification({
    to: ADMIN_EMAILS,
    template: processedTemplate,
    data: userData
  });

  // Also create in-app notifications
  for (const email of ADMIN_EMAILS) {
    await createNotification({
      userId: 'admin-user',
      type: 'system',
      title: 'New User Registration',
      message: `${userData.userName} (${userData.userType}) has registered`,
      isRead: false,
      priority: 'low',
      metadata: { userEmail: userData.userEmail }
    });
  }
};

export const sendLowStockNotification = async (stockData: {
  productName: string;
  currentStock: number;
  minStock: number;
  category: string;
  productId: string;
}) => {
  const template = EMAIL_TEMPLATES.lowStock;
  const processedTemplate = {
    subject: replaceTemplateVariables(template.subject, stockData),
    body: replaceTemplateVariables(template.body, stockData)
  };

  await sendEmailNotification({
    to: ADMIN_EMAILS,
    template: processedTemplate,
    data: stockData
  });

  // Also create in-app notifications
  for (const email of ADMIN_EMAILS) {
    await createNotification({
      userId: 'admin-user',
      type: 'stock',
      title: 'Low Stock Alert',
      message: `${stockData.productName} is running low (${stockData.currentStock} units remaining)`,
      isRead: false,
      priority: stockData.currentStock < 5 ? 'urgent' : 'high',
      metadata: { productId: stockData.productId, stockLevel: stockData.currentStock }
    });
  }
};

export const sendPaymentNotification = async (paymentData: {
  orderId: string;
  amount: number;
  paymentMethod: string;
  transactionId: string;
}) => {
  const template = EMAIL_TEMPLATES.paymentReceived;
  const processedTemplate = {
    subject: replaceTemplateVariables(template.subject, paymentData),
    body: replaceTemplateVariables(template.body, paymentData)
  };

  await sendEmailNotification({
    to: ADMIN_EMAILS,
    template: processedTemplate,
    data: paymentData
  });

  // Also create in-app notifications
  for (const email of ADMIN_EMAILS) {
    await createNotification({
      userId: 'admin-user',
      type: 'order',
      title: 'Payment Received',
      message: `Payment of ${paymentData.amount} Dh received for order ${paymentData.orderId}`,
      isRead: false,
      priority: 'medium',
      metadata: { orderId: paymentData.orderId }
    });
  }
};
