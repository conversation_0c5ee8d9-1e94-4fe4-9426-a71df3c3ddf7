
import { AutomationRule, StockReorderRule, ApprovalWorkflow, NotificationTemplate } from '../types/automation';
import { generateId } from '../utils/inventoryUtils';

// Mock automation data
let automationRules: AutomationRule[] = [
  {
    id: 'AUTO-001',
    name: 'Low Stock Auto Reorder',
    type: 'stock_reorder',
    isActive: true,
    conditions: [
      { field: 'stock', operator: 'less_than', value: 20 }
    ],
    actions: [
      { type: 'reorder_stock', parameters: { quantity: 100, supplierId: 'SUP-001' } }
    ],
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  },
  {
    id: 'AUTO-002',
    name: 'Weekly Sales Report',
    type: 'scheduled_report',
    isActive: true,
    conditions: [],
    actions: [
      { type: 'generate_report', parameters: { type: 'sales', recipients: ['<EMAIL>'] } }
    ],
    schedule: {
      frequency: 'weekly',
      time: '09:00',
      dayOfWeek: 1
    },
    createdAt: '2024-06-01T10:00:00Z',
    updatedAt: '2024-06-01T10:00:00Z'
  }
];

let stockReorderRules: StockReorderRule[] = [
  {
    id: 'REORDER-001',
    productId: 'PRD-001',
    minStockLevel: 20,
    reorderQuantity: 100,
    supplierId: 'SUP-001',
    isActive: true
  },
  {
    id: 'REORDER-002',
    productId: 'PRD-002',
    minStockLevel: 50,
    reorderQuantity: 200,
    supplierId: 'SUP-002',
    isActive: true
  }
];

let approvalWorkflows: ApprovalWorkflow[] = [
  {
    id: 'APPROVAL-001',
    name: 'Large Order Approval',
    type: 'order',
    threshold: 5000,
    approvers: ['<EMAIL>', '<EMAIL>'],
    requiredApprovals: 1,
    isActive: true
  }
];

export const getAutomationRules = async (): Promise<AutomationRule[]> => {
  return [...automationRules];
};

export const createAutomationRule = async (ruleData: Omit<AutomationRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<AutomationRule> => {
  const newRule: AutomationRule = {
    ...ruleData,
    id: generateId('AUTO'),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  automationRules.push(newRule);
  return newRule;
};

export const updateAutomationRule = async (id: string, updates: Partial<AutomationRule>): Promise<AutomationRule | null> => {
  const index = automationRules.findIndex(rule => rule.id === id);
  if (index === -1) return null;

  automationRules[index] = {
    ...automationRules[index],
    ...updates,
    updatedAt: new Date().toISOString()
  };

  return automationRules[index];
};

export const getStockReorderRules = async (): Promise<StockReorderRule[]> => {
  return [...stockReorderRules];
};

export const createStockReorderRule = async (ruleData: Omit<StockReorderRule, 'id'>): Promise<StockReorderRule> => {
  const newRule: StockReorderRule = {
    ...ruleData,
    id: generateId('REORDER')
  };

  stockReorderRules.push(newRule);
  return newRule;
};

export const triggerAutomatedReorder = async (productId: string): Promise<boolean> => {
  const rule = stockReorderRules.find(r => r.productId === productId && r.isActive);
  if (!rule) return false;

  console.log(`Triggering automated reorder for product ${productId}: ${rule.reorderQuantity} units`);
  
  // Update last triggered
  rule.lastTriggered = new Date().toISOString();
  
  return true;
};

export const getApprovalWorkflows = async (): Promise<ApprovalWorkflow[]> => {
  return [...approvalWorkflows];
};

export const createApprovalWorkflow = async (workflowData: Omit<ApprovalWorkflow, 'id'>): Promise<ApprovalWorkflow> => {
  const newWorkflow: ApprovalWorkflow = {
    ...workflowData,
    id: generateId('APPROVAL')
  };

  approvalWorkflows.push(newWorkflow);
  return newWorkflow;
};

export const checkApprovalRequired = async (type: 'order' | 'transfer' | 'discount', amount: number): Promise<boolean> => {
  const workflow = approvalWorkflows.find(w => w.type === type && w.isActive && amount >= w.threshold);
  return !!workflow;
};

export const sendAutomatedNotification = async (type: string, recipientId: string, data: Record<string, any>) => {
  console.log(`Sending automated ${type} notification to ${recipientId}:`, data);
  
  // Simulate notification sending
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    notificationId: generateId('NOTIF'),
    status: 'sent',
    sentAt: new Date().toISOString()
  };
};
