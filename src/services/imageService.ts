/**
 * Image Service for YalaOffice
 * Handles image uploads, URL generation, and fallback images
 */

import { supabase } from '../integrations/supabase/client';

export interface ImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export class ImageService {
  private static readonly BUCKET_NAME = 'product-images';
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  // Alternative: Use base64 encoding for images if Supabase storage is not available
  private static readonly USE_BASE64_FALLBACK = false; // Set to false once storage is working

  /**
   * Generate a fallback image URL using UI Avatars
   */
  static generateFallbackImage(text: string, size: number = 400): string {
    const encodedText = encodeURIComponent(text);
    return `https://ui-avatars.com/api/?name=${encodedText}&background=f97316&color=fff&size=${size}&bold=true`;
  }

  /**
   * Get the best available image URL with fallback
   */
  static getBestImageUrl(product: any, size: number = 400): string {
    // Try featured image first
    if (product.featuredImage && product.featuredImage !== '/placeholder.svg') {
      return product.featuredImage;
    }
    
    // Try main image
    if (product.image && product.image !== '/placeholder.svg') {
      return product.image;
    }
    
    // Try first thumbnail image
    if (product.thumbnailImages && product.thumbnailImages.length > 0) {
      const firstThumbnail = product.thumbnailImages.find((img: string) => img && img !== '/placeholder.svg');
      if (firstThumbnail) {
        return firstThumbnail;
      }
    }
    
    // Return fallback image
    return this.generateFallbackImage(product.title || 'Product', size);
  }

  /**
   * Get all available images for a product
   */
  static getAllProductImages(product: any): string[] {
    const images: string[] = [];
    
    // Add featured image
    if (product.featuredImage && product.featuredImage !== '/placeholder.svg') {
      images.push(product.featuredImage);
    }
    
    // Add main image if different from featured
    if (product.image && product.image !== '/placeholder.svg' && product.image !== product.featuredImage) {
      images.push(product.image);
    }
    
    // Add thumbnail images
    if (product.thumbnailImages && product.thumbnailImages.length > 0) {
      product.thumbnailImages.forEach((img: string) => {
        if (img && img !== '/placeholder.svg' && !images.includes(img)) {
          images.push(img);
        }
      });
    }
    
    // If no images, add fallback
    if (images.length === 0) {
      images.push(this.generateFallbackImage(product.title || 'Product'));
    }
    
    return images;
  }

  /**
   * Validate image file
   */
  static validateImageFile(file: File): { valid: boolean; error?: string } {
    if (!file) {
      return { valid: false, error: 'No file provided' };
    }

    if (file.size > this.MAX_FILE_SIZE) {
      return { valid: false, error: 'File size must be less than 5MB' };
    }

    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return { valid: false, error: 'File must be a JPEG, PNG, or WebP image' };
    }

    return { valid: true };
  }

  /**
   * Check if storage bucket exists and create if needed
   */
  static async ensureBucketExists(): Promise<{ success: boolean; error?: string }> {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await supabase.storage.listBuckets();

      if (listError) {
        console.error('ImageService: Error listing buckets:', listError);
        return { success: false, error: `Failed to check buckets: ${listError.message}` };
      }

      const bucketExists = buckets?.some(bucket => bucket.id === this.BUCKET_NAME);

      if (!bucketExists) {
        console.log('ImageService: Bucket does not exist, attempting to create...');

        // Try to create bucket
        const { data: newBucket, error: createError } = await supabase.storage.createBucket(this.BUCKET_NAME, {
          public: true,
          fileSizeLimit: this.MAX_FILE_SIZE,
          allowedMimeTypes: this.ALLOWED_TYPES
        });

        if (createError) {
          console.error('ImageService: Error creating bucket:', createError);
          return {
            success: false,
            error: `Bucket '${this.BUCKET_NAME}' does not exist and could not be created. Please create it manually in Supabase Dashboard > Storage. Error: ${createError.message}`
          };
        }

        console.log('ImageService: Bucket created successfully:', newBucket);
      }

      return { success: true };
    } catch (error) {
      console.error('ImageService: Exception checking bucket:', error);
      return {
        success: false,
        error: `Failed to verify storage bucket: ${(error as Error).message}. Please ensure the '${this.BUCKET_NAME}' bucket exists in Supabase Dashboard > Storage.`
      };
    }
  }

  /**
   * Convert image file to base64 data URL (fallback method)
   */
  static async convertToBase64(file: File): Promise<ImageUploadResult> {
    return new Promise((resolve) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        const base64Url = event.target?.result as string;
        console.log('ImageService: Image converted to base64');
        resolve({ success: true, url: base64Url });
      };

      reader.onerror = (error) => {
        console.error('ImageService: Error converting to base64:', error);
        resolve({ success: false, error: 'Failed to process image file' });
      };

      reader.readAsDataURL(file);
    });
  }

  /**
   * Upload image to Supabase storage with base64 fallback
   */
  static async uploadImage(file: File, path: string): Promise<ImageUploadResult> {
    try {
      // Validate file
      const validation = this.validateImageFile(file);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Try Supabase storage first
      if (!this.USE_BASE64_FALLBACK) {
        // Ensure bucket exists
        const bucketCheck = await this.ensureBucketExists();
        if (!bucketCheck.success) {
          console.log('ImageService: Bucket check failed, falling back to base64');
          return await this.convertToBase64(file);
        }

        // Generate unique filename
        const fileExt = file.name.split('.').pop();
        const fileName = `${path}/${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;

        console.log('ImageService: Uploading image to Supabase storage:', fileName);

        // Upload to Supabase storage
        const { data, error } = await supabase.storage
          .from(this.BUCKET_NAME)
          .upload(fileName, file, {
            cacheControl: '3600',
            upsert: false
          });

        if (error) {
          console.error('ImageService: Supabase upload error, falling back to base64:', error);
          return await this.convertToBase64(file);
        }

        // Get public URL
        const { data: urlData } = supabase.storage
          .from(this.BUCKET_NAME)
          .getPublicUrl(fileName);

        console.log('ImageService: Supabase upload successful:', urlData.publicUrl);
        return { success: true, url: urlData.publicUrl };
      } else {
        // Use base64 fallback directly
        console.log('ImageService: Using base64 fallback for image storage');
        return await this.convertToBase64(file);
      }

    } catch (error) {
      console.error('ImageService: Upload exception:', error);
      return {
        success: false,
        error: `Image upload failed: ${(error as Error).message}. Please check your Supabase storage configuration.`
      };
    }
  }

  /**
   * Upload multiple images
   */
  static async uploadMultipleImages(files: File[], basePath: string): Promise<ImageUploadResult[]> {
    const results: ImageUploadResult[] = [];
    
    for (const file of files) {
      const result = await this.uploadImage(file, basePath);
      results.push(result);
    }
    
    return results;
  }

  /**
   * Delete image from Supabase storage
   */
  static async deleteImage(imageUrl: string): Promise<boolean> {
    try {
      // Extract file path from URL
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split('/');
      const fileName = pathParts[pathParts.length - 1];
      
      if (!fileName) {
        console.error('ImageService: Could not extract filename from URL:', imageUrl);
        return false;
      }

      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([fileName]);

      if (error) {
        console.error('ImageService: Delete error:', error);
        return false;
      }

      console.log('ImageService: Image deleted successfully:', fileName);
      return true;

    } catch (error) {
      console.error('ImageService: Delete exception:', error);
      return false;
    }
  }

  /**
   * Create optimized image URL with transformations
   */
  static createOptimizedImageUrl(baseUrl: string, width?: number, height?: number, quality: number = 80): string {
    // If it's already a fallback image, return as-is
    if (baseUrl.includes('ui-avatars.com')) {
      return baseUrl;
    }

    // For Supabase storage URLs, we can add transformation parameters
    try {
      const url = new URL(baseUrl);
      const params = new URLSearchParams();
      
      if (width) params.set('width', width.toString());
      if (height) params.set('height', height.toString());
      if (quality !== 80) params.set('quality', quality.toString());
      
      if (params.toString()) {
        url.search = params.toString();
      }
      
      return url.toString();
    } catch {
      // If URL parsing fails, return original
      return baseUrl;
    }
  }

  /**
   * Preload images for better performance
   */
  static preloadImages(imageUrls: string[]): Promise<void[]> {
    return Promise.all(
      imageUrls.map(url => 
        new Promise<void>((resolve) => {
          const img = new Image();
          img.onload = () => resolve();
          img.onerror = () => resolve(); // Resolve even on error to not block
          img.src = url;
        })
      )
    );
  }
}

export default ImageService;
