import { Category } from '../types/management';
import { realTimeService, syncProductData } from './realTimeService';

// Mock data for categories
let categories: Category[] = [
  {
    id: 'CAT-001',
    name: 'Writing Instruments',
    description: 'Pens, pencils, markers, and other writing tools',
    level: 0,
    isActive: true,
    sortOrder: 1,
    icon: 'pen',
    color: '#3B82F6',
    productCount: 45,
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z',
    createdBy: 'admin',
    updatedBy: 'admin'
  },
  {
    id: 'CAT-002',
    name: 'Ballpoint Pens',
    description: 'Various types of ballpoint pens',
    parentId: 'CAT-001',
    level: 1,
    isActive: true,
    sortOrder: 1,
    icon: 'pen',
    color: '#3B82F6',
    productCount: 15,
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z',
    createdBy: 'admin',
    updatedBy: 'admin'
  },
  {
    id: 'CAT-003',
    name: 'Paper & Notebooks',
    description: 'All types of paper products and notebooks',
    level: 0,
    isActive: true,
    sortOrder: 2,
    icon: 'book',
    color: '#10B981',
    productCount: 32,
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z',
    createdBy: 'admin',
    updatedBy: 'admin'
  },
  {
    id: 'CAT-004',
    name: 'Office & Desk Accessories',
    description: 'Desk organizers, staplers, and office accessories',
    level: 0,
    isActive: true,
    sortOrder: 3,
    icon: 'briefcase',
    color: '#F59E0B',
    productCount: 28,
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z',
    createdBy: 'admin',
    updatedBy: 'admin'
  },
  {
    id: 'CAT-005',
    name: 'Art & Craft Supplies',
    description: 'Creative supplies for art and craft projects',
    level: 0,
    isActive: true,
    sortOrder: 4,
    icon: 'palette',
    color: '#EF4444',
    productCount: 19,
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-01T10:00:00Z',
    createdBy: 'admin',
    updatedBy: 'admin'
  }
];

// Category service functions
export const getCategories = async (): Promise<Category[]> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  return [...categories];
};

export const getCategoryById = async (id: string): Promise<Category | null> => {
  await new Promise(resolve => setTimeout(resolve, 100));
  return categories.find(cat => cat.id === id) || null;
};

export const getCategoriesByParent = async (parentId?: string): Promise<Category[]> => {
  await new Promise(resolve => setTimeout(resolve, 150));
  return categories.filter(cat => cat.parentId === parentId);
};

export const getCategoryHierarchy = async (): Promise<Category[]> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const buildHierarchy = (parentId?: string): Category[] => {
    return categories
      .filter(cat => cat.parentId === parentId)
      .sort((a, b) => a.sortOrder - b.sortOrder)
      .map(cat => ({
        ...cat,
        children: buildHierarchy(cat.id)
      }));
  };
  
  return buildHierarchy();
};

export const createCategory = async (categoryData: Omit<Category, 'id' | 'createdAt' | 'updatedAt' | 'productCount'>, userId: string): Promise<Category> => {
  await new Promise(resolve => setTimeout(resolve, 300));
  
  const newCategory: Category = {
    ...categoryData,
    id: `CAT-${String(categories.length + 1).padStart(3, '0')}`,
    productCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: userId,
    updatedBy: userId
  };
  
  categories.push(newCategory);
  
  // Emit real-time event
  realTimeService.emit('category-created', {
    category: newCategory,
    userId
  });
  
  console.log('Category created:', newCategory);
  return newCategory;
};

export const updateCategory = async (id: string, updates: Partial<Category>, userId: string): Promise<Category | null> => {
  await new Promise(resolve => setTimeout(resolve, 250));
  
  const index = categories.findIndex(cat => cat.id === id);
  if (index === -1) return null;
  
  const oldCategory = categories[index];
  categories[index] = {
    ...categories[index],
    ...updates,
    updatedAt: new Date().toISOString(),
    updatedBy: userId
  };
  
  // Emit real-time event
  realTimeService.emit('category-updated', {
    categoryId: id,
    oldData: oldCategory,
    newData: categories[index],
    userId
  });
  
  console.log('Category updated:', categories[index]);
  return categories[index];
};

export const deleteCategory = async (id: string, userId: string): Promise<boolean> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  const category = categories.find(cat => cat.id === id);
  if (!category) return false;
  
  // Check if category has children
  const hasChildren = categories.some(cat => cat.parentId === id);
  if (hasChildren) {
    throw new Error('Cannot delete category with subcategories. Please delete or move subcategories first.');
  }
  
  // Check if category has products
  if (category.productCount > 0) {
    throw new Error('Cannot delete category with products. Please move or delete products first.');
  }
  
  categories = categories.filter(cat => cat.id !== id);
  
  // Emit real-time event
  realTimeService.emit('category-deleted', {
    categoryId: id,
    category,
    userId
  });
  
  console.log('Category deleted:', id);
  return true;
};

export const reorderCategories = async (categoryIds: string[], userId: string): Promise<Category[]> => {
  await new Promise(resolve => setTimeout(resolve, 200));
  
  categoryIds.forEach((id, index) => {
    const category = categories.find(cat => cat.id === id);
    if (category) {
      category.sortOrder = index + 1;
      category.updatedAt = new Date().toISOString();
      category.updatedBy = userId;
    }
  });
  
  // Emit real-time event
  realTimeService.emit('categories-reordered', {
    categoryIds,
    userId
  });
  
  console.log('Categories reordered:', categoryIds);
  return categories.sort((a, b) => a.sortOrder - b.sortOrder);
};

export const updateCategoryProductCount = async (categoryId: string, count: number): Promise<void> => {
  const category = categories.find(cat => cat.id === categoryId);
  if (category) {
    category.productCount = count;
    category.updatedAt = new Date().toISOString();
    
    // Also update parent category counts
    if (category.parentId) {
      const parentCategory = categories.find(cat => cat.id === category.parentId);
      if (parentCategory) {
        const siblingCount = categories
          .filter(cat => cat.parentId === category.parentId)
          .reduce((sum, cat) => sum + cat.productCount, 0);
        parentCategory.productCount = siblingCount;
        parentCategory.updatedAt = new Date().toISOString();
      }
    }
  }
};

export const searchCategories = async (query: string): Promise<Category[]> => {
  await new Promise(resolve => setTimeout(resolve, 150));
  
  const searchTerm = query.toLowerCase();
  return categories.filter(cat => 
    cat.name.toLowerCase().includes(searchTerm) ||
    cat.description.toLowerCase().includes(searchTerm)
  );
};

export const getCategoryStats = async (): Promise<{
  total: number;
  active: number;
  inactive: number;
  withProducts: number;
  empty: number;
}> => {
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return {
    total: categories.length,
    active: categories.filter(cat => cat.isActive).length,
    inactive: categories.filter(cat => !cat.isActive).length,
    withProducts: categories.filter(cat => cat.productCount > 0).length,
    empty: categories.filter(cat => cat.productCount === 0).length
  };
};

export default {
  getCategories,
  getCategoryById,
  getCategoriesByParent,
  getCategoryHierarchy,
  createCategory,
  updateCategory,
  deleteCategory,
  reorderCategories,
  updateCategoryProductCount,
  searchCategories,
  getCategoryStats
};
