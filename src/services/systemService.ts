
import { SystemConfig, AuditLog, BackupJob, SystemHealth } from '../types/system';
import { generateId } from '../utils/inventoryUtils';

// Mock system data
let systemConfigs: SystemConfig[] = [
  {
    id: 'CONFIG-001',
    category: 'general',
    key: 'app_name',
    value: 'YalaOffice',
    description: 'Application name displayed in the interface',
    dataType: 'string',
    isEditable: true,
    requiresRestart: false,
    updatedAt: '2024-06-01T10:00:00Z',
    updatedBy: '<EMAIL>'
  },
  {
    id: 'CONFIG-002',
    category: 'security',
    key: 'session_timeout',
    value: 3600,
    description: 'Session timeout in seconds',
    dataType: 'number',
    isEditable: true,
    requiresRestart: true,
    updatedAt: '2024-06-01T10:00:00Z',
    updatedBy: '<EMAIL>'
  }
];

let auditLogs: AuditLog[] = [
  {
    id: 'AUDIT-001',
    userId: 'USR-001',
    userName: 'Admin User',
    action: 'UPDATE',
    entity: 'Product',
    entityId: 'PRD-001',
    changes: [
      { field: 'price', oldValue: 100, newValue: 120 }
    ],
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0',
    timestamp: '2024-06-14T10:00:00Z',
    severity: 'medium'
  }
];

export const getSystemConfigs = async (category?: string): Promise<SystemConfig[]> => {
  if (category) {
    return systemConfigs.filter(config => config.category === category);
  }
  return [...systemConfigs];
};

export const updateSystemConfig = async (id: string, value: any): Promise<SystemConfig | null> => {
  const config = systemConfigs.find(c => c.id === id);
  if (!config || !config.isEditable) return null;

  config.value = value;
  config.updatedAt = new Date().toISOString();
  
  return config;
};

export const getAuditLogs = async (filters?: {
  userId?: string;
  entity?: string;
  action?: string;
  startDate?: string;
  endDate?: string;
}): Promise<AuditLog[]> => {
  let filteredLogs = [...auditLogs];

  if (filters) {
    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }
    if (filters.entity) {
      filteredLogs = filteredLogs.filter(log => log.entity === filters.entity);
    }
    if (filters.action) {
      filteredLogs = filteredLogs.filter(log => log.action === filters.action);
    }
  }

  return filteredLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

export const createAuditLog = async (logData: Omit<AuditLog, 'id' | 'timestamp'>): Promise<AuditLog> => {
  const newLog: AuditLog = {
    ...logData,
    id: generateId('AUDIT'),
    timestamp: new Date().toISOString()
  };

  auditLogs.push(newLog);
  return newLog;
};

export const createBackup = async (type: 'full' | 'incremental' | 'differential' = 'full'): Promise<BackupJob> => {
  const backup: BackupJob = {
    id: generateId('BACKUP'),
    type,
    status: 'running',
    fileName: `backup_${type}_${Date.now()}.zip`,
    startedAt: new Date().toISOString()
  };

  console.log('Starting backup:', backup);

  // Simulate backup process
  setTimeout(() => {
    backup.status = 'completed';
    backup.fileSize = Math.floor(Math.random() * 1000000) + 500000; // Random size
    backup.duration = Math.floor(Math.random() * 300) + 60; // Random duration
    backup.completedAt = new Date().toISOString();
  }, 5000);

  return backup;
};

export const getBackupHistory = async (): Promise<BackupJob[]> => {
  return [
    {
      id: 'BACKUP-001',
      type: 'full',
      status: 'completed',
      fileName: 'backup_full_1718366400.zip',
      fileSize: 1250000,
      duration: 180,
      startedAt: '2024-06-14T08:00:00Z',
      completedAt: '2024-06-14T08:03:00Z'
    },
    {
      id: 'BACKUP-002',
      type: 'incremental',
      status: 'completed',
      fileName: 'backup_incremental_1718452800.zip',
      fileSize: 450000,
      duration: 45,
      startedAt: '2024-06-15T08:00:00Z',
      completedAt: '2024-06-15T08:00:45Z'
    }
  ];
};

export const getSystemHealth = async (): Promise<SystemHealth> => {
  return {
    status: 'healthy',
    uptime: 86400000, // 24 hours in milliseconds
    lastChecked: new Date().toISOString(),
    metrics: [
      { name: 'CPU Usage', value: 35, unit: '%', threshold: 80, status: 'normal' },
      { name: 'Memory Usage', value: 62, unit: '%', threshold: 85, status: 'normal' },
      { name: 'Disk Usage', value: 78, unit: '%', threshold: 90, status: 'warning' },
      { name: 'Response Time', value: 120, unit: 'ms', threshold: 500, status: 'normal' }
    ],
    alerts: [
      {
        id: 'ALERT-001',
        type: 'warning',
        message: 'Disk usage approaching threshold',
        severity: 'medium',
        timestamp: '2024-06-14T12:00:00Z',
        isResolved: false
      }
    ]
  };
};

export const restoreBackup = async (backupId: string): Promise<boolean> => {
  console.log(`Starting restore from backup: ${backupId}`);
  
  // Simulate restore process
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  return true;
};
