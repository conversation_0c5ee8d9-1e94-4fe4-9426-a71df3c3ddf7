import { supabase } from '../integrations/supabase/client';
import { realTimeService } from './realTimeService';

export interface BranchInventory {
  id: string;
  branchId: string;
  productId: string;
  stock: number;
  minStock: number;
  maxStock?: number;
  reservedStock: number;
  lastUpdated: string;
  branchName?: string;
  branchCode?: string;
  productTitle?: string;
  productSku?: string;
}

export interface BranchInventoryUpdate {
  branchId: string;
  productId: string;
  stock?: number;
  minStock?: number;
  maxStock?: number;
  reservedStock?: number;
}

/**
 * Get branch inventory for a specific branch
 */
export const getBranchInventory = async (branchId: string): Promise<BranchInventory[]> => {
  try {
    const { data, error } = await supabase
      .from('branch_inventory')
      .select(`
        *,
        branches (
          name,
          code
        ),
        products (
          title,
          sku,
          featured_image
        )
      `)
      .eq('branch_id', branchId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching branch inventory:', error);
      throw error;
    }

    return data?.map(item => ({
      id: item.id,
      branchId: item.branch_id,
      productId: item.product_id,
      stock: item.stock || 0,
      minStock: item.min_stock || 0,
      maxStock: item.max_stock,
      reservedStock: item.reserved_stock || 0,
      lastUpdated: item.updated_at || item.created_at,
      branchName: item.branches?.name,
      branchCode: item.branches?.code,
      productTitle: item.products?.title,
      productSku: item.products?.sku
    })) || [];
  } catch (error) {
    console.error('Error in getBranchInventory:', error);
    return [];
  }
};

/**
 * Get the primary branch assignment for a product (first branch in inventory)
 */
export const getProductPrimaryBranch = async (productId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('branch_inventory')
      .select('branch_id')
      .eq('product_id', productId)
      .limit(1)
      .single();

    if (error) {
      console.error('Error fetching product primary branch:', error);
      return null;
    }

    return data?.branch_id || null;
  } catch (error) {
    console.error('Error in getProductPrimaryBranch:', error);
    return null;
  }
};

/**
 * Get inventory for a specific product across all branches
 */
export const getProductInventoryAcrossBranches = async (productId: string): Promise<BranchInventory[]> => {
  try {
    const { data, error } = await supabase
      .from('branch_inventory')
      .select(`
        *,
        branches (
          name,
          code,
          address
        )
      `)
      .eq('product_id', productId)
      .order('stock', { ascending: false });

    if (error) {
      console.error('Error fetching product inventory across branches:', error);
      throw error;
    }

    return data?.map(item => ({
      id: item.id,
      branchId: item.branch_id,
      productId: item.product_id,
      stock: item.stock || 0,
      minStock: item.min_stock || 0,
      maxStock: item.max_stock,
      reservedStock: item.reserved_stock || 0,
      lastUpdated: item.updated_at || item.created_at,
      branchName: item.branches?.name,
      branchCode: item.branches?.code
    })) || [];
  } catch (error) {
    console.error('Error in getProductInventoryAcrossBranches:', error);
    return [];
  }
};

/**
 * Update branch inventory
 */
export const updateBranchInventory = async (update: BranchInventoryUpdate): Promise<boolean> => {
  try {
    console.log('Updating branch inventory:', update);

    const { error } = await supabase
      .from('branch_inventory')
      .upsert({
        branch_id: update.branchId,
        product_id: update.productId,
        stock: update.stock,
        min_stock: update.minStock,
        max_stock: update.maxStock,
        reserved_stock: update.reservedStock,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'branch_id,product_id'
      });

    if (error) {
      console.error('Error updating branch inventory:', error);
      throw error;
    }

    // Emit real-time event for immediate UI updates
    realTimeService.emit('branch-inventory-updated', {
      branchId: update.branchId,
      productId: update.productId,
      update
    });

    console.log('Branch inventory updated successfully');
    return true;
  } catch (error) {
    console.error('Error in updateBranchInventory:', error);
    return false;
  }
};

/**
 * Create branch inventory record
 */
export const createBranchInventory = async (
  branchId: string,
  productId: string,
  stock: number = 0,
  minStock: number = 0
): Promise<boolean> => {
  try {
    console.log('Creating branch inventory:', { branchId, productId, stock, minStock });

    const { error } = await supabase
      .from('branch_inventory')
      .insert({
        branch_id: branchId,
        product_id: productId,
        stock,
        min_stock: minStock,
        reserved_stock: 0
      });

    if (error) {
      console.error('Error creating branch inventory:', error);
      throw error;
    }

    // Emit real-time event
    realTimeService.emit('branch-inventory-created', {
      branchId,
      productId,
      stock,
      minStock
    });

    console.log('Branch inventory created successfully');
    return true;
  } catch (error) {
    console.error('Error in createBranchInventory:', error);
    return false;
  }
};

/**
 * Delete branch inventory record
 */
export const deleteBranchInventory = async (branchId: string, productId: string): Promise<boolean> => {
  try {
    console.log('Deleting branch inventory:', { branchId, productId });

    const { error } = await supabase
      .from('branch_inventory')
      .delete()
      .eq('branch_id', branchId)
      .eq('product_id', productId);

    if (error) {
      console.error('Error deleting branch inventory:', error);
      throw error;
    }

    // Emit real-time event
    realTimeService.emit('branch-inventory-deleted', {
      branchId,
      productId
    });

    console.log('Branch inventory deleted successfully');
    return true;
  } catch (error) {
    console.error('Error in deleteBranchInventory:', error);
    return false;
  }
};

/**
 * Sync product stock across all branches when product is updated
 */
export const syncProductStockAcrossBranches = async (
  productId: string,
  newStock: number,
  newMinStock: number
): Promise<boolean> => {
  try {
    console.log('Syncing product stock across branches:', { productId, newStock, newMinStock });

    // Get all branches that have this product
    const { data: branchInventories, error } = await supabase
      .from('branch_inventory')
      .select('branch_id')
      .eq('product_id', productId);

    if (error) {
      console.error('Error fetching branch inventories for sync:', error);
      return false;
    }

    // Update stock for all branches
    if (branchInventories && branchInventories.length > 0) {
      const { error: updateError } = await supabase
        .from('branch_inventory')
        .update({
          stock: newStock,
          min_stock: newMinStock,
          updated_at: new Date().toISOString()
        })
        .eq('product_id', productId);

      if (updateError) {
        console.error('Error syncing stock across branches:', updateError);
        return false;
      }

      // Emit real-time events for each branch
      branchInventories.forEach(inventory => {
        realTimeService.emit('branch-inventory-synced', {
          branchId: inventory.branch_id,
          productId,
          newStock,
          newMinStock
        });
      });
    }

    console.log('Product stock synced across all branches successfully');
    return true;
  } catch (error) {
    console.error('Error in syncProductStockAcrossBranches:', error);
    return false;
  }
};

/**
 * Get low stock items for a branch
 */
export const getLowStockItems = async (branchId: string): Promise<BranchInventory[]> => {
  try {
    const { data, error } = await supabase
      .from('branch_inventory')
      .select(`
        *,
        branches (
          name,
          code
        ),
        products (
          title,
          sku,
          featured_image
        )
      `)
      .eq('branch_id', branchId)
      .filter('stock', 'lte', 'min_stock')
      .order('stock', { ascending: true });

    if (error) {
      console.error('Error fetching low stock items:', error);
      throw error;
    }

    return data?.map(item => ({
      id: item.id,
      branchId: item.branch_id,
      productId: item.product_id,
      stock: item.stock || 0,
      minStock: item.min_stock || 0,
      maxStock: item.max_stock,
      reservedStock: item.reserved_stock || 0,
      lastUpdated: item.updated_at || item.created_at,
      branchName: item.branches?.name,
      branchCode: item.branches?.code,
      productTitle: item.products?.title,
      productSku: item.products?.sku
    })) || [];
  } catch (error) {
    console.error('Error in getLowStockItems:', error);
    return [];
  }
};
