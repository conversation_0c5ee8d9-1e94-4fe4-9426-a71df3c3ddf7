/**
 * System Health Monitoring Service for YalaOffice
 * Provides comprehensive system monitoring, performance tracking, and health alerts
 */

import { supabase } from '../integrations/supabase/client';

// System Health interfaces
export interface SystemMetrics {
  timestamp: string;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkLatency: number;
  activeConnections: number;
  databaseConnections: number;
  responseTime: number;
}

export interface PerformanceAlert {
  id: string;
  type: 'high_cpu' | 'high_memory' | 'high_disk' | 'slow_response' | 'database_slow' | 'connection_limit';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  threshold: number;
  currentValue: number;
  timestamp: string;
  resolved: boolean;
  resolvedAt?: string;
}

export interface BackupStatus {
  id: string;
  type: 'full' | 'incremental' | 'differential';
  status: 'running' | 'completed' | 'failed' | 'scheduled';
  startTime: string;
  endTime?: string;
  size?: number;
  location: string;
  error?: string;
}

export interface DatabaseHealth {
  status: 'healthy' | 'warning' | 'critical';
  connectionCount: number;
  maxConnections: number;
  queryPerformance: {
    averageResponseTime: number;
    slowQueries: number;
    failedQueries: number;
  };
  storage: {
    totalSize: number;
    usedSize: number;
    freeSize: number;
    usagePercentage: number;
  };
  replication: {
    status: 'active' | 'inactive' | 'error';
    lag: number;
  };
}

export interface SystemHealthOverview {
  overallStatus: 'healthy' | 'warning' | 'critical';
  uptime: number;
  lastChecked: string;
  systemMetrics: SystemMetrics;
  databaseHealth: DatabaseHealth;
  activeAlerts: PerformanceAlert[];
  recentBackups: BackupStatus[];
  performanceTrends: SystemMetrics[];
}

// System Health Monitoring Service
export class SystemHealthService {
  
  // Get current system metrics
  static async getCurrentMetrics(): Promise<SystemMetrics> {
    try {
      const startTime = Date.now();
      
      // Test database response time
      const { error: dbError } = await supabase
        .from('users')
        .select('count(*)')
        .limit(1);

      const responseTime = Date.now() - startTime;

      // Mock system metrics (in production, these would come from system monitoring)
      return {
        timestamp: new Date().toISOString(),
        cpuUsage: Math.random() * 80 + 10, // 10-90%
        memoryUsage: Math.random() * 70 + 20, // 20-90%
        diskUsage: Math.random() * 60 + 30, // 30-90%
        networkLatency: Math.random() * 50 + 10, // 10-60ms
        activeConnections: Math.floor(Math.random() * 100) + 20,
        databaseConnections: Math.floor(Math.random() * 50) + 5,
        responseTime: dbError ? 5000 : responseTime
      };
    } catch (error) {
      console.error('Error getting current metrics:', error);
      return {
        timestamp: new Date().toISOString(),
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        networkLatency: 0,
        activeConnections: 0,
        databaseConnections: 0,
        responseTime: 0
      };
    }
  }

  // Store system metrics
  static async storeMetrics(metrics: SystemMetrics): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('system_metrics')
        .insert({
          timestamp: metrics.timestamp,
          cpu_usage: metrics.cpuUsage,
          memory_usage: metrics.memoryUsage,
          disk_usage: metrics.diskUsage,
          network_latency: metrics.networkLatency,
          active_connections: metrics.activeConnections,
          database_connections: metrics.databaseConnections,
          response_time: metrics.responseTime
        });

      if (error) {
        console.error('Error storing metrics:', error);
        return false;
      }

      // Check for performance alerts
      await this.checkPerformanceThresholds(metrics);
      
      return true;
    } catch (error) {
      console.error('Error in storeMetrics:', error);
      return false;
    }
  }

  // Get historical metrics
  static async getHistoricalMetrics(hours: number = 24): Promise<SystemMetrics[]> {
    try {
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000).toISOString();
      
      const { data, error } = await supabase
        .from('system_metrics')
        .select('*')
        .gte('timestamp', startTime)
        .order('timestamp', { ascending: true });

      if (error) {
        console.error('Error fetching historical metrics:', error);
        return [];
      }

      return data?.map(metric => ({
        timestamp: metric.timestamp,
        cpuUsage: metric.cpu_usage,
        memoryUsage: metric.memory_usage,
        diskUsage: metric.disk_usage,
        networkLatency: metric.network_latency,
        activeConnections: metric.active_connections,
        databaseConnections: metric.database_connections,
        responseTime: metric.response_time
      })) || [];
    } catch (error) {
      console.error('Error in getHistoricalMetrics:', error);
      return [];
    }
  }

  // Create performance alert
  static async createPerformanceAlert(alert: Omit<PerformanceAlert, 'id' | 'timestamp' | 'resolved'>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('performance_alerts')
        .insert({
          type: alert.type,
          title: alert.title,
          description: alert.description,
          severity: alert.severity,
          threshold: alert.threshold,
          current_value: alert.currentValue,
          resolved: false
        });

      if (error) {
        console.error('Error creating performance alert:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in createPerformanceAlert:', error);
      return false;
    }
  }

  // Get performance alerts
  static async getPerformanceAlerts(resolved: boolean = false): Promise<PerformanceAlert[]> {
    try {
      const { data, error } = await supabase
        .from('performance_alerts')
        .select('*')
        .eq('resolved', resolved)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching performance alerts:', error);
        return [];
      }

      return data?.map(alert => ({
        id: alert.id,
        type: alert.type,
        title: alert.title,
        description: alert.description,
        severity: alert.severity,
        threshold: alert.threshold,
        currentValue: alert.current_value,
        timestamp: alert.created_at,
        resolved: alert.resolved,
        resolvedAt: alert.resolved_at
      })) || [];
    } catch (error) {
      console.error('Error in getPerformanceAlerts:', error);
      return [];
    }
  }

  // Resolve performance alert
  static async resolvePerformanceAlert(alertId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('performance_alerts')
        .update({
          resolved: true,
          resolved_at: new Date().toISOString()
        })
        .eq('id', alertId);

      if (error) {
        console.error('Error resolving performance alert:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in resolvePerformanceAlert:', error);
      return false;
    }
  }

  // Get database health
  static async getDatabaseHealth(): Promise<DatabaseHealth> {
    try {
      const startTime = Date.now();
      
      // Test database connection and performance
      const { error: dbError, count } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      const responseTime = Date.now() - startTime;

      // Mock database health data (in production, these would come from database monitoring)
      return {
        status: dbError ? 'critical' : responseTime > 1000 ? 'warning' : 'healthy',
        connectionCount: Math.floor(Math.random() * 50) + 10,
        maxConnections: 100,
        queryPerformance: {
          averageResponseTime: responseTime,
          slowQueries: Math.floor(Math.random() * 5),
          failedQueries: dbError ? 1 : 0
        },
        storage: {
          totalSize: **********, // 1GB in bytes
          usedSize: Math.floor(Math.random() * 600000000) + 200000000, // 200MB-800MB
          freeSize: 0, // Will be calculated
          usagePercentage: 0 // Will be calculated
        },
        replication: {
          status: 'active',
          lag: Math.random() * 100 // milliseconds
        }
      };
    } catch (error) {
      console.error('Error getting database health:', error);
      return {
        status: 'critical',
        connectionCount: 0,
        maxConnections: 100,
        queryPerformance: {
          averageResponseTime: 0,
          slowQueries: 0,
          failedQueries: 1
        },
        storage: {
          totalSize: 0,
          usedSize: 0,
          freeSize: 0,
          usagePercentage: 0
        },
        replication: {
          status: 'error',
          lag: 0
        }
      };
    }
  }

  // Get backup status
  static async getBackupStatus(): Promise<BackupStatus[]> {
    try {
      const { data, error } = await supabase
        .from('backup_logs')
        .select('*')
        .order('start_time', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error fetching backup status:', error);
        return [];
      }

      return data?.map(backup => ({
        id: backup.id,
        type: backup.type,
        status: backup.status,
        startTime: backup.start_time,
        endTime: backup.end_time,
        size: backup.size,
        location: backup.location,
        error: backup.error
      })) || [];
    } catch (error) {
      console.error('Error in getBackupStatus:', error);
      return [];
    }
  }

  // Get system health overview
  static async getSystemHealthOverview(): Promise<SystemHealthOverview> {
    try {
      const currentMetrics = await this.getCurrentMetrics();
      const databaseHealth = await this.getDatabaseHealth();
      const activeAlerts = await this.getPerformanceAlerts(false);
      const recentBackups = await this.getBackupStatus();
      const performanceTrends = await this.getHistoricalMetrics(24);

      // Calculate overall status
      let overallStatus: 'healthy' | 'warning' | 'critical' = 'healthy';
      
      if (databaseHealth.status === 'critical' || activeAlerts.some(a => a.severity === 'critical')) {
        overallStatus = 'critical';
      } else if (databaseHealth.status === 'warning' || activeAlerts.some(a => a.severity === 'high')) {
        overallStatus = 'warning';
      }

      return {
        overallStatus,
        uptime: Date.now() - (new Date('2024-01-01').getTime()), // Mock uptime
        lastChecked: new Date().toISOString(),
        systemMetrics: currentMetrics,
        databaseHealth,
        activeAlerts,
        recentBackups,
        performanceTrends
      };
    } catch (error) {
      console.error('Error getting system health overview:', error);
      return {
        overallStatus: 'critical',
        uptime: 0,
        lastChecked: new Date().toISOString(),
        systemMetrics: await this.getCurrentMetrics(),
        databaseHealth: await this.getDatabaseHealth(),
        activeAlerts: [],
        recentBackups: [],
        performanceTrends: []
      };
    }
  }

  // Check performance thresholds and create alerts
  private static async checkPerformanceThresholds(metrics: SystemMetrics): Promise<void> {
    try {
      // CPU usage threshold
      if (metrics.cpuUsage > 80) {
        await this.createPerformanceAlert({
          type: 'high_cpu',
          title: 'High CPU Usage',
          description: `CPU usage is at ${metrics.cpuUsage.toFixed(1)}%`,
          severity: metrics.cpuUsage > 90 ? 'critical' : 'high',
          threshold: 80,
          currentValue: metrics.cpuUsage
        });
      }

      // Memory usage threshold
      if (metrics.memoryUsage > 85) {
        await this.createPerformanceAlert({
          type: 'high_memory',
          title: 'High Memory Usage',
          description: `Memory usage is at ${metrics.memoryUsage.toFixed(1)}%`,
          severity: metrics.memoryUsage > 95 ? 'critical' : 'high',
          threshold: 85,
          currentValue: metrics.memoryUsage
        });
      }

      // Disk usage threshold
      if (metrics.diskUsage > 90) {
        await this.createPerformanceAlert({
          type: 'high_disk',
          title: 'High Disk Usage',
          description: `Disk usage is at ${metrics.diskUsage.toFixed(1)}%`,
          severity: metrics.diskUsage > 95 ? 'critical' : 'high',
          threshold: 90,
          currentValue: metrics.diskUsage
        });
      }

      // Response time threshold
      if (metrics.responseTime > 2000) {
        await this.createPerformanceAlert({
          type: 'slow_response',
          title: 'Slow Response Time',
          description: `Database response time is ${metrics.responseTime}ms`,
          severity: metrics.responseTime > 5000 ? 'critical' : 'medium',
          threshold: 2000,
          currentValue: metrics.responseTime
        });
      }
    } catch (error) {
      console.error('Error checking performance thresholds:', error);
    }
  }

  // Start automated monitoring
  static startMonitoring(intervalMinutes: number = 5): void {
    setInterval(async () => {
      try {
        const metrics = await this.getCurrentMetrics();
        await this.storeMetrics(metrics);
      } catch (error) {
        console.error('Error in automated monitoring:', error);
      }
    }, intervalMinutes * 60 * 1000);
  }
}

export default SystemHealthService;
