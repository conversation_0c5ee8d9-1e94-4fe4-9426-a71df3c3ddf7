import { Wishlist, ProductReview, CustomerBehavior } from '../types/customer';
import { generateId } from '../utils/inventoryUtils';

// Mock data storage
let wishlists: Wishlist[] = [];
let reviews: ProductReview[] = [];
let customerBehaviors: CustomerBehavior[] = [];

// Wishlist Management
export const addToWishlist = async (customerId: string, productId: number): Promise<Wishlist> => {
  const existingItem = wishlists.find(w => w.customerId === customerId && w.productId === productId);
  if (existingItem) {
    throw new Error('Product already in wishlist');
  }

  const wishlistItem: Wishlist = {
    id: generateId('WISH'),
    customerId,
    productId,
    addedAt: new Date().toISOString()
  };

  wishlists.push(wishlistItem);
  return wishlistItem;
};

export const removeFromWishlist = async (customerId: string, productId: number): Promise<boolean> => {
  const index = wishlists.findIndex(w => w.customerId === customerId && w.productId === productId);
  if (index === -1) return false;

  wishlists.splice(index, 1);
  return true;
};

export const getWishlist = async (customerId: string): Promise<Wishlist[]> => {
  return wishlists.filter(w => w.customerId === customerId);
};

export const isInWishlist = async (customerId: string, productId: number): Promise<boolean> => {
  return wishlists.some(w => w.customerId === customerId && w.productId === productId);
};

// Reviews and Ratings
export const addReview = async (
  productId: number,
  customerId: string,
  customerName: string,
  rating: number,
  title?: string,
  comment?: string
): Promise<ProductReview> => {
  const existingReview = reviews.find(r => r.productId === productId && r.customerId === customerId);
  if (existingReview) {
    throw new Error('Customer has already reviewed this product');
  }

  const review: ProductReview = {
    id: generateId('REV'),
    productId,
    customerId,
    customerName,
    rating: Math.max(1, Math.min(5, rating)), // Ensure 1-5 range
    title,
    comment,
    verified: false, // Would check purchase history in real app
    helpful: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  reviews.push(review);
  return review;
};

export const getProductReviews = async (productId: number): Promise<ProductReview[]> => {
  return reviews
    .filter(r => r.productId === productId)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};

export const getProductRating = async (productId: number): Promise<{ averageRating: number; totalReviews: number }> => {
  const productReviews = await getProductReviews(productId);
  
  if (productReviews.length === 0) {
    return { averageRating: 0, totalReviews: 0 };
  }

  const totalRating = productReviews.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = totalRating / productReviews.length;

  return {
    averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
    totalReviews: productReviews.length
  };
};

export const markReviewHelpful = async (reviewId: string): Promise<void> => {
  const review = reviews.find(r => r.id === reviewId);
  if (review) {
    review.helpful += 1;
  }
};

// Customer Behavior and Recommendations
export const trackProductView = async (customerId: string, productId: number): Promise<void> => {
  let behavior = customerBehaviors.find(b => b.customerId === customerId);
  
  if (!behavior) {
    behavior = {
      customerId,
      viewedProducts: [],
      purchaseHistory: [],
      searchHistory: [],
      categoryPreferences: {}
    };
    customerBehaviors.push(behavior);
  }

  // Add to viewed products (keep last 50)
  behavior.viewedProducts = behavior.viewedProducts.filter(id => id !== productId);
  behavior.viewedProducts.unshift(productId);
  behavior.viewedProducts = behavior.viewedProducts.slice(0, 50);
};

export const getRecommendations = async (customerId: string): Promise<number[]> => {
  const behavior = customerBehaviors.find(b => b.customerId === customerId);
  if (!behavior) return [];

  // Simple recommendation: return recently viewed products from other customers
  // In a real app, this would use more sophisticated algorithms
  const otherCustomers = customerBehaviors.filter(b => b.customerId !== customerId);
  const recommendations = new Set<number>();

  // Get products viewed by customers with similar purchase history
  for (const other of otherCustomers) {
    const commonProducts = behavior.viewedProducts.filter(id => 
      other.viewedProducts.includes(id) || 
      other.purchaseHistory.some(p => p.productId === id)
    );

    if (commonProducts.length > 0) {
      other.viewedProducts.slice(0, 10).forEach(id => recommendations.add(id));
    }
  }

  return Array.from(recommendations).slice(0, 20);
};

export const getRecentlyViewed = async (customerId: string): Promise<number[]> => {
  const behavior = customerBehaviors.find(b => b.customerId === customerId);
  return behavior?.viewedProducts.slice(0, 10) || [];
};
