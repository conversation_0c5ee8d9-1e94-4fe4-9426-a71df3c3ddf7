// Real-time data synchronization service
// This service provides real-time updates across all user types and components

export type EventType =
  | 'product-updated'
  | 'product-added'
  | 'product-deleted'
  | 'stock-updated'
  | 'order-created'
  | 'order-updated'
  | 'order-status-changed'
  | 'order-deleted'
  | 'order-assigned'
  | 'delivery-assigned'
  | 'delivery-updated'
  | 'user-updated'
  | 'inventory-changed'
  | 'price-updated'
  | 'image-updated'
  | 'category-created'
  | 'category-updated'
  | 'category-deleted'
  | 'categories-reordered'
  | 'branch-created'
  | 'branch-updated'
  | 'branch-deleted'
  | 'branch-status-changed'
  | 'transfer-created'
  | 'transfer-updated'
  | 'transfer-status-changed'
  | 'inventory-changed'
  | 'user-created'
  | 'user-updated'
  | 'user-deleted'
  | 'user-status-changed'
  | 'client-created'
  | 'client-updated'
  | 'client-deleted'
  | 'statistics-updated';

export interface RealTimeEvent {
  type: EventType;
  data: any;
  timestamp: string;
  userId?: string;
  userType?: string;
}

type EventListener = (event: RealTimeEvent) => void;

class RealTimeService {
  private listeners: Map<EventType, Set<EventListener>> = new Map();
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000;

  constructor() {
    this.initializeConnection();
  }

  private initializeConnection() {
    // In a real implementation, this would establish a WebSocket connection
    // For now, we'll simulate real-time updates using a polling mechanism
    this.isConnected = true;
    console.log('Real-time service initialized');
    
    // Simulate connection events
    this.startHeartbeat();
  }

  private startHeartbeat() {
    // Simulate periodic connection checks
    setInterval(() => {
      if (!this.isConnected) {
        this.attemptReconnect();
      }
    }, 5000);
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.isConnected = true;
        this.reconnectAttempts = 0;
        console.log('Reconnected to real-time service');
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  // Subscribe to specific event types
  subscribe(eventType: EventType, listener: EventListener): () => void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    
    this.listeners.get(eventType)!.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.get(eventType)?.delete(listener);
    };
  }

  // Subscribe to multiple event types
  subscribeToMultiple(eventTypes: EventType[], listener: EventListener): () => void {
    const unsubscribeFunctions = eventTypes.map(eventType => 
      this.subscribe(eventType, listener)
    );
    
    return () => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }

  // Emit events to all subscribers
  emit(eventType: EventType, data: any, userId?: string, userType?: string) {
    const event: RealTimeEvent = {
      type: eventType,
      data,
      timestamp: new Date().toISOString(),
      userId,
      userType
    };

    const listeners = this.listeners.get(eventType);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in real-time event listener:', error);
        }
      });
    }

    // Log event for debugging
    console.log('Real-time event emitted:', event);
  }

  // Broadcast to all event types (for critical updates)
  broadcast(data: any, userId?: string, userType?: string) {
    const allEventTypes: EventType[] = [
      'product-updated',
      'product-added',
      'product-deleted',
      'stock-updated',
      'order-created',
      'order-updated',
      'order-status-changed',
      'order-deleted',
      'order-assigned',
      'delivery-assigned',
      'delivery-updated',
      'user-updated',
      'inventory-changed',
      'price-updated',
      'image-updated',
      'category-created',
      'category-updated',
      'category-deleted',
      'branch-created',
      'branch-updated',
      'branch-deleted',
      'branch-status-changed',
      'transfer-created',
      'transfer-updated',
      'transfer-status-changed',
      'user-created',
      'user-updated',
      'user-deleted',
      'user-status-changed',
      'client-created',
      'client-updated',
      'client-deleted',
      'statistics-updated'
    ];

    allEventTypes.forEach(eventType => {
      this.emit(eventType, data, userId, userType);
    });
  }

  // Get connection status
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  // Manually disconnect
  disconnect() {
    this.isConnected = false;
    console.log('Disconnected from real-time service');
  }

  // Manually reconnect
  reconnect() {
    this.reconnectAttempts = 0;
    this.initializeConnection();
  }
}

// Create singleton instance
export const realTimeService = new RealTimeService();

// React hook for using real-time updates
export const useRealTimeUpdates = (
  eventTypes: EventType | EventType[],
  callback: (event: RealTimeEvent) => void,
  dependencies: any[] = []
) => {
  const eventTypesArray = Array.isArray(eventTypes) ? eventTypes : [eventTypes];
  
  // This would be implemented as a React hook in a real application
  // For now, we'll provide the subscription mechanism
  return {
    subscribe: () => realTimeService.subscribeToMultiple(eventTypesArray, callback),
    isConnected: realTimeService.getConnectionStatus()
  };
};

// Utility functions for common real-time operations
export const syncProductData = (productId: string, updatedData: any, userId?: string) => {
  realTimeService.emit('product-updated', { productId, ...updatedData }, userId);
};

export const syncInventoryData = (productId: string, stockData: any, userId?: string) => {
  realTimeService.emit('stock-updated', { productId, ...stockData }, userId);
  realTimeService.emit('inventory-changed', { productId, ...stockData }, userId);
};

export const syncOrderData = (orderId: string, orderData: any, userId?: string) => {
  realTimeService.emit('order-updated', { orderId, ...orderData }, userId);
};

export const syncUserData = (userId: string, userData: any) => {
  realTimeService.emit('user-updated', { userId, ...userData }, userId);
};

export const syncPriceData = (productId: string, priceData: any, userId?: string) => {
  realTimeService.emit('price-updated', { productId, ...priceData }, userId);
};

export const syncImageData = (productId: string, imageData: any, userId?: string) => {
  realTimeService.emit('image-updated', { productId, ...imageData }, userId);
};

export const syncBranchData = (branchId: string, branchData: any, userId?: string) => {
  realTimeService.emit('branch-updated', { branchId, ...branchData }, userId);
};

export const syncTransferData = (transferId: string, transferData: any, userId?: string) => {
  realTimeService.emit('transfer-updated', { transferId, ...transferData }, userId);
};

export const syncBranchStatus = (branchId: string, isActive: boolean, userId?: string) => {
  realTimeService.emit('branch-status-changed', { branchId, isActive }, userId);
};

export const syncOrderCreated = (orderId: string, orderData: any, userId?: string) => {
  realTimeService.emit('order-created', { orderId, order: orderData }, userId);
  realTimeService.emit('statistics-updated', { type: 'order-created', orderId }, userId);
};

export const syncOrderStatusChanged = (orderId: string, oldStatus: string, newStatus: string, userId?: string) => {
  realTimeService.emit('order-status-changed', { orderId, oldStatus, newStatus }, userId);
  realTimeService.emit('statistics-updated', { type: 'order-status-changed', orderId, newStatus }, userId);
};

export const syncDeliveryAssigned = (orderId: string, deliveryPersonId: string, deliveryPersonName?: string, userId?: string) => {
  realTimeService.emit('delivery-assigned', { orderId, deliveryPersonId, deliveryPersonName }, userId);
  realTimeService.emit('order-updated', { orderId, deliveryPersonId, deliveryPersonName }, userId);
  realTimeService.emit('statistics-updated', { type: 'delivery-assigned', orderId, deliveryPersonId }, userId);
};

export const syncOrderDeleted = (orderId: string, orderData: any, userId?: string) => {
  realTimeService.emit('order-deleted', { orderId, order: orderData }, userId);
  realTimeService.emit('statistics-updated', { type: 'order-deleted', orderId }, userId);
};

export const syncOrderUpdated = (orderId: string, orderData: any, userId?: string) => {
  realTimeService.emit('order-updated', { orderId, order: orderData }, userId);
  realTimeService.emit('statistics-updated', { type: 'order-updated', orderId }, userId);
};

// User synchronization functions
export const syncUserCreated = (userData: any, userId?: string) => {
  realTimeService.emit('user-created', { user: userData }, userId);
  realTimeService.emit('users-updated', { type: 'user-created', user: userData }, userId);
  realTimeService.emit('statistics-updated', { type: 'user-created', userId: userData.id }, userId);
};

export const syncUserUpdated = (userData: any, userId?: string) => {
  realTimeService.emit('user-updated', { user: userData }, userId);
  realTimeService.emit('users-updated', { type: 'user-updated', user: userData }, userId);
  realTimeService.emit('statistics-updated', { type: 'user-updated', userId: userData.id }, userId);
};

export const syncUserStatusChanged = (userData: any, oldStatus: string, newStatus: string, userId?: string) => {
  realTimeService.emit('user-status-changed', { user: userData, oldStatus, newStatus }, userId);
  realTimeService.emit('users-updated', { type: 'user-status-changed', user: userData }, userId);
  realTimeService.emit('statistics-updated', { type: 'user-status-changed', userId: userData.id }, userId);
};

export const syncUserRoleChanged = (userData: any, oldRole: string, newRole: string, userId?: string) => {
  realTimeService.emit('user-role-changed', { user: userData, oldRole, newRole }, userId);
  realTimeService.emit('users-updated', { type: 'user-role-changed', user: userData }, userId);
  realTimeService.emit('statistics-updated', { type: 'user-role-changed', userId: userData.id }, userId);

  // Special handling for delivery personnel changes
  if (oldRole === 'delivery' || newRole === 'delivery') {
    realTimeService.emit('delivery-personnel-updated', { user: userData, oldRole, newRole }, userId);
  }
};

export const syncUserDeleted = (userData: any, userId?: string) => {
  realTimeService.emit('user-deleted', { user: userData }, userId);
  realTimeService.emit('users-updated', { type: 'user-deleted', user: userData }, userId);
  realTimeService.emit('statistics-updated', { type: 'user-deleted', userId: userData.id }, userId);
};

// Enhanced event manager for inventory with real-time sync
export class EnhancedInventoryEventManager {
  private realTimeService: RealTimeService;

  constructor() {
    this.realTimeService = realTimeService;
  }

  emit(eventType: EventType, data: any, userId?: string, userType?: string) {
    // Emit to local listeners and real-time service
    this.realTimeService.emit(eventType, data, userId, userType);
  }

  subscribe(eventType: EventType, listener: EventListener) {
    return this.realTimeService.subscribe(eventType, listener);
  }
}

export const enhancedInventoryEventManager = new EnhancedInventoryEventManager();

export default realTimeService;
