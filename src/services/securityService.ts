
/**
 * Advanced Security Service for YalaOffice
 * Provides comprehensive security management, monitoring, and audit capabilities
 */

import { supabase } from '../integrations/supabase/client';

// Security interfaces
export interface SecurityEvent {
  id: string;
  eventType: 'login' | 'logout' | 'failed_login' | 'permission_change' | 'data_access' | 'system_change';
  userId: string;
  userEmail: string;
  ipAddress: string;
  userAgent: string;
  details: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  resolved: boolean;
}

export interface SecurityAlert {
  id: string;
  type: 'suspicious_activity' | 'multiple_failed_logins' | 'unusual_access' | 'system_breach';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ipAddress?: string;
  timestamp: string;
  resolved: boolean;
  resolvedBy?: string;
  resolvedAt?: string;
}

export interface UserSession {
  id: string;
  userId: string;
  userEmail: string;
  ipAddress: string;
  userAgent: string;
  loginTime: string;
  lastActivity: string;
  isActive: boolean;
  location?: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
}

export interface SecurityMetrics {
  totalEvents: number;
  criticalAlerts: number;
  activeSessions: number;
  failedLogins: number;
  suspiciousActivities: number;
  eventsByType: { [key: string]: number };
  alertsByType: { [key: string]: number };
  recentEvents: SecurityEvent[];
  recentAlerts: SecurityAlert[];
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  databaseStatus: 'connected' | 'disconnected' | 'slow';
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
  activeConnections: number;
  responseTime: number;
  lastChecked: string;
}

// Advanced Security Service
export class AdvancedSecurityService {

  // Log security events
  static async logSecurityEvent(event: Omit<SecurityEvent, 'id' | 'timestamp'>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('security_events')
        .insert({
          event_type: event.eventType,
          user_id: event.userId,
          user_email: event.userEmail,
          ip_address: event.ipAddress,
          user_agent: event.userAgent,
          details: event.details,
          severity: event.severity,
          resolved: event.resolved
        });

      if (error) {
        console.error('Error logging security event:', error);
        return false;
      }

      // Check for suspicious patterns
      await this.checkForSuspiciousActivity(event);

      return true;
    } catch (error) {
      console.error('Error in logSecurityEvent:', error);
      return false;
    }
  }

  // Get security events with filtering
  static async getSecurityEvents(filters?: {
    eventType?: string;
    severity?: string;
    userId?: string;
    dateFrom?: string;
    dateTo?: string;
    limit?: number;
  }): Promise<SecurityEvent[]> {
    try {
      let query = supabase
        .from('security_events')
        .select('*')
        .order('created_at', { ascending: false });

      if (filters?.eventType) {
        query = query.eq('event_type', filters.eventType);
      }

      if (filters?.severity) {
        query = query.eq('severity', filters.severity);
      }

      if (filters?.userId) {
        query = query.eq('user_id', filters.userId);
      }

      if (filters?.dateFrom) {
        query = query.gte('created_at', filters.dateFrom);
      }

      if (filters?.dateTo) {
        query = query.lte('created_at', filters.dateTo);
      }

      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching security events:', error);
        return [];
      }

      return data?.map(event => ({
        id: event.id,
        eventType: event.event_type,
        userId: event.user_id,
        userEmail: event.user_email,
        ipAddress: event.ip_address,
        userAgent: event.user_agent,
        details: event.details,
        severity: event.severity,
        timestamp: event.created_at,
        resolved: event.resolved
      })) || [];
    } catch (error) {
      console.error('Error in getSecurityEvents:', error);
      return [];
    }
  }

  // Create security alert
  static async createSecurityAlert(alert: Omit<SecurityAlert, 'id' | 'timestamp' | 'resolved'>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('security_alerts')
        .insert({
          type: alert.type,
          title: alert.title,
          description: alert.description,
          severity: alert.severity,
          user_id: alert.userId,
          ip_address: alert.ipAddress,
          resolved: false
        });

      if (error) {
        console.error('Error creating security alert:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in createSecurityAlert:', error);
      return false;
    }
  }

  // Get security alerts
  static async getSecurityAlerts(filters?: {
    type?: string;
    severity?: string;
    resolved?: boolean;
    limit?: number;
  }): Promise<SecurityAlert[]> {
    try {
      let query = supabase
        .from('security_alerts')
        .select('*')
        .order('created_at', { ascending: false });

      if (filters?.type) {
        query = query.eq('type', filters.type);
      }

      if (filters?.severity) {
        query = query.eq('severity', filters.severity);
      }

      if (filters?.resolved !== undefined) {
        query = query.eq('resolved', filters.resolved);
      }

      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching security alerts:', error);
        return [];
      }

      return data?.map(alert => ({
        id: alert.id,
        type: alert.type,
        title: alert.title,
        description: alert.description,
        severity: alert.severity,
        userId: alert.user_id,
        ipAddress: alert.ip_address,
        timestamp: alert.created_at,
        resolved: alert.resolved,
        resolvedBy: alert.resolved_by,
        resolvedAt: alert.resolved_at
      })) || [];
    } catch (error) {
      console.error('Error in getSecurityAlerts:', error);
      return [];
    }
  }

  // Resolve security alert
  static async resolveSecurityAlert(alertId: string, resolvedBy: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('security_alerts')
        .update({
          resolved: true,
          resolved_by: resolvedBy,
          resolved_at: new Date().toISOString()
        })
        .eq('id', alertId);

      if (error) {
        console.error('Error resolving security alert:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in resolveSecurityAlert:', error);
      return false;
    }
  }

  // Get active user sessions
  static async getActiveSessions(): Promise<UserSession[]> {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .select(`
          *,
          users (
            email
          )
        `)
        .eq('is_active', true)
        .order('login_time', { ascending: false });

      if (error) {
        console.error('Error fetching active sessions:', error);
        return [];
      }

      return data?.map(session => ({
        id: session.id,
        userId: session.user_id,
        userEmail: session.users?.email || 'Unknown',
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        loginTime: session.login_time,
        lastActivity: session.last_activity,
        isActive: session.is_active,
        location: session.location,
        deviceType: this.detectDeviceType(session.user_agent)
      })) || [];
    } catch (error) {
      console.error('Error in getActiveSessions:', error);
      return [];
    }
  }

  // Terminate user session
  static async terminateSession(sessionId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_sessions')
        .update({
          is_active: false,
          ended_at: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (error) {
        console.error('Error terminating session:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in terminateSession:', error);
      return false;
    }
  }

  // Get security metrics
  static async getSecurityMetrics(): Promise<SecurityMetrics> {
    try {
      // Get total events count
      const { count: totalEvents } = await supabase
        .from('security_events')
        .select('*', { count: 'exact', head: true });

      // Get critical alerts count
      const { count: criticalAlerts } = await supabase
        .from('security_alerts')
        .select('*', { count: 'exact', head: true })
        .eq('severity', 'critical')
        .eq('resolved', false);

      // Get active sessions count
      const { count: activeSessions } = await supabase
        .from('user_sessions')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);

      // Get failed logins count (last 24 hours)
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const { count: failedLogins } = await supabase
        .from('security_events')
        .select('*', { count: 'exact', head: true })
        .eq('event_type', 'failed_login')
        .gte('created_at', yesterday);

      // Get recent events
      const recentEvents = await this.getSecurityEvents({ limit: 10 });

      // Get recent alerts
      const recentAlerts = await this.getSecurityAlerts({ limit: 5, resolved: false });

      return {
        totalEvents: totalEvents || 0,
        criticalAlerts: criticalAlerts || 0,
        activeSessions: activeSessions || 0,
        failedLogins: failedLogins || 0,
        suspiciousActivities: 0, // Would be calculated based on patterns
        eventsByType: {}, // Would be calculated from events
        alertsByType: {}, // Would be calculated from alerts
        recentEvents,
        recentAlerts
      };
    } catch (error) {
      console.error('Error in getSecurityMetrics:', error);
      return {
        totalEvents: 0,
        criticalAlerts: 0,
        activeSessions: 0,
        failedLogins: 0,
        suspiciousActivities: 0,
        eventsByType: {},
        alertsByType: {},
        recentEvents: [],
        recentAlerts: []
      };
    }
  }

  // Check for suspicious activity patterns
  private static async checkForSuspiciousActivity(event: Omit<SecurityEvent, 'id' | 'timestamp'>): Promise<void> {
    try {
      // Check for multiple failed logins
      if (event.eventType === 'failed_login') {
        const recentFailures = await this.getSecurityEvents({
          eventType: 'failed_login',
          userId: event.userId,
          dateFrom: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // Last 15 minutes
          limit: 5
        });

        if (recentFailures.length >= 3) {
          await this.createSecurityAlert({
            type: 'multiple_failed_logins',
            title: 'Multiple Failed Login Attempts',
            description: `User ${event.userEmail} has ${recentFailures.length} failed login attempts in the last 15 minutes`,
            severity: 'high',
            userId: event.userId,
            ipAddress: event.ipAddress
          });
        }
      }

      // Check for unusual access patterns
      if (event.eventType === 'login') {
        // Check for login from new IP
        const recentLogins = await this.getSecurityEvents({
          eventType: 'login',
          userId: event.userId,
          limit: 10
        });

        const knownIPs = recentLogins.map(e => e.ipAddress);
        if (!knownIPs.includes(event.ipAddress)) {
          await this.createSecurityAlert({
            type: 'unusual_access',
            title: 'Login from New IP Address',
            description: `User ${event.userEmail} logged in from a new IP address: ${event.ipAddress}`,
            severity: 'medium',
            userId: event.userId,
            ipAddress: event.ipAddress
          });
        }
      }
    } catch (error) {
      console.error('Error checking for suspicious activity:', error);
    }
  }

  // Detect device type from user agent
  private static detectDeviceType(userAgent: string): 'desktop' | 'mobile' | 'tablet' {
    const ua = userAgent.toLowerCase();
    if (ua.includes('mobile')) return 'mobile';
    if (ua.includes('tablet') || ua.includes('ipad')) return 'tablet';
    return 'desktop';
  }

  // Get system health status
  static async getSystemHealth(): Promise<SystemHealth> {
    try {
      const startTime = Date.now();

      // Test database connection
      const { error: dbError } = await supabase
        .from('users')
        .select('count(*)')
        .limit(1);

      const responseTime = Date.now() - startTime;

      return {
        status: dbError ? 'critical' : responseTime > 1000 ? 'warning' : 'healthy',
        uptime: Date.now() - (new Date('2024-01-01').getTime()), // Mock uptime
        databaseStatus: dbError ? 'disconnected' : responseTime > 500 ? 'slow' : 'connected',
        memoryUsage: Math.random() * 80, // Mock data
        cpuUsage: Math.random() * 60,
        diskUsage: Math.random() * 70,
        activeConnections: Math.floor(Math.random() * 100) + 10,
        responseTime,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting system health:', error);
      return {
        status: 'critical',
        uptime: 0,
        databaseStatus: 'disconnected',
        memoryUsage: 0,
        cpuUsage: 0,
        diskUsage: 0,
        activeConnections: 0,
        responseTime: 0,
        lastChecked: new Date().toISOString()
      };
    }
  }
}

export default AdvancedSecurityService;
