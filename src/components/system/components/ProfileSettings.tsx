
import { User, Mail, Phone, MapPin, Building } from 'lucide-react';

const ProfileSettings = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Profile Settings</h3>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Personal Information */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h4 className="text-lg font-semibold mb-4 flex items-center">
            <User className="h-5 w-5 text-teal-600 mr-2" />
            Personal Information
          </h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
              <input type="text" defaultValue="admin" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
              <input type="text" defaultValue="Administrator" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Job Title</label>
              <input type="text" defaultValue="System Administrator" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Bio</label>
              <textarea rows={3} className="w-full border border-gray-300 rounded-lg px-3 py-2" placeholder="Brief description..."></textarea>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h4 className="text-lg font-semibold mb-4 flex items-center">
            <Mail className="h-5 w-5 text-teal-600 mr-2" />
            Contact Information
          </h4>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input type="email" defaultValue="<EMAIL>" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
              <input type="tel" defaultValue="+212 6 12 34 56 78" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
              <input type="text" defaultValue="Casablanca" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
              <input type="text" defaultValue="YalaOffice" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
            </div>
          </div>
        </div>
      </div>

      {/* Address Management */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <h4 className="text-lg font-semibold flex items-center">
            <MapPin className="h-5 w-5 text-teal-600 mr-2" />
            Address Management
          </h4>
          <button className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700">
            Add Address
          </button>
        </div>
        <div className="space-y-3">
          <div className="p-3 border border-gray-200 rounded-lg">
            <div className="flex justify-between items-start">
              <div>
                <p className="font-medium">Office Address</p>
                <p className="text-sm text-gray-600">123 Rue Mohammed V, Casablanca, Morocco</p>
              </div>
              <div className="flex space-x-2">
                <button className="text-blue-600 hover:text-blue-900 text-sm">Edit</button>
                <button className="text-red-600 hover:text-red-900 text-sm">Delete</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button className="bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700">
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default ProfileSettings;
