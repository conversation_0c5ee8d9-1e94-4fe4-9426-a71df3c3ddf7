
import { Shield, Lock, Edit, Trash2 } from 'lucide-react';

const SecurityManagement = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Security Management</h3>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Password Management */}
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h4 className="text-lg font-semibold mb-4 flex items-center">
            <Lock className="h-5 w-5 text-teal-600 mr-2" />
            Password Management
          </h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Minimum Length</span>
              <input type="number" defaultValue="8" className="w-16 border rounded px-2 py-1" />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Require Special Characters</span>
              <input type="checkbox" className="rounded" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Password Expiry (days)</span>
              <input type="number" defaultValue="90" className="w-16 border rounded px-2 py-1" />
            </div>
          </div>
        </div>
      </div>

      {/* User Roles and Permissions */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <h4 className="text-lg font-semibold mb-4">User Roles and Permissions</h4>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Role</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Users</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Permissions</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap font-medium">Administrator</td>
                <td className="px-6 py-4 whitespace-nowrap">3</td>
                <td className="px-6 py-4 whitespace-nowrap">Full Access</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900"><Edit className="h-4 w-4" /></button>
                    <button className="text-red-600 hover:text-red-900"><Trash2 className="h-4 w-4" /></button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SecurityManagement;
