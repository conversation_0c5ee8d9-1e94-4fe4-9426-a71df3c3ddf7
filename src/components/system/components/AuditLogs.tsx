
import { useState, useEffect } from 'react';
import { AuditLog } from '../../../types/system';
import { getAuditLogs } from '../../../services/systemService';

const AuditLogs = () => {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);

  useEffect(() => {
    loadAuditLogs();
  }, []);

  const loadAuditLogs = async () => {
    try {
      const logs = await getAuditLogs();
      setAuditLogs(logs);
    } catch (error) {
      console.error('Error loading audit logs:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Audit Logs</h3>
        <span className="text-sm text-gray-500">{auditLogs.length} entries</span>
      </div>
      <div className="space-y-2">
        {auditLogs.slice(0, 20).map(log => (
          <div key={log.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-3">
                <span className="font-medium">{log.userName}</span>
                <span className="text-sm text-gray-600">{log.action}</span>
                <span className="text-sm text-gray-600">{log.entity} #{log.entityId}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 text-xs rounded ${
                  log.severity === 'critical' ? 'bg-red-100 text-red-800' :
                  log.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                  log.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {log.severity}
                </span>
                <span className="text-xs text-gray-500">
                  {new Date(log.timestamp).toLocaleString()}
                </span>
              </div>
            </div>
            {log.changes.length > 0 && (
              <div className="text-sm text-gray-600">
                Changes: {log.changes.map(change => 
                  `${change.field}: ${change.oldValue} → ${change.newValue}`
                ).join(', ')}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default AuditLogs;
