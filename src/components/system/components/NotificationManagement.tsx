
import { <PERSON>, <PERSON>, <PERSON>, Trash2 } from 'lucide-react';

const NotificationManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Notification Management</h3>
        <div className="flex space-x-2">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Mark All Read
          </button>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <h4 className="text-lg font-semibold mb-4">Notification Settings</h4>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <span className="font-medium">Email Notifications</span>
              <p className="text-sm text-gray-600">Receive notifications via email</p>
            </div>
            <input type="checkbox" className="rounded" defaultChecked />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <span className="font-medium">Order Updates</span>
              <p className="text-sm text-gray-600">Get notified about order status changes</p>
            </div>
            <input type="checkbox" className="rounded" defaultChecked />
          </div>
          <div className="flex items-center justify-between">
            <div>
              <span className="font-medium">Low Stock Alerts</span>
              <p className="text-sm text-gray-600">Receive alerts when products are low in stock</p>
            </div>
            <input type="checkbox" className="rounded" defaultChecked />
          </div>
        </div>
      </div>

      {/* Notifications List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h4 className="text-lg font-semibold">Recent Notifications</h4>
        </div>
        <div className="divide-y divide-gray-200">
          <div className="p-4 hover:bg-gray-50">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <Bell className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium">New order received</p>
                <p className="text-sm text-gray-600">Order #ORD-001234 from Ahmed Mansouri</p>
                <p className="text-xs text-gray-500">2 hours ago</p>
              </div>
              <div className="flex space-x-2">
                <button className="text-blue-600 hover:text-blue-900"><Eye className="h-4 w-4" /></button>
                <button className="text-green-600 hover:text-green-900"><Check className="h-4 w-4" /></button>
                <button className="text-red-600 hover:text-red-900"><Trash2 className="h-4 w-4" /></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationManagement;
