
import { Download, BarChart3, FileText, Calendar } from 'lucide-react';

const AdvancedReports = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Advanced Reports</h3>
        <div className="flex space-x-2">
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2">
            <Download className="h-4 w-4" />
            <span>Export All</span>
          </button>
        </div>
      </div>

      {/* Report Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <BarChart3 className="h-8 w-8 text-blue-600 mr-3" />
            <h4 className="text-lg font-semibold">Sales Reports</h4>
          </div>
          <p className="text-gray-600 mb-4">Daily, weekly, monthly, and yearly sales analytics</p>
          <button className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700">
            Generate Report
          </button>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <FileText className="h-8 w-8 text-green-600 mr-3" />
            <h4 className="text-lg font-semibold">Inventory Reports</h4>
          </div>
          <p className="text-gray-600 mb-4">Stock levels, movements, and inventory analytics</p>
          <button className="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700">
            Generate Report
          </button>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <Calendar className="h-8 w-8 text-purple-600 mr-3" />
            <h4 className="text-lg font-semibold">Custom Reports</h4>
          </div>
          <p className="text-gray-600 mb-4">Create custom reports with specific date ranges</p>
          <button className="w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700">
            Create Custom
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdvancedReports;
