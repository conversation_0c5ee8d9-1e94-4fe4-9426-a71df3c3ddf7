
import { useState, useEffect } from 'react';
import { Cog, Plus, Edit, CreditCard } from 'lucide-react';
import { SystemConfig } from '../../../types/system';
import { getSystemConfigs, updateSystemConfig } from '../../../services/systemService';

const SystemSettings = () => {
  const [systemConfigs, setSystemConfigs] = useState<SystemConfig[]>([]);

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      const configs = await getSystemConfigs();
      setSystemConfigs(configs);
    } catch (error) {
      console.error('Error loading system configs:', error);
    }
  };

  const handleConfigUpdate = async (configId: string, newValue: any) => {
    const updated = await updateSystemConfig(configId, newValue);
    if (updated) {
      setSystemConfigs(prev => prev.map(config => 
        config.id === configId ? updated : config
      ));
    }
  };

  return (
    <div className="space-y-6">
      {['general', 'security', 'notifications', 'integrations', 'performance'].map(category => (
        <div key={category} className="border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4 capitalize flex items-center">
            <Cog className="h-5 w-5 text-teal-600 mr-2" />
            {category} Settings
          </h3>
          <div className="space-y-4">
            {systemConfigs.filter(config => config.category === category).map(config => (
              <div key={config.id} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{config.key}</span>
                    {config.requiresRestart && (
                      <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                        Requires Restart
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">{config.description}</p>
                </div>
                <div className="ml-4">
                  {config.isEditable ? (
                    config.dataType === 'boolean' ? (
                      <input
                        type="checkbox"
                        checked={config.value}
                        onChange={(e) => handleConfigUpdate(config.id, e.target.checked)}
                        className="rounded border-gray-300"
                      />
                    ) : (
                      <input
                        type={config.dataType === 'number' ? 'number' : 'text'}
                        value={config.value}
                        onChange={(e) => handleConfigUpdate(config.id, e.target.value)}
                        className="border border-gray-300 rounded px-3 py-1 text-sm w-32"
                      />
                    )
                  ) : (
                    <span className="text-sm text-gray-500">{String(config.value)}</span>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Payment Methods Section for General Settings */}
          {category === 'general' && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h4 className="text-md font-semibold mb-4 flex items-center">
                <CreditCard className="h-4 w-4 text-teal-600 mr-2" />
                Payment Methods
              </h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span>Cash on Delivery</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600 text-sm">Active</span>
                    <button className="text-gray-600 hover:text-gray-900"><Edit className="h-4 w-4" /></button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span>Bank Transfer</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600 text-sm">Active</span>
                    <button className="text-gray-600 hover:text-gray-900"><Edit className="h-4 w-4" /></button>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span>Check Payment</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-400 text-sm">Inactive</span>
                    <button className="text-gray-600 hover:text-gray-900"><Edit className="h-4 w-4" /></button>
                  </div>
                </div>
              </div>
              <button className="mt-4 bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 flex items-center space-x-2">
                <Plus className="h-4 w-4" />
                <span>Add Payment Method</span>
              </button>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default SystemSettings;
