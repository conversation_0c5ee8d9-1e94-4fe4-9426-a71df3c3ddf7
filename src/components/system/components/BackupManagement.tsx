
import { Database, Download, Upload, Clock } from 'lucide-react';

const BackupManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Backup Management</h3>
        <div className="flex space-x-2">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>Create Backup</span>
          </button>
        </div>
      </div>

      {/* Backup Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h4 className="text-lg font-semibold mb-4">Automatic Backups</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span>Enable Automatic Backups</span>
              <input type="checkbox" className="rounded" defaultChecked />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Backup Frequency</label>
              <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
                <option>Daily</option>
                <option>Weekly</option>
                <option>Monthly</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Retention Period (days)</label>
              <input type="number" defaultValue="30" className="w-full border border-gray-300 rounded-lg px-3 py-2" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h4 className="text-lg font-semibold mb-4">Storage Information</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>Total Backups:</span>
              <span className="font-medium">15</span>
            </div>
            <div className="flex justify-between">
              <span>Storage Used:</span>
              <span className="font-medium">2.3 GB</span>
            </div>
            <div className="flex justify-between">
              <span>Last Backup:</span>
              <span className="font-medium">2 hours ago</span>
            </div>
          </div>
        </div>
      </div>

      {/* Backup History */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h4 className="text-lg font-semibold">Backup History</h4>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Backup ID</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Size</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap font-medium">#BACKUP-001</td>
                <td className="px-6 py-4 whitespace-nowrap">Full</td>
                <td className="px-6 py-4 whitespace-nowrap">1.2 GB</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Jun 14, 2024 08:00</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button className="text-blue-600 hover:text-blue-900"><Download className="h-4 w-4" /></button>
                    <button className="text-green-600 hover:text-green-900"><Upload className="h-4 w-4" /></button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default BackupManagement;
