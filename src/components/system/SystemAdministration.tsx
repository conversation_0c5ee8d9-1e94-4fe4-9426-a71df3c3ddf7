
import { useState } from 'react';
import { 
  Settings, Activity, Database, Shield, Users, Package, 
  ShoppingCart, Building2, BarChart3, FileText, Bell, User, Tag, Monitor
} from 'lucide-react';
import OverviewReports from './components/OverviewReports';
import UsersManagement from './components/UsersManagement';
import BranchManagement from './components/BranchManagement';
import ProductManagement from './components/ProductManagement';
import CategoryManagement from './components/CategoryManagement';
import OrderManagement from './components/OrderManagement';
import SecurityManagement from './components/SecurityManagement';
import SystemSettings from './components/SystemSettings';
import AdvancedReports from './components/AdvancedReports';
import ProfileSettings from './components/ProfileSettings';
import NotificationManagement from './components/NotificationManagement';
import InvoiceManagement from './components/InvoiceManagement';
import AuditLogs from './components/AuditLogs';
import BackupManagement from './components/BackupManagement';
import SystemHealth from './components/SystemHealth';

const SystemAdministration = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'Overview Reports', icon: BarChart3 },
    { id: 'users', label: 'Users Management', icon: Users },
    { id: 'branches', label: 'Branches', icon: Building2 },
    { id: 'products', label: 'Products', icon: Package },
    { id: 'categories', label: 'Categories', icon: Tag },
    { id: 'orders', label: 'Orders', icon: ShoppingCart },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'settings', label: 'System Settings', icon: Settings },
    { id: 'reports', label: 'Advanced Reports', icon: FileText },
    { id: 'profile', label: 'Profile Settings', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'invoices', label: 'Invoices', icon: FileText },
    { id: 'audit', label: 'Audit Logs', icon: Activity },
    { id: 'backups', label: 'Backups', icon: Database },
    { id: 'health', label: 'System Health', icon: Monitor }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewReports />;
      case 'users':
        return <UsersManagement />;
      case 'branches':
        return <BranchManagement />;
      case 'products':
        return <ProductManagement />;
      case 'categories':
        return <CategoryManagement />;
      case 'orders':
        return <OrderManagement />;
      case 'security':
        return <SecurityManagement />;
      case 'settings':
        return <SystemSettings />;
      case 'reports':
        return <AdvancedReports />;
      case 'profile':
        return <ProfileSettings />;
      case 'notifications':
        return <NotificationManagement />;
      case 'invoices':
        return <InvoiceManagement />;
      case 'audit':
        return <AuditLogs />;
      case 'backups':
        return <BackupManagement />;
      case 'health':
        return <SystemHealth />;
      default:
        return <OverviewReports />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">System Administrator Dashboard</h2>

        {/* Tab Navigation */}
        <div className="flex flex-wrap gap-1 bg-gray-100 p-1 rounded-lg mb-6 overflow-x-auto">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors whitespace-nowrap text-sm ${
                activeTab === tab.id
                  ? 'bg-white text-teal-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        {renderTabContent()}
      </div>
    </div>
  );
};

export default SystemAdministration;
