import React, { useState, useEffect } from 'react';
import {
  Activity,
  Server,
  Database,
  HardDrive,
  Cpu,
  Wifi,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  RefreshCw,
  Download
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { SystemHealthService, SystemHealthOverview, PerformanceAlert } from '../../services/systemHealthService';

interface SystemHealthMonitorProps {
  onNavigate?: (page: string) => void;
}

const SystemHealthMonitor: React.FC<SystemHealthMonitorProps> = ({ onNavigate }) => {
  const [healthOverview, setHealthOverview] = useState<SystemHealthOverview | null>(null);
  const [performanceAlerts, setPerformanceAlerts] = useState<PerformanceAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadSystemHealth();
    
    if (autoRefresh) {
      const interval = setInterval(loadSystemHealth, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const loadSystemHealth = async () => {
    try {
      setLoading(true);
      const [overview, alerts] = await Promise.all([
        SystemHealthService.getSystemHealthOverview(),
        SystemHealthService.getPerformanceAlerts(false)
      ]);

      setHealthOverview(overview);
      setPerformanceAlerts(alerts);
    } catch (error) {
      console.error('Error loading system health:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadSystemHealth();
    setRefreshing(false);
  };

  const handleResolveAlert = async (alertId: string) => {
    try {
      await SystemHealthService.resolvePerformanceAlert(alertId);
      await loadSystemHealth();
    } catch (error) {
      console.error('Error resolving alert:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'critical':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Activity className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-blue-600 bg-blue-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatUptime = (uptime: number) => {
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    return `${days}d ${hours}h ${minutes}m`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">System Health Monitor</h1>
          <p className="text-gray-600">Real-time system performance and health monitoring</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">Auto-refresh</span>
          </label>
          
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overall Status</p>
              <p className={`text-lg font-bold capitalize ${getStatusColor(healthOverview?.overallStatus || 'unknown').split(' ')[0]}`}>
                {healthOverview?.overallStatus || 'Unknown'}
              </p>
            </div>
            <div className={`h-12 w-12 rounded-lg flex items-center justify-center ${getStatusColor(healthOverview?.overallStatus || 'unknown')}`}>
              {getStatusIcon(healthOverview?.overallStatus || 'unknown')}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Uptime</p>
              <p className="text-lg font-bold text-green-600">
                {formatUptime(healthOverview?.uptime || 0)}
              </p>
            </div>
            <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Clock className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Alerts</p>
              <p className="text-2xl font-bold text-red-600">{performanceAlerts.length}</p>
            </div>
            <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Response Time</p>
              <p className="text-2xl font-bold text-blue-600">
                {healthOverview?.systemMetrics.responseTime || 0}ms
              </p>
            </div>
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Resources</h3>
          <div className="space-y-6">
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Cpu className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-gray-700">CPU Usage</span>
                </div>
                <span className="text-sm font-bold text-gray-900">
                  {healthOverview?.systemMetrics.cpuUsage?.toFixed(1) || 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${healthOverview?.systemMetrics.cpuUsage || 0}%` }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <HardDrive className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-gray-700">Memory Usage</span>
                </div>
                <span className="text-sm font-bold text-gray-900">
                  {healthOverview?.systemMetrics.memoryUsage?.toFixed(1) || 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${healthOverview?.systemMetrics.memoryUsage || 0}%` }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-purple-600" />
                  <span className="text-sm font-medium text-gray-700">Disk Usage</span>
                </div>
                <span className="text-sm font-bold text-gray-900">
                  {healthOverview?.systemMetrics.diskUsage?.toFixed(1) || 0}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${healthOverview?.systemMetrics.diskUsage || 0}%` }}
                ></div>
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Wifi className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium text-gray-700">Network Latency</span>
                </div>
                <span className="text-sm font-bold text-gray-900">
                  {healthOverview?.systemMetrics.networkLatency?.toFixed(0) || 0}ms
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-orange-600 h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${Math.min((healthOverview?.systemMetrics.networkLatency || 0) / 100 * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Database Health</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">Status</span>
              <div className="flex items-center space-x-2">
                {getStatusIcon(healthOverview?.databaseHealth.status || 'unknown')}
                <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(healthOverview?.databaseHealth.status || 'unknown')}`}>
                  {healthOverview?.databaseHealth.status || 'Unknown'}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700">Connections</span>
              <span className="text-sm font-medium text-gray-900">
                {healthOverview?.databaseHealth.connectionCount || 0} / {healthOverview?.databaseHealth.maxConnections || 100}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700">Avg Response Time</span>
              <span className="text-sm font-medium text-gray-900">
                {healthOverview?.databaseHealth.queryPerformance.averageResponseTime || 0}ms
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700">Slow Queries</span>
              <span className="text-sm font-medium text-gray-900">
                {healthOverview?.databaseHealth.queryPerformance.slowQueries || 0}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700">Failed Queries</span>
              <span className="text-sm font-medium text-gray-900">
                {healthOverview?.databaseHealth.queryPerformance.failedQueries || 0}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700">Storage Usage</span>
              <span className="text-sm font-medium text-gray-900">
                {healthOverview?.databaseHealth.storage.usagePercentage?.toFixed(1) || 0}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Trends */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Trends (24h)</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={healthOverview?.performanceTrends || []}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="timestamp" 
              tickFormatter={(value) => new Date(value).toLocaleTimeString()}
            />
            <YAxis />
            <Tooltip 
              labelFormatter={(value) => new Date(value).toLocaleString()}
              formatter={(value: any, name: string) => [
                `${typeof value === 'number' ? value.toFixed(1) : value}${name.includes('Usage') ? '%' : name.includes('Time') ? 'ms' : ''}`,
                name.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
              ]}
            />
            <Legend />
            <Line type="monotone" dataKey="cpuUsage" stroke="#3B82F6" name="CPU Usage" />
            <Line type="monotone" dataKey="memoryUsage" stroke="#10B981" name="Memory Usage" />
            <Line type="monotone" dataKey="responseTime" stroke="#F59E0B" name="Response Time" />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Active Alerts */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Performance Alerts</h3>
        {performanceAlerts.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <p className="text-gray-600">No active alerts. System is running smoothly!</p>
          </div>
        ) : (
          <div className="space-y-3">
            {performanceAlerts.map((alert) => (
              <div key={alert.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className={`h-5 w-5 ${getSeverityColor(alert.severity).split(' ')[0]}`} />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{alert.title}</p>
                    <p className="text-xs text-gray-600">{alert.description}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Current: {alert.currentValue.toFixed(1)} | Threshold: {alert.threshold}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                    {alert.severity}
                  </span>
                  <button
                    onClick={() => handleResolveAlert(alert.id)}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Resolve
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemHealthMonitor;
