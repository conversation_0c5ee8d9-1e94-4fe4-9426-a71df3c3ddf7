import { useState, useEffect } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle, Package, ShoppingCart, TrendingUp, Users } from 'lucide-react';
import { realTimeService, RealTimeEvent } from '../../services/realTimeService';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface NotificationSystemProps {
  userType: string;
  userId?: string;
}

const NotificationSystem = ({ userType, userId }: NotificationSystemProps) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    // Subscribe to real-time events for notifications
    const unsubscribeProduct = realTimeService.subscribe('product-updated', handleProductUpdate);
    const unsubscribeStock = realTimeService.subscribe('stock-updated', handleStockUpdate);
    const unsubscribeOrder = realTimeService.subscribe('order-created', handleOrderCreated);
    const unsubscribeInventory = realTimeService.subscribe('inventory-changed', handleInventoryChange);
    const unsubscribePrice = realTimeService.subscribe('price-updated', handlePriceUpdate);

    return () => {
      unsubscribeProduct();
      unsubscribeStock();
      unsubscribeOrder();
      unsubscribeInventory();
      unsubscribePrice();
    };
  }, [userType, userId]);

  const handleProductUpdate = (event: RealTimeEvent) => {
    if (userType === 'admin' || userType === 'manager') {
      addNotification({
        type: 'info',
        title: 'Product Updated',
        message: `Product "${event.data.title || event.data.productId}" has been updated`,
        duration: 5000
      });
    }
  };

  const handleStockUpdate = (event: RealTimeEvent) => {
    const { productId, newStock, oldStock } = event.data;
    
    if (newStock < 10 && oldStock >= 10) {
      addNotification({
        type: 'warning',
        title: 'Low Stock Alert',
        message: `Product ${productId} is running low (${newStock} remaining)`,
        duration: 8000,
        persistent: true
      });
    } else if (newStock === 0) {
      addNotification({
        type: 'error',
        title: 'Out of Stock',
        message: `Product ${productId} is now out of stock`,
        duration: 10000,
        persistent: true
      });
    } else if (newStock > oldStock) {
      addNotification({
        type: 'success',
        title: 'Stock Replenished',
        message: `Product ${productId} stock updated to ${newStock}`,
        duration: 4000
      });
    }
  };

  const handleOrderCreated = (event: RealTimeEvent) => {
    if (userType === 'admin' || userType === 'manager') {
      addNotification({
        type: 'success',
        title: 'New Order Received',
        message: `Order ${event.data.orderId || event.data.id} has been placed`,
        duration: 6000,
        action: {
          label: 'View Order',
          onClick: () => console.log('Navigate to order details')
        }
      });
    }
  };

  const handleInventoryChange = (event: RealTimeEvent) => {
    if (userType === 'admin' || userType === 'manager') {
      addNotification({
        type: 'info',
        title: 'Inventory Updated',
        message: `Inventory changes detected for product ${event.data.productId}`,
        duration: 4000
      });
    }
  };

  const handlePriceUpdate = (event: RealTimeEvent) => {
    if (userType === 'reseller' || userType === 'client') {
      addNotification({
        type: 'info',
        title: 'Price Update',
        message: `Price updated for product ${event.data.productId}`,
        duration: 5000
      });
    }
  };

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    const newNotification: Notification = {
      ...notification,
      id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString()
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 4)]); // Keep max 5 notifications

    // Auto-remove notification after duration (unless persistent)
    if (!notification.persistent && notification.duration) {
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, notification.duration);
    }
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getNotificationStyles = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info':
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  if (notifications.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`border rounded-lg p-4 shadow-lg backdrop-blur-sm transition-all duration-300 transform hover:scale-105 ${getNotificationStyles(notification.type)}`}
        >
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              {getNotificationIcon(notification.type)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-semibold">{notification.title}</h4>
                <button
                  onClick={() => removeNotification(notification.id)}
                  className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              
              <p className="text-sm mt-1 opacity-90">{notification.message}</p>
              
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs opacity-70">
                  {new Date(notification.timestamp).toLocaleTimeString()}
                </span>
                
                {notification.action && (
                  <button
                    onClick={notification.action.onClick}
                    className="text-xs font-medium underline hover:no-underline transition-all"
                  >
                    {notification.action.label}
                  </button>
                )}
              </div>
            </div>
          </div>
          
          {notification.persistent && (
            <div className="mt-2 pt-2 border-t border-current border-opacity-20">
              <div className="flex items-center text-xs opacity-70">
                <AlertTriangle className="h-3 w-3 mr-1" />
                <span>Requires attention</span>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

// Hook for programmatically adding notifications
export const useNotifications = () => {
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp'>) => {
    // This would be connected to a global notification context in a real app
    console.log('Adding notification:', notification);
  };

  return { addNotification };
};

// Predefined notification templates
export const NotificationTemplates = {
  stockLow: (productName: string, stock: number): Omit<Notification, 'id' | 'timestamp'> => ({
    type: 'warning',
    title: 'Low Stock Alert',
    message: `${productName} is running low (${stock} remaining)`,
    duration: 8000,
    persistent: true
  }),

  stockOut: (productName: string): Omit<Notification, 'id' | 'timestamp'> => ({
    type: 'error',
    title: 'Out of Stock',
    message: `${productName} is now out of stock`,
    duration: 10000,
    persistent: true
  }),

  orderReceived: (orderId: string): Omit<Notification, 'id' | 'timestamp'> => ({
    type: 'success',
    title: 'New Order',
    message: `Order ${orderId} has been received`,
    duration: 6000,
    action: {
      label: 'View Order',
      onClick: () => console.log(`Navigate to order ${orderId}`)
    }
  }),

  priceUpdate: (productName: string): Omit<Notification, 'id' | 'timestamp'> => ({
    type: 'info',
    title: 'Price Updated',
    message: `Price has been updated for ${productName}`,
    duration: 5000
  }),

  productAdded: (productName: string): Omit<Notification, 'id' | 'timestamp'> => ({
    type: 'success',
    title: 'Product Added',
    message: `${productName} has been added to the catalog`,
    duration: 4000
  }),

  systemMaintenance: (): Omit<Notification, 'id' | 'timestamp'> => ({
    type: 'warning',
    title: 'System Maintenance',
    message: 'Scheduled maintenance will begin in 30 minutes',
    duration: 15000,
    persistent: true
  })
};

export default NotificationSystem;
