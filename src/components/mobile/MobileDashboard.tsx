import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Package,
  ShoppingCart,
  Users,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Plus,
  ArrowRight
} from 'lucide-react';
import { mobileDashboardService, MobileStats, MobileActivity } from '../../services/mobileDashboardService';
import { realTimeService } from '../../services/realTimeService';
import { useAuth } from '../../contexts/AuthContext';
import { formatCurrency } from '../../utils/currency';

interface MobileDashboardProps {
  onNavigate: (page: string) => void;
}



const MobileDashboard: React.FC<MobileDashboardProps> = ({ onNavigate }) => {
  const { user } = useAuth();
  const [stats, setStats] = useState<MobileStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<MobileActivity[]>([]);
  const [loading, setLoading] = useState(true);

  // Load dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        console.log('MobileDashboard: Loading mobile dashboard data...');

        const { stats: mobileStats, activity } = await mobileDashboardService.refreshMobileData();
        console.log('MobileDashboard: Mobile data loaded:', { mobileStats, activity });

        setStats(mobileStats);
        setRecentActivity(activity);
      } catch (error) {
        console.error('MobileDashboard: Error loading mobile dashboard data:', error);
        // Fallback to default stats
        setStats({
          revenue: { value: 0, change: 0, trend: 'up' },
          orders: { value: 0, change: 0, trend: 'up' },
          customers: { value: 0, change: 0, trend: 'up' },
          products: { value: 0, change: 0, trend: 'up' }
        });
        setRecentActivity([]);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();

    // Set up real-time subscriptions
    const unsubscribeStats = realTimeService.subscribe('statistics-updated', () => {
      console.log('MobileDashboard: Statistics updated, reloading...');
      loadDashboardData();
    });

    const unsubscribeOrderCreated = realTimeService.subscribe('order-created', () => {
      console.log('MobileDashboard: Order created, reloading stats...');
      loadDashboardData();
    });

    const unsubscribeOrderUpdated = realTimeService.subscribe('order-updated', () => {
      console.log('MobileDashboard: Order updated, reloading stats...');
      loadDashboardData();
    });

    return () => {
      unsubscribeStats();
      unsubscribeOrderCreated();
      unsubscribeOrderUpdated();
    };
  }, []);

  const quickActions = [
    {
      id: 'orders',
      title: 'Orders',
      description: 'Manage orders',
      icon: ShoppingCart,
      color: 'bg-blue-500',
      action: () => onNavigate('orders')
    },
    {
      id: 'product-management',
      title: 'Products',
      description: 'Manage inventory',
      icon: Package,
      color: 'bg-green-500',
      action: () => onNavigate('product-management')
    },
    {
      id: 'customers',
      title: 'Customers',
      description: 'Manage customers',
      icon: Users,
      color: 'bg-purple-500',
      action: () => onNavigate('customers')
    },
    {
      id: 'analytics',
      title: 'Analytics',
      description: 'View reports',
      icon: TrendingUp,
      color: 'bg-orange-500',
      action: () => onNavigate('analytics')
    }
  ];

  const getActivityIcon = (type: string, status: string) => {
    switch (type) {
      case 'order':
        return <ShoppingCart className="h-4 w-4" />;
      case 'customer':
        return <Users className="h-4 w-4" />;
      case 'product':
        return <Package className="h-4 w-4" />;
      case 'alert':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      case 'info':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="pb-20"> {/* Space for bottom navigation */}
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-teal-600 to-amber-500 text-white p-6 rounded-b-3xl mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Welcome back{user?.full_name ? `, ${user.full_name}` : ''}!</h1>
            <p className="text-teal-100 mt-1">Here's what's happening today</p>
          </div>
          <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <TrendingUp className="h-6 w-6" />
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {loading ? (
        <div className="grid grid-cols-2 gap-4 px-4 mb-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
              <div className="animate-pulse">
                <div className="w-10 h-10 bg-gray-200 rounded-xl mb-2"></div>
                <div className="h-6 bg-gray-200 rounded mb-1"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      ) : stats ? (
        <div className="grid grid-cols-2 gap-4 px-4 mb-6">
          <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
              <div className={`flex items-center space-x-1 ${
                stats.revenue.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.revenue.trend === 'up' ? (
                  <TrendingUp className="h-3 w-3" />
                ) : (
                  <TrendingDown className="h-3 w-3" />
                )}
                <span className="text-xs font-medium">{Math.abs(stats.revenue.change)}%</span>
              </div>
            </div>
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.revenue.value)}</p>
            <p className="text-xs text-gray-600">Revenue</p>
          </div>

        <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
              <ShoppingCart className="h-5 w-5 text-blue-600" />
            </div>
            <div className={`flex items-center space-x-1 ${
              stats.orders.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.orders.trend === 'up' ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span className="text-xs font-medium">{Math.abs(stats.orders.change)}%</span>
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{stats.orders.value}</p>
          <p className="text-xs text-gray-600">Orders</p>
        </div>

        <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
              <Users className="h-5 w-5 text-purple-600" />
            </div>
            <div className={`flex items-center space-x-1 ${
              stats.customers.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {stats.customers.trend === 'up' ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span className="text-xs font-medium">{Math.abs(stats.customers.change)}%</span>
            </div>
          </div>
          <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.customers.value)}</p>
          <p className="text-xs text-gray-600">Customers</p>
        </div>

          <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                <Package className="h-5 w-5 text-orange-600" />
              </div>
              <div className={`flex items-center space-x-1 ${
                stats.products.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stats.products.trend === 'up' ? (
                  <TrendingUp className="h-3 w-3" />
                ) : (
                  <TrendingDown className="h-3 w-3" />
                )}
                <span className="text-xs font-medium">{Math.abs(stats.products.change)}%</span>
              </div>
            </div>
            <p className="text-2xl font-bold text-gray-900">{stats.products.value}</p>
            <p className="text-xs text-gray-600">Products</p>
          </div>
        </div>
      ) : (
        <div className="px-4 mb-6">
          <div className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 text-center">
            <p className="text-gray-500">Unable to load dashboard data</p>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="px-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Quick Actions</h2>
        </div>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <button
                key={action.id}
                onClick={action.action}
                className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow"
              >
                <div className={`w-12 h-12 ${action.color} rounded-xl flex items-center justify-center mb-3`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="font-semibold text-gray-900 text-sm mb-1">{action.title}</h3>
                <p className="text-xs text-gray-600">{action.description}</p>
              </button>
            );
          })}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="px-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
          <button
            onClick={() => onNavigate('activity')}
            className="text-blue-600 text-sm font-medium flex items-center space-x-1"
          >
            <span>View All</span>
            <ArrowRight className="h-3 w-3" />
          </button>
        </div>
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
          {recentActivity.map((activity, index) => (
            <div
              key={activity.id}
              className={`p-4 flex items-center space-x-3 ${
                index !== recentActivity.length - 1 ? 'border-b border-gray-100' : ''
              }`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getStatusColor(activity.status)}`}>
                {getActivityIcon(activity.type, activity.status)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{activity.title}</p>
                <p className="text-xs text-gray-600 truncate">{activity.description}</p>
              </div>
              <div className="text-xs text-gray-500">{activity.time}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Performance Summary */}
      {stats && (
        <div className="px-4 mb-6">
          <div className="bg-gradient-to-r from-teal-600 to-amber-500 rounded-2xl p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">System Overview</h3>
              <CheckCircle className="h-6 w-6" />
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold">{stats.orders.value}</p>
                <p className="text-xs text-teal-100">Total Orders</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">{formatNumber(stats.revenue.value)}</p>
                <p className="text-xs text-teal-100">Revenue (MAD)</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">{stats.customers.value}</p>
                <p className="text-xs text-teal-100">Customers</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileDashboard;
