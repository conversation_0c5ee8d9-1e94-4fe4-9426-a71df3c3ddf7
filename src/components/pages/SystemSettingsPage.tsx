import { useState, useEffect } from 'react';
import { Settings, Save, RefreshCw, Mail, Bell, Shield, Globe, Eye, EyeOff, Send, CheckCircle, AlertCircle } from 'lucide-react';
import { supabase } from '../../integrations/supabase/client';

const SystemSettingsPage = () => {
  const [activeSection, setActiveSection] = useState('general');
  const [showPassword, setShowPassword] = useState(false);
  const [testEmailLoading, setTestEmailLoading] = useState(false);
  const [testEmailStatus, setTestEmailStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [showTemplateEditor, setShowTemplateEditor] = useState(false);
  const [selectedNotificationType, setSelectedNotificationType] = useState('');
  const [notificationTemplates, setNotificationTemplates] = useState({
    lowStockAlerts: 'Product {{productName}} is running low. Current stock: {{currentStock}}',
    orderStatusChangeNotifications: 'Your order #{{orderNumber}} status has been updated to {{status}}',
    userAccountNotifications: 'Your account has been {{action}}. Please contact support if you have questions.',
    paymentConfirmationNotifications: 'Payment of {{amount}} for order #{{orderNumber}} has been confirmed.',
    deliveryAssignmentNotifications: 'Order #{{orderNumber}} has been assigned to {{deliveryPerson}} for delivery.',
    systemMaintenanceNotifications: 'System maintenance scheduled for {{maintenanceDate}}. Expected downtime: {{duration}}.'
  });
  const [settings, setSettings] = useState({
    general: {
      siteName: 'YalaOffice',
      siteDescription: 'Office Supply Management System',
      timezone: 'Africa/Casablanca',
      sessionTimeout: 30,
      fileUploadSizeLimit: 10,
      maintenanceMode: false,
      backupFrequency: 'daily'
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      lowStockAlerts: true,
      orderNotifications: true,
      orderStatusChangeNotifications: true,
      userAccountNotifications: true,
      paymentConfirmationNotifications: true,
      deliveryAssignmentNotifications: true,
      systemMaintenanceNotifications: true,
      systemAlerts: true
    },
    security: {
      passwordMinLength: 8,
      passwordRequireUppercase: true,
      passwordRequireLowercase: true,
      passwordRequireNumbers: true,
      passwordRequireSpecialChars: true,
      sessionTimeout: 30,
      sessionSecurityStrict: true,
      allowRememberMe: true,
      maxLoginAttempts: 5,
      lockoutDuration: 15,
      twoFactorAuthEnabled: false,
      apiAccessControlEnabled: true,
      ipWhitelistEnabled: false
    },
    email: {
      smtpHost: '',
      smtpPort: 587,
      smtpUsername: '',
      smtpPassword: '',
      encryptionType: 'TLS',
      fromEmail: '',
      fromName: 'YalaOffice',
      testEmail: ''
    }
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      console.log('SystemSettings: Loading settings from database...');

      const { data: configs, error } = await supabase
        .from('system_configs')
        .select('*');

      if (error) {
        console.error('Error loading settings:', error);
        return;
      }

      if (configs && configs.length > 0) {
        const loadedSettings = { ...settings };
        const loadedTemplates = { ...notificationTemplates };

        configs.forEach(config => {
          const value = JSON.parse(config.value);

          if (config.category === 'general') {
            switch (config.key) {
              case 'site_name':
                loadedSettings.general.siteName = value;
                break;
              case 'site_description':
                loadedSettings.general.siteDescription = value;
                break;
              case 'timezone':
                loadedSettings.general.timezone = value;
                break;
              case 'session_timeout':
                loadedSettings.general.sessionTimeout = value;
                break;
              case 'file_upload_size_limit':
                loadedSettings.general.fileUploadSizeLimit = value;
                break;
              case 'maintenance_mode':
                loadedSettings.general.maintenanceMode = value;
                break;
              case 'backup_frequency':
                loadedSettings.general.backupFrequency = value;
                break;
            }
          } else if (config.category === 'notifications') {
            switch (config.key) {
              case 'email_notifications':
                loadedSettings.notifications.emailNotifications = value;
                break;
              case 'push_notifications':
                loadedSettings.notifications.pushNotifications = value;
                break;
              case 'low_stock_alerts':
                loadedSettings.notifications.lowStockAlerts = value;
                break;
              case 'order_notifications':
                loadedSettings.notifications.orderNotifications = value;
                break;
              case 'order_status_change_notifications':
                loadedSettings.notifications.orderStatusChangeNotifications = value;
                break;
              case 'user_account_notifications':
                loadedSettings.notifications.userAccountNotifications = value;
                break;
              case 'payment_confirmation_notifications':
                loadedSettings.notifications.paymentConfirmationNotifications = value;
                break;
              case 'delivery_assignment_notifications':
                loadedSettings.notifications.deliveryAssignmentNotifications = value;
                break;
              case 'system_maintenance_notifications':
                loadedSettings.notifications.systemMaintenanceNotifications = value;
                break;
              case 'system_alerts':
                loadedSettings.notifications.systemAlerts = value;
                break;
            }
          } else if (config.category === 'security') {
            switch (config.key) {
              case 'password_min_length':
                loadedSettings.security.passwordMinLength = value;
                break;
              case 'password_require_uppercase':
                loadedSettings.security.passwordRequireUppercase = value;
                break;
              case 'password_require_lowercase':
                loadedSettings.security.passwordRequireLowercase = value;
                break;
              case 'password_require_numbers':
                loadedSettings.security.passwordRequireNumbers = value;
                break;
              case 'password_require_special_chars':
                loadedSettings.security.passwordRequireSpecialChars = value;
                break;
              case 'session_timeout':
                loadedSettings.security.sessionTimeout = value;
                break;
              case 'session_security_strict':
                loadedSettings.security.sessionSecurityStrict = value;
                break;
              case 'allow_remember_me':
                loadedSettings.security.allowRememberMe = value;
                break;
              case 'max_login_attempts':
                loadedSettings.security.maxLoginAttempts = value;
                break;
              case 'lockout_duration':
                loadedSettings.security.lockoutDuration = value;
                break;
              case 'two_factor_auth_enabled':
                loadedSettings.security.twoFactorAuthEnabled = value;
                break;
              case 'api_access_control_enabled':
                loadedSettings.security.apiAccessControlEnabled = value;
                break;
              case 'ip_whitelist_enabled':
                loadedSettings.security.ipWhitelistEnabled = value;
                break;
            }
          } else if (config.category === 'email') {
            switch (config.key) {
              case 'smtp_host':
                loadedSettings.email.smtpHost = value;
                break;
              case 'smtp_port':
                loadedSettings.email.smtpPort = value;
                break;
              case 'smtp_username':
                loadedSettings.email.smtpUsername = value;
                break;
              case 'smtp_password':
                loadedSettings.email.smtpPassword = value;
                break;
              case 'encryption_type':
                loadedSettings.email.encryptionType = value;
                break;
              case 'from_email':
                loadedSettings.email.fromEmail = value;
                break;
              case 'from_name':
                loadedSettings.email.fromName = value;
                break;
            }
          } else if (config.category === 'templates') {
            switch (config.key) {
              case 'low_stock_alerts':
                loadedTemplates.lowStockAlerts = value;
                break;
              case 'order_status_change_notifications':
                loadedTemplates.orderStatusChangeNotifications = value;
                break;
              case 'user_account_notifications':
                loadedTemplates.userAccountNotifications = value;
                break;
              case 'payment_confirmation_notifications':
                loadedTemplates.paymentConfirmationNotifications = value;
                break;
              case 'delivery_assignment_notifications':
                loadedTemplates.deliveryAssignmentNotifications = value;
                break;
              case 'system_maintenance_notifications':
                loadedTemplates.systemMaintenanceNotifications = value;
                break;
            }
          }
        });

        setSettings(loadedSettings);
        setNotificationTemplates(loadedTemplates);
        console.log('SystemSettings: Settings loaded successfully');
      }
    } catch (error) {
      console.error('SystemSettings: Error loading settings:', error);
    }
  };

  const handleSettingChange = (section: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  const handleSave = async () => {
    try {
      console.log('SystemSettings: Saving settings to database...', settings);

      // Validate critical settings
      if (settings.general.sessionTimeout < 5 || settings.general.sessionTimeout > 480) {
        alert('Session timeout must be between 5 and 480 minutes.');
        return;
      }

      if (settings.security.passwordMinLength < 6 || settings.security.passwordMinLength > 32) {
        alert('Password minimum length must be between 6 and 32 characters.');
        return;
      }

      if (settings.security.maxLoginAttempts < 3 || settings.security.maxLoginAttempts > 10) {
        alert('Max login attempts must be between 3 and 10.');
        return;
      }

      // Save settings to Supabase using system_configs table
      const settingsToSave = [
        // General settings
        { category: 'general', key: 'site_name', value: settings.general.siteName },
        { category: 'general', key: 'site_description', value: settings.general.siteDescription },
        { category: 'general', key: 'timezone', value: settings.general.timezone },
        { category: 'general', key: 'session_timeout', value: settings.general.sessionTimeout },
        { category: 'general', key: 'file_upload_size_limit', value: settings.general.fileUploadSizeLimit },
        { category: 'general', key: 'maintenance_mode', value: settings.general.maintenanceMode },
        { category: 'general', key: 'backup_frequency', value: settings.general.backupFrequency },

        // Notification settings
        { category: 'notifications', key: 'email_notifications', value: settings.notifications.emailNotifications },
        { category: 'notifications', key: 'push_notifications', value: settings.notifications.pushNotifications },
        { category: 'notifications', key: 'low_stock_alerts', value: settings.notifications.lowStockAlerts },
        { category: 'notifications', key: 'order_notifications', value: settings.notifications.orderNotifications },
        { category: 'notifications', key: 'order_status_change_notifications', value: settings.notifications.orderStatusChangeNotifications },
        { category: 'notifications', key: 'user_account_notifications', value: settings.notifications.userAccountNotifications },
        { category: 'notifications', key: 'payment_confirmation_notifications', value: settings.notifications.paymentConfirmationNotifications },
        { category: 'notifications', key: 'delivery_assignment_notifications', value: settings.notifications.deliveryAssignmentNotifications },
        { category: 'notifications', key: 'system_maintenance_notifications', value: settings.notifications.systemMaintenanceNotifications },
        { category: 'notifications', key: 'system_alerts', value: settings.notifications.systemAlerts },

        // Security settings
        { category: 'security', key: 'password_min_length', value: settings.security.passwordMinLength },
        { category: 'security', key: 'password_require_uppercase', value: settings.security.passwordRequireUppercase },
        { category: 'security', key: 'password_require_lowercase', value: settings.security.passwordRequireLowercase },
        { category: 'security', key: 'password_require_numbers', value: settings.security.passwordRequireNumbers },
        { category: 'security', key: 'password_require_special_chars', value: settings.security.passwordRequireSpecialChars },
        { category: 'security', key: 'session_timeout', value: settings.security.sessionTimeout },
        { category: 'security', key: 'session_security_strict', value: settings.security.sessionSecurityStrict },
        { category: 'security', key: 'allow_remember_me', value: settings.security.allowRememberMe },
        { category: 'security', key: 'max_login_attempts', value: settings.security.maxLoginAttempts },
        { category: 'security', key: 'lockout_duration', value: settings.security.lockoutDuration },
        { category: 'security', key: 'two_factor_auth_enabled', value: settings.security.twoFactorAuthEnabled },
        { category: 'security', key: 'api_access_control_enabled', value: settings.security.apiAccessControlEnabled },
        { category: 'security', key: 'ip_whitelist_enabled', value: settings.security.ipWhitelistEnabled },

        // Email settings
        { category: 'email', key: 'smtp_host', value: settings.email.smtpHost },
        { category: 'email', key: 'smtp_port', value: settings.email.smtpPort },
        { category: 'email', key: 'smtp_username', value: settings.email.smtpUsername },
        { category: 'email', key: 'smtp_password', value: settings.email.smtpPassword },
        { category: 'email', key: 'encryption_type', value: settings.email.encryptionType },
        { category: 'email', key: 'from_email', value: settings.email.fromEmail },
        { category: 'email', key: 'from_name', value: settings.email.fromName },

        // Notification templates
        { category: 'templates', key: 'low_stock_alerts', value: notificationTemplates.lowStockAlerts },
        { category: 'templates', key: 'order_status_change_notifications', value: notificationTemplates.orderStatusChangeNotifications },
        { category: 'templates', key: 'user_account_notifications', value: notificationTemplates.userAccountNotifications },
        { category: 'templates', key: 'payment_confirmation_notifications', value: notificationTemplates.paymentConfirmationNotifications },
        { category: 'templates', key: 'delivery_assignment_notifications', value: notificationTemplates.deliveryAssignmentNotifications },
        { category: 'templates', key: 'system_maintenance_notifications', value: notificationTemplates.systemMaintenanceNotifications }
      ];

      // Save each setting using upsert
      for (const setting of settingsToSave) {
        const { error } = await supabase
          .from('system_configs')
          .upsert({
            category: setting.category,
            key: setting.key,
            value: JSON.stringify(setting.value),
            data_type: typeof setting.value,
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'category,key'
          });

        if (error) {
          console.error(`Error saving setting ${setting.category}.${setting.key}:`, error);
          throw new Error(`Failed to save ${setting.category}.${setting.key}: ${error.message}`);
        }
      }

      console.log('SystemSettings: Settings saved successfully');

      // Show confirmation with critical settings that require restart
      const requiresRestart = settings.general.maintenanceMode ||
                             settings.security.sessionSecurityStrict ||
                             settings.security.twoFactorAuthEnabled;

      if (requiresRestart) {
        alert('Settings saved successfully!\n\nNote: Some changes may require a system restart to take full effect.');
      } else {
        alert('Settings saved successfully!');
      }

      // Emit real-time event for system-wide synchronization
      // This would notify all connected components about the settings change
      console.log('SystemSettings: Broadcasting settings update event');

    } catch (error) {
      console.error('SystemSettings: Error saving settings:', error);
      alert(`Error saving settings: ${error instanceof Error ? error.message : 'Please try again.'}`);
    }
  };

  const handleTestEmail = async () => {
    setTestEmailLoading(true);
    setTestEmailStatus('idle');

    try {
      console.log('SystemSettings: Testing SMTP configuration...');

      // Validate required fields
      if (!settings.email.smtpHost || !settings.email.smtpUsername || !settings.email.smtpPassword || !settings.email.fromEmail || !settings.email.testEmail) {
        alert('Please fill in all required SMTP fields before testing.');
        setTestEmailStatus('error');
        return;
      }

      // Prepare test email data
      const testEmailData = {
        smtpConfig: {
          host: settings.email.smtpHost,
          port: settings.email.smtpPort,
          username: settings.email.smtpUsername,
          password: settings.email.smtpPassword,
          encryption: settings.email.encryptionType
        },
        emailData: {
          from: `${settings.email.fromName} <${settings.email.fromEmail}>`,
          to: settings.email.testEmail,
          subject: 'YalaOffice SMTP Test Email',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #0d9488;">YalaOffice SMTP Test</h2>
              <p>This is a test email to verify your SMTP configuration.</p>
              <div style="background-color: #f0f9ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="color: #0369a1; margin-top: 0;">Configuration Details:</h3>
                <ul style="color: #374151;">
                  <li><strong>SMTP Host:</strong> ${settings.email.smtpHost}</li>
                  <li><strong>Port:</strong> ${settings.email.smtpPort}</li>
                  <li><strong>Encryption:</strong> ${settings.email.encryptionType}</li>
                  <li><strong>From:</strong> ${settings.email.fromEmail}</li>
                </ul>
              </div>
              <p style="color: #6b7280;">If you received this email, your SMTP configuration is working correctly!</p>
              <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 20px 0;">
              <p style="color: #9ca3af; font-size: 12px;">
                This email was sent from YalaOffice System Settings at ${new Date().toLocaleString()}
              </p>
            </div>
          `
        }
      };

      console.log('SystemSettings: Sending test email with config:', {
        host: testEmailData.smtpConfig.host,
        port: testEmailData.smtpConfig.port,
        to: testEmailData.emailData.to
      });

      // For now, simulate the email test since we don't have a backend API
      // In a real implementation, this would call your backend API
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Simulate success/failure based on configuration completeness
      const hasValidConfig = settings.email.smtpHost &&
                           settings.email.smtpUsername &&
                           settings.email.smtpPassword &&
                           settings.email.fromEmail;

      if (hasValidConfig) {
        console.log('SystemSettings: Test email sent successfully (simulated)');
        setTestEmailStatus('success');
        alert(`Test email sent successfully to ${settings.email.testEmail}! Please check your inbox.\n\nNote: This is a simulated test. In production, this would send a real email using your SMTP configuration.`);
      } else {
        throw new Error('Invalid SMTP configuration');
      }
    } catch (error) {
      console.error('SystemSettings: SMTP test error:', error);
      setTestEmailStatus('error');
      alert(`SMTP test failed: ${error instanceof Error ? error.message : 'Please check your SMTP configuration and try again.'}`);
    } finally {
      setTestEmailLoading(false);
    }
  };

  const sections = [
    { id: 'general', label: 'General', icon: Settings },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'email', label: 'Email Settings', icon: Mail }
  ];

  return (
    <div className="space-y-6">
      {/* Settings Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6 overflow-x-auto">
            {sections.map((section) => {
              const Icon = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center space-x-2 ${
                    activeSection === section.id
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{section.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* General Settings */}
          {activeSection === 'general' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">General Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                  <input
                    type="text"
                    value={settings.general.siteName}
                    onChange={(e) => handleSettingChange('general', 'siteName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                  <select
                    value={settings.general.timezone}
                    onChange={(e) => handleSettingChange('general', 'timezone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="Africa/Casablanca">Africa/Casablanca</option>
                    <option value="Europe/London">Europe/London</option>
                    <option value="America/New_York">America/New_York</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                  <input
                    type="number"
                    min="5"
                    max="480"
                    value={settings.general.sessionTimeout}
                    onChange={(e) => handleSettingChange('general', 'sessionTimeout', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">Time before user sessions expire (5-480 minutes)</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">File Upload Size Limit (MB)</label>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={settings.general.fileUploadSizeLimit}
                    onChange={(e) => handleSettingChange('general', 'fileUploadSizeLimit', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">Maximum file size for uploads (1-100 MB)</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Backup Frequency</label>
                  <select
                    value={settings.general.backupFrequency}
                    onChange={(e) => handleSettingChange('general', 'backupFrequency', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="hourly">Hourly</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">Frequency of automatic system backups</p>
                </div>

                <div className="md:col-span-2">
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">Maintenance Mode</h4>
                      <p className="text-sm text-gray-600">Enable to temporarily disable system access for maintenance</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.general.maintenanceMode}
                        onChange={(e) => handleSettingChange('general', 'maintenanceMode', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-500"></div>
                    </label>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                <textarea
                  value={settings.general.siteDescription}
                  onChange={(e) => handleSettingChange('general', 'siteDescription', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
            </div>
          )}

          {/* Notification Settings */}
          {activeSection === 'notifications' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Notification Settings</h3>
                <div className="text-sm text-gray-500">Configure system-wide notification preferences</div>
              </div>

              <div className="space-y-4">
                {Object.entries(settings.notifications).map(([key, value]) => {
                  const notificationDescriptions = {
                    emailNotifications: 'Send notifications via email to users',
                    pushNotifications: 'Show browser push notifications',
                    lowStockAlerts: 'Alert when products are running low in stock',
                    orderNotifications: 'Notify about new orders and updates',
                    orderStatusChangeNotifications: 'Send notifications when order status changes',
                    userAccountNotifications: 'Notify about user account changes and activations',
                    paymentConfirmationNotifications: 'Send payment confirmation notifications',
                    deliveryAssignmentNotifications: 'Notify when orders are assigned for delivery',
                    systemMaintenanceNotifications: 'Send system maintenance announcements',
                    systemAlerts: 'Show critical system alerts and warnings'
                  };

                  const hasTemplate = ['lowStockAlerts', 'orderStatusChangeNotifications', 'userAccountNotifications', 'paymentConfirmationNotifications', 'deliveryAssignmentNotifications', 'systemMaintenanceNotifications'].includes(key);

                  return (
                    <div key={key} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-gray-900">
                            {key.replace(/([A-Z])/g, ' $1').trim().replace(/^./, str => str.toUpperCase())}
                          </h4>
                          {hasTemplate && (
                            <button
                              onClick={() => {
                                setSelectedNotificationType(key);
                                setShowTemplateEditor(true);
                              }}
                              className="text-gray-400 hover:text-orange-600 transition-colors"
                              title="Edit notification template"
                            >
                              <Settings className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {notificationDescriptions[key as keyof typeof notificationDescriptions]}
                        </p>
                        {hasTemplate && (
                          <p className="text-xs text-orange-600 mt-1">
                            Template customizable • Supports dynamic variables
                          </p>
                        )}
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer ml-4">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={value}
                          onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>
                  );
                })}
              </div>

              {/* Role-based Notification Preferences */}
              <div className="mt-8 p-6 bg-gray-50 rounded-lg">
                <h4 className="text-md font-semibold text-gray-900 mb-4">Role-based Notification Preferences</h4>
                <p className="text-sm text-gray-600 mb-4">Configure which user roles receive specific notifications</p>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {['admin', 'manager', 'client', 'reseller', 'delivery'].map(role => (
                    <div key={role} className="p-3 bg-white rounded border">
                      <h5 className="font-medium text-gray-900 capitalize mb-2">{role}</h5>
                      <div className="space-y-1 text-sm">
                        <label className="flex items-center">
                          <input type="checkbox" className="mr-2" defaultChecked />
                          Order notifications
                        </label>
                        <label className="flex items-center">
                          <input type="checkbox" className="mr-2" defaultChecked={role === 'admin' || role === 'manager'} />
                          Low stock alerts
                        </label>
                        <label className="flex items-center">
                          <input type="checkbox" className="mr-2" defaultChecked={role === 'admin'} />
                          System alerts
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Security Settings */}
          {activeSection === 'security' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>
                <div className="text-sm text-gray-500">Configure system security policies</div>
              </div>

              {/* Password Complexity Requirements */}
              <div className="p-6 bg-gray-50 rounded-lg">
                <h4 className="text-md font-semibold text-gray-900 mb-4">Password Complexity Requirements</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Minimum Password Length</label>
                    <input
                      type="number"
                      min="6"
                      max="32"
                      value={settings.security.passwordMinLength}
                      onChange={(e) => handleSettingChange('security', 'passwordMinLength', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">Minimum characters required (6-32)</p>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium text-gray-900">Require Uppercase Letters</h5>
                        <p className="text-sm text-gray-600">At least one uppercase letter (A-Z)</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.security.passwordRequireUppercase}
                          onChange={(e) => handleSettingChange('security', 'passwordRequireUppercase', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium text-gray-900">Require Lowercase Letters</h5>
                        <p className="text-sm text-gray-600">At least one lowercase letter (a-z)</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.security.passwordRequireLowercase}
                          onChange={(e) => handleSettingChange('security', 'passwordRequireLowercase', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium text-gray-900">Require Numbers</h5>
                        <p className="text-sm text-gray-600">At least one numeric digit (0-9)</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.security.passwordRequireNumbers}
                          onChange={(e) => handleSettingChange('security', 'passwordRequireNumbers', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium text-gray-900">Require Special Characters</h5>
                        <p className="text-sm text-gray-600">At least one special character (!@#$%^&*)</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.security.passwordRequireSpecialChars}
                          onChange={(e) => handleSettingChange('security', 'passwordRequireSpecialChars', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Session Security Settings */}
              <div className="p-6 bg-gray-50 rounded-lg">
                <h4 className="text-md font-semibold text-gray-900 mb-4">Session Security Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                    <input
                      type="number"
                      min="5"
                      max="480"
                      value={settings.security.sessionTimeout}
                      onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">Time before sessions expire (5-480 minutes)</p>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium text-gray-900">Strict Session Security</h5>
                        <p className="text-sm text-gray-600">Enhanced session validation and IP checking</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.security.sessionSecurityStrict}
                          onChange={(e) => handleSettingChange('security', 'sessionSecurityStrict', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium text-gray-900">Allow Remember Me</h5>
                        <p className="text-sm text-gray-600">Let users stay logged in longer</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only peer"
                          checked={settings.security.allowRememberMe}
                          onChange={(e) => handleSettingChange('security', 'allowRememberMe', e.target.checked)}
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Login Security Settings */}
              <div className="p-6 bg-gray-50 rounded-lg">
                <h4 className="text-md font-semibold text-gray-900 mb-4">Login Security & Access Control</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
                    <input
                      type="number"
                      min="3"
                      max="10"
                      value={settings.security.maxLoginAttempts}
                      onChange={(e) => handleSettingChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">Failed attempts before lockout (3-10)</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Lockout Duration (minutes)</label>
                    <input
                      type="number"
                      min="5"
                      max="60"
                      value={settings.security.lockoutDuration}
                      onChange={(e) => handleSettingChange('security', 'lockoutDuration', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">Account lockout duration (5-60 minutes)</p>
                  </div>
                </div>

                <div className="mt-6 space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-medium text-gray-900">Two-Factor Authentication</h5>
                      <p className="text-sm text-gray-600">Require 2FA for enhanced security (if applicable)</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.security.twoFactorAuthEnabled}
                        onChange={(e) => handleSettingChange('security', 'twoFactorAuthEnabled', e.target.checked)}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-medium text-gray-900">API Access Control</h5>
                      <p className="text-sm text-gray-600">Enable API access restrictions and rate limiting</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.security.apiAccessControlEnabled}
                        onChange={(e) => handleSettingChange('security', 'apiAccessControlEnabled', e.target.checked)}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-medium text-gray-900">IP Whitelist</h5>
                      <p className="text-sm text-gray-600">Restrict access to specific IP addresses</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.security.ipWhitelistEnabled}
                        onChange={(e) => handleSettingChange('security', 'ipWhitelistEnabled', e.target.checked)}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Password Security</h4>
                    <p className="text-sm text-gray-600">Enforce strong password requirements</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={true}
                      onChange={() => {}}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                  </label>
                </div>
                
                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Allow Remember Me</h4>
                    <p className="text-sm text-gray-600">Let users stay logged in</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      className="sr-only peer"
                      checked={settings.security.allowRememberMe}
                      onChange={(e) => handleSettingChange('security', 'allowRememberMe', e.target.checked)}
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Email Settings */}
          {activeSection === 'email' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Email Settings</h3>
                  <p className="text-gray-600">Configure SMTP settings for system-wide email notifications</p>
                </div>
                <div className="flex items-center space-x-2">
                  {testEmailStatus === 'success' && (
                    <div className="flex items-center text-green-600">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      <span className="text-sm">Test successful</span>
                    </div>
                  )}
                  {testEmailStatus === 'error' && (
                    <div className="flex items-center text-red-600">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      <span className="text-sm">Test failed</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* SMTP Host */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Host <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={settings.email.smtpHost}
                    onChange={(e) => handleSettingChange('email', 'smtpHost', e.target.value)}
                    placeholder="smtp.gmail.com"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>

                {/* SMTP Port */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SMTP Port <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    value={settings.email.smtpPort}
                    onChange={(e) => handleSettingChange('email', 'smtpPort', parseInt(e.target.value))}
                    placeholder="587"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>

                {/* Username */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Username <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={settings.email.smtpUsername}
                    onChange={(e) => handleSettingChange('email', 'smtpUsername', e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>

                {/* Password */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={settings.email.smtpPassword}
                      onChange={(e) => handleSettingChange('email', 'smtpPassword', e.target.value)}
                      placeholder="App password or SMTP password"
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Encryption Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Encryption Type
                  </label>
                  <select
                    value={settings.email.encryptionType}
                    onChange={(e) => handleSettingChange('email', 'encryptionType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="None">None</option>
                    <option value="TLS">TLS</option>
                    <option value="SSL">SSL</option>
                  </select>
                </div>

                {/* From Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    From Email Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    value={settings.email.fromEmail}
                    onChange={(e) => handleSettingChange('email', 'fromEmail', e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>

                {/* From Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    From Name
                  </label>
                  <input
                    type="text"
                    value={settings.email.fromName}
                    onChange={(e) => handleSettingChange('email', 'fromName', e.target.value)}
                    placeholder="YalaOffice"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>

                {/* Test Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Test Email Address
                  </label>
                  <input
                    type="email"
                    value={settings.email.testEmail}
                    onChange={(e) => handleSettingChange('email', 'testEmail', e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Test Email Button */}
              <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                <div className="text-sm text-gray-600">
                  <p>Test your SMTP configuration by sending a test email.</p>
                  <p className="text-xs mt-1">Make sure to save your settings before testing.</p>
                </div>
                <button
                  onClick={handleTestEmail}
                  disabled={testEmailLoading || !settings.email.testEmail}
                  className="flex items-center space-x-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  {testEmailLoading ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                  <span>{testEmailLoading ? 'Sending...' : 'Send Test Email'}</span>
                </button>
              </div>

              {/* SMTP Configuration Help */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Common SMTP Settings</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p><strong>Gmail:</strong> smtp.gmail.com, Port 587, TLS</p>
                  <p><strong>Outlook:</strong> smtp-mail.outlook.com, Port 587, TLS</p>
                  <p><strong>Yahoo:</strong> smtp.mail.yahoo.com, Port 587, TLS</p>
                  <p><strong>Custom SMTP:</strong> Contact your email provider for settings</p>
                </div>
              </div>
            </div>
          )}


        </div>

        {/* Save Button */}
        <div className="border-t border-gray-200 px-6 py-4">
          <div className="flex justify-end space-x-3">
            <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
              Reset
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>Save Settings</span>
            </button>
          </div>
        </div>
      </div>

      {/* Notification Template Editor Modal */}
      {showTemplateEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Edit Notification Template: {selectedNotificationType.replace(/([A-Z])/g, ' $1').trim()}
                </h3>
                <button
                  onClick={() => setShowTemplateEditor(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                {/* Template Editor */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Template
                  </label>
                  <textarea
                    value={notificationTemplates[selectedNotificationType as keyof typeof notificationTemplates] || ''}
                    onChange={(e) => setNotificationTemplates(prev => ({
                      ...prev,
                      [selectedNotificationType]: e.target.value
                    }))}
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent font-mono text-sm"
                    placeholder="Enter your notification template here..."
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Use double curly braces for variables: {'{{variableName}}'}
                  </p>
                </div>

                {/* Available Variables */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Available Variables</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
                    {selectedNotificationType === 'lowStockAlerts' && (
                      <>
                        <code className="bg-white px-2 py-1 rounded">{'{{productName}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{currentStock}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{minStock}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{category}}'}</code>
                      </>
                    )}
                    {selectedNotificationType === 'orderStatusChangeNotifications' && (
                      <>
                        <code className="bg-white px-2 py-1 rounded">{'{{orderNumber}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{status}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{customerName}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{orderDate}}'}</code>
                      </>
                    )}
                    {selectedNotificationType === 'userAccountNotifications' && (
                      <>
                        <code className="bg-white px-2 py-1 rounded">{'{{userName}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{action}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{userEmail}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{userType}}'}</code>
                      </>
                    )}
                    {selectedNotificationType === 'paymentConfirmationNotifications' && (
                      <>
                        <code className="bg-white px-2 py-1 rounded">{'{{amount}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{orderNumber}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{paymentMethod}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{transactionId}}'}</code>
                      </>
                    )}
                    {selectedNotificationType === 'deliveryAssignmentNotifications' && (
                      <>
                        <code className="bg-white px-2 py-1 rounded">{'{{orderNumber}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{deliveryPerson}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{customerAddress}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{estimatedDelivery}}'}</code>
                      </>
                    )}
                    {selectedNotificationType === 'systemMaintenanceNotifications' && (
                      <>
                        <code className="bg-white px-2 py-1 rounded">{'{{maintenanceDate}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{duration}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{affectedServices}}'}</code>
                        <code className="bg-white px-2 py-1 rounded">{'{{contactInfo}}'}</code>
                      </>
                    )}
                  </div>
                </div>

                {/* Template Preview */}
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-semibold text-gray-900 mb-3">Preview</h4>
                  <div className="bg-white p-4 rounded border text-sm">
                    {notificationTemplates[selectedNotificationType as keyof typeof notificationTemplates]?.replace(
                      /\{\{(\w+)\}\}/g,
                      '<span style="background-color: #fef3c7; padding: 2px 4px; border-radius: 3px;">$1</span>'
                    ) || 'No template content'}
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setShowTemplateEditor(false)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  console.log('Saving notification template:', selectedNotificationType, notificationTemplates[selectedNotificationType as keyof typeof notificationTemplates]);
                  alert('Notification template saved successfully!');
                  setShowTemplateEditor(false);
                }}
                className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
              >
                Save Template
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemSettingsPage;
