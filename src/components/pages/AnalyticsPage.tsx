import { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, DollarSign, Users, Package, Calendar, Activity, Target, Download, RefreshCw, Filter } from 'lucide-react';
import { liveDataService } from '../../services/liveDataService';
import { realTimeService } from '../../services/realTimeService';
import DetailedReportModal from '../analytics/DetailedReportModals';

interface AnalyticsData {
  totalRevenue: number;
  totalOrders: number;
  activeUsers: number;
  productSales: number;
  orderVolume: number[];
  revenueTrend: number[];
  previousPeriodRevenue: number;
  previousPeriodOrders: number;
  previousPeriodUsers: number;
  previousPeriodSales: number;
}

interface DetailedReport {
  userAnalytics: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    usersByRole: { role: string; count: number }[];
    userGrowth: { date: string; count: number }[];
  };
  productPerformance: {
    topProducts: { name: string; sales: number; revenue: number }[];
    categoryPerformance: { category: string; sales: number; revenue: number }[];
    lowStockItems: { name: string; stock: number; minStock: number }[];
  };
  salesGoals: {
    monthlyTarget: number;
    currentProgress: number;
    progressPercentage: number;
    dailyAverage: number;
    projectedTotal: number;
  };
}

const AnalyticsPage = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('30days');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [detailedReports, setDetailedReports] = useState<DetailedReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportDateRange, setExportDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const [showDetailedReportModal, setShowDetailedReportModal] = useState(false);
  const [selectedReportType, setSelectedReportType] = useState<'user' | 'product' | 'sales' | null>(null);

  // Load analytics data
  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod]);

  // Real-time synchronization
  useEffect(() => {
    console.log('Analytics: Setting up real-time event listeners');

    // Create a single event handler for all analytics-related events
    const handleAnalyticsEvent = (event: any) => {
      console.log('Analytics: Real-time event received:', event.type, event.data);
      loadAnalyticsData(); // Refresh analytics data
    };

    // Subscribe to multiple event types that affect analytics
    const eventTypes = [
      'user-updated',
      'user-created',
      'user-status-changed',
      'order-updated',
      'order-created',
      'order-status-changed',
      'product-updated',
      'product-added',
      'stock-updated',
      'statistics-updated'
    ] as const;

    // Subscribe to all relevant events
    const unsubscribe = realTimeService.subscribeToMultiple(eventTypes, handleAnalyticsEvent);

    console.log('Analytics: Real-time event listeners set up successfully');

    // Cleanup event listeners
    return () => {
      console.log('Analytics: Cleaning up real-time event listeners');
      unsubscribe();
    };
  }, []);

  // Set up comprehensive real-time subscriptions for complete database synchronization
  useEffect(() => {
    console.log('Analytics: Setting up comprehensive real-time subscriptions');

    // Order-related events
    const unsubscribeOrderCreated = realTimeService.subscribe('order-created', (event) => {
      console.log('Analytics: Order created event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeOrderStatusChanged = realTimeService.subscribe('order-status-changed', (event) => {
      console.log('Analytics: Order status changed event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeOrderUpdated = realTimeService.subscribe('order-updated', (event) => {
      console.log('Analytics: Order updated event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeOrderDeleted = realTimeService.subscribe('order-deleted', (event) => {
      console.log('Analytics: Order deleted event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeDeliveryAssigned = realTimeService.subscribe('delivery-assigned', (event) => {
      console.log('Analytics: Delivery assigned event received:', event);
      loadAnalyticsData();
    });

    // User-related events
    const unsubscribeUserCreated = realTimeService.subscribe('user-created', (event) => {
      console.log('Analytics: User created event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeUserUpdated = realTimeService.subscribe('user-updated', (event) => {
      console.log('Analytics: User updated event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeUserDeleted = realTimeService.subscribe('user-deleted', (event) => {
      console.log('Analytics: User deleted event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeUserStatusChanged = realTimeService.subscribe('user-status-changed', (event) => {
      console.log('Analytics: User status changed event received:', event);
      loadAnalyticsData();
    });

    // Product-related events
    const unsubscribeProductCreated = realTimeService.subscribe('product-created', (event) => {
      console.log('Analytics: Product created event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeProductUpdated = realTimeService.subscribe('product-updated', (event) => {
      console.log('Analytics: Product updated event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeProductDeleted = realTimeService.subscribe('product-deleted', (event) => {
      console.log('Analytics: Product deleted event received:', event);
      loadAnalyticsData();
    });

    const unsubscribeInventoryUpdated = realTimeService.subscribe('inventory-updated', (event) => {
      console.log('Analytics: Inventory updated event received:', event);
      loadAnalyticsData();
    });

    // Statistics-related events
    const unsubscribeStatisticsUpdated = realTimeService.subscribe('statistics-updated', (event) => {
      console.log('Analytics: Statistics updated event received:', event);
      loadAnalyticsData();
    });

    // Dashboard sync events
    const unsubscribeDashboardSync = realTimeService.subscribe('dashboard-sync', (event) => {
      console.log('Analytics: Dashboard sync event received:', event);
      loadAnalyticsData();
    });

    return () => {
      // Clean up all subscriptions
      unsubscribeOrderCreated();
      unsubscribeOrderStatusChanged();
      unsubscribeOrderUpdated();
      unsubscribeOrderDeleted();
      unsubscribeDeliveryAssigned();
      unsubscribeUserCreated();
      unsubscribeUserUpdated();
      unsubscribeUserDeleted();
      unsubscribeUserStatusChanged();
      unsubscribeProductCreated();
      unsubscribeProductUpdated();
      unsubscribeProductDeleted();
      unsubscribeInventoryUpdated();
      unsubscribeStatisticsUpdated();
      unsubscribeDashboardSync();
      console.log('Analytics: All real-time subscriptions cleaned up');
    };
  }, []);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      console.log('Analytics: Loading comprehensive data for period:', selectedPeriod);

      // Get current period data with comprehensive database synchronization
      const [dashboardStats, orders, users, products] = await Promise.all([
        liveDataService.getDashboardStats(),
        liveDataService.getAllOrders(),
        liveDataService.getAllUsers(),
        liveDataService.getAllProducts()
      ]);

      console.log('Analytics: Data loaded successfully:', {
        dashboardStats,
        ordersCount: orders.length,
        usersCount: users.length,
        productsCount: products.length
      });

      // Calculate period-specific data
      const periodDays = getPeriodDays(selectedPeriod);
      const currentDate = new Date();
      const periodStart = new Date(currentDate.getTime() - periodDays * 24 * 60 * 60 * 1000);
      const previousPeriodStart = new Date(periodStart.getTime() - periodDays * 24 * 60 * 60 * 1000);

      // Filter orders for current period
      const currentPeriodOrders = orders.filter(order =>
        new Date(order.created_at) >= periodStart
      );

      // Filter orders for previous period (for comparison)
      const previousPeriodOrders = orders.filter(order => {
        const orderDate = new Date(order.created_at);
        return orderDate >= previousPeriodStart && orderDate < periodStart;
      });

      // Calculate current period metrics
      const totalRevenue = currentPeriodOrders
        .filter(order => order.payment_status === 'completed')
        .reduce((sum, order) => sum + order.total, 0);

      const totalOrders = currentPeriodOrders.length;

      const activeUsers = users.filter(user => user.is_active).length;

      const productSales = currentPeriodOrders.reduce((sum, order) =>
        sum + (order.item_count || 0), 0
      );

      // Calculate previous period metrics for comparison
      const previousRevenue = previousPeriodOrders
        .filter(order => order.payment_status === 'completed')
        .reduce((sum, order) => sum + order.total, 0);

      const previousOrders = previousPeriodOrders.length;
      const previousUsers = users.filter(user =>
        user.is_active && new Date(user.created_at) < periodStart
      ).length;
      const previousSales = previousPeriodOrders.reduce((sum, order) =>
        sum + (order.item_count || 0), 0
      );

      // Generate trend data
      const orderVolume = generateTrendData(currentPeriodOrders, periodDays, 'count');
      const revenueTrend = generateTrendData(
        currentPeriodOrders.filter(order => order.payment_status === 'completed'),
        periodDays,
        'revenue'
      );

      setAnalyticsData({
        totalRevenue,
        totalOrders,
        activeUsers,
        productSales,
        orderVolume,
        revenueTrend,
        previousPeriodRevenue: previousRevenue,
        previousPeriodOrders: previousOrders,
        previousPeriodUsers: previousUsers,
        previousPeriodSales: previousSales
      });

      // Load detailed reports
      await loadDetailedReports(orders, users, products);

      console.log('Analytics: All data loaded and processed successfully', {
        analyticsData: {
          totalRevenue,
          totalOrders,
          activeUsers,
          productSales
        },
        period: selectedPeriod,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Analytics: Error loading analytics data:', error);
      // Set fallback data to prevent UI crashes
      setAnalyticsData({
        totalRevenue: 0,
        totalOrders: 0,
        activeUsers: 0,
        productSales: 0,
        orderVolume: [],
        revenueTrend: [],
        previousPeriodRevenue: 0,
        previousPeriodOrders: 0,
        previousPeriodUsers: 0,
        previousPeriodSales: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const loadDetailedReports = async (orders: any[], users: any[], products: any[]) => {
    try {
      // User Analytics
      const totalUsers = users.length;
      const activeUsers = users.filter(user => user.is_active).length;
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const newUsers = users.filter(user => new Date(user.created_at) > thirtyDaysAgo).length;

      const usersByRole = [
        { role: 'Admin', count: users.filter(u => u.user_type === 'admin').length },
        { role: 'Manager', count: users.filter(u => u.user_type === 'manager').length },
        { role: 'Client', count: users.filter(u => u.user_type === 'client').length },
        { role: 'Reseller', count: users.filter(u => u.user_type === 'reseller').length },
        { role: 'Delivery', count: users.filter(u => u.user_type === 'delivery').length }
      ];

      const userGrowth = generateUserGrowthData(users, 30);

      // Enhanced Product Performance Analytics
      console.log('Analytics: Calculating comprehensive product performance...');

      // Get categories for category performance analysis
      const categories = await liveDataService.getAllCategories();
      console.log('Analytics: Categories loaded:', categories.length);

      // Calculate product sales and revenue with profit margins
      const productSalesMap = new Map();
      const completedOrders = orders.filter(order => order.payment_status === 'completed');

      completedOrders.forEach(order => {
        if (order.order_items) {
          order.order_items.forEach((item: any) => {
            const product = products.find(p => p.id === item.product_id);
            const existing = productSalesMap.get(item.product_id) || {
              sales: 0,
              revenue: 0,
              cost: 0,
              name: product?.title || item.product_name || 'Unknown Product',
              categoryId: product?.category_id
            };

            existing.sales += item.quantity || 0;
            existing.revenue += (item.quantity || 0) * (item.unit_price || 0);

            // Calculate cost using real cost_price or estimate
            const costPrice = product?.cost_price || item.cost_price || (item.unit_price * 0.7);
            existing.cost += (item.quantity || 0) * costPrice;

            productSalesMap.set(item.product_id, existing);
          });
        }
      });

      // Top products with profit calculations
      const topProducts = Array.from(productSalesMap.values())
        .map(product => ({
          ...product,
          profit: product.revenue - product.cost,
          margin: product.revenue > 0 ? ((product.revenue - product.cost) / product.revenue) * 100 : 0
        }))
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 15);

      // Category performance analysis
      const categoryMetrics = new Map();
      const totalRevenue = Array.from(productSalesMap.values()).reduce((sum, p) => sum + p.revenue, 0);

      // Initialize category metrics
      categories.forEach(category => {
        categoryMetrics.set(category.id, {
          category: category.name,
          products: 0,
          sales: 0,
          revenue: 0,
          percentage: 0
        });
      });

      // Count products per category
      products.forEach(product => {
        if (product.category_id && categoryMetrics.has(product.category_id)) {
          const categoryData = categoryMetrics.get(product.category_id);
          categoryData.products++;
        }
      });

      // Calculate sales and revenue per category
      Array.from(productSalesMap.values()).forEach(product => {
        if (product.categoryId && categoryMetrics.has(product.categoryId)) {
          const categoryData = categoryMetrics.get(product.categoryId);
          categoryData.sales += product.sales;
          categoryData.revenue += product.revenue;
        }
      });

      // Calculate percentages and sort by revenue
      const categoryPerformance = Array.from(categoryMetrics.values())
        .map(category => ({
          ...category,
          percentage: totalRevenue > 0 ? (category.revenue / totalRevenue) * 100 : 0
        }))
        .filter(category => category.products > 0 || category.sales > 0)
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 10);

      // Inventory insights
      const totalValue = products.reduce((sum, p) => sum + ((p.stock || 0) * (p.price || 0)), 0);
      const averageStockLevel = products.length > 0 ?
        products.reduce((sum, p) => sum + (p.stock || 0), 0) / products.length : 0;

      // Calculate real turnover rate
      const totalSalesQuantity = Array.from(productSalesMap.values()).reduce((sum, p) => sum + p.sales, 0);
      const totalCurrentStock = products.reduce((sum, p) => sum + (p.stock || 0), 0);
      const turnoverRate = totalCurrentStock > 0 ? totalSalesQuantity / totalCurrentStock : 0;

      // Low stock items with enhanced details
      const lowStockItems = products
        .filter(product => product.stock <= (product.min_stock || 10))
        .map(product => ({
          name: product.title,
          stock: product.stock,
          minStock: product.min_stock || 10,
          price: product.price,
          category: categories.find(c => c.id === product.category_id)?.name || 'Uncategorized'
        }))
        .sort((a, b) => a.stock - b.stock)
        .slice(0, 15);

      console.log('Analytics: Product performance calculated:', {
        topProductsCount: topProducts.length,
        categoryPerformanceCount: categoryPerformance.length,
        lowStockItemsCount: lowStockItems.length,
        totalValue: totalValue.toFixed(2),
        turnoverRate: turnoverRate.toFixed(2)
      });

      // Sales Goals (example targets)
      const monthlyTarget = 100000; // 100k Dh target
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const monthStart = new Date(currentYear, currentMonth, 1);
      const monthOrders = orders.filter(order =>
        new Date(order.created_at) >= monthStart && order.payment_status === 'completed'
      );
      const currentProgress = monthOrders.reduce((sum, order) => sum + order.total, 0);
      const progressPercentage = (currentProgress / monthlyTarget) * 100;
      const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
      const currentDay = new Date().getDate();
      const dailyAverage = currentProgress / currentDay;
      const projectedTotal = dailyAverage * daysInMonth;

      setDetailedReports({
        userAnalytics: {
          totalUsers,
          activeUsers,
          newUsers,
          usersByRole,
          userGrowth
        },
        productPerformance: {
          topProducts,
          categoryPerformance,
          lowStockItems,
          inventoryInsights: {
            totalValue,
            turnoverRate: Math.round(turnoverRate * 100) / 100,
            averageStockLevel: Math.round(averageStockLevel * 100) / 100,
            reorderAlerts: lowStockItems.length
          }
        },
        salesGoals: {
          monthlyTarget,
          currentProgress,
          progressPercentage,
          dailyAverage,
          projectedTotal
        }
      });

      console.log('Analytics: Detailed reports set successfully');

    } catch (error) {
      console.error('Error loading detailed reports:', error);
    }
  };

  const getPeriodDays = (period: string): number => {
    switch (period) {
      case '7days': return 7;
      case '30days': return 30;
      case '90days': return 90;
      case '1year': return 365;
      default: return 30;
    }
  };

  const generateTrendData = (orders: any[], days: number, type: 'count' | 'revenue'): number[] => {
    const data = new Array(Math.min(days, 30)).fill(0); // Limit to 30 points for chart
    const interval = Math.max(1, Math.floor(days / 30));

    orders.forEach(order => {
      const orderDate = new Date(order.created_at);
      const daysDiff = Math.floor((Date.now() - orderDate.getTime()) / (24 * 60 * 60 * 1000));
      const index = Math.floor(daysDiff / interval);

      if (index >= 0 && index < data.length) {
        if (type === 'count') {
          data[data.length - 1 - index]++;
        } else {
          data[data.length - 1 - index] += order.total;
        }
      }
    });

    return data;
  };

  const generateUserGrowthData = (users: any[], days: number) => {
    const data = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const count = users.filter(user => new Date(user.created_at) <= date).length;
      data.push({
        date: date.toISOString().split('T')[0],
        count
      });
    }
    return data;
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAnalyticsData();
    setRefreshing(false);
  };

  const handleViewFullReport = (reportType: 'user' | 'product' | 'sales') => {
    console.log('Opening detailed report for:', reportType);
    setSelectedReportType(reportType);
    setShowDetailedReportModal(true);
  };

  const handleCloseDetailedReport = () => {
    setShowDetailedReportModal(false);
    setSelectedReportType(null);
  };

  const calculateGrowth = (current: number, previous: number): { value: number; trend: 'up' | 'down' } => {
    if (previous === 0) return { value: 0, trend: 'up' };
    const growth = ((current - previous) / previous) * 100;
    return {
      value: Math.abs(growth),
      trend: growth >= 0 ? 'up' : 'down'
    };
  };

  const handleExportData = async () => {
    try {
      console.log('Exporting analytics data for range:', exportDateRange);

      // Get fresh data for export
      const [orders, users, products] = await Promise.all([
        liveDataService.getAllOrders(),
        liveDataService.getAllUsers(),
        liveDataService.getAllProducts()
      ]);

      // Filter data by date range
      const startDate = new Date(exportDateRange.start);
      const endDate = new Date(exportDateRange.end);
      endDate.setHours(23, 59, 59, 999); // Include full end date

      const filteredOrders = orders.filter(order => {
        const orderDate = new Date(order.created_at);
        return orderDate >= startDate && orderDate <= endDate;
      });

      const filteredUsers = users.filter(user => {
        const userDate = new Date(user.created_at);
        return userDate >= startDate && userDate <= endDate;
      });

      // Prepare CSV data
      const csvData = [];

      // Header
      csvData.push([
        'Export Date',
        'Date Range',
        'Total Orders',
        'Total Revenue (Dh)',
        'Completed Orders',
        'Pending Orders',
        'Cancelled Orders',
        'New Users',
        'Active Users',
        'Total Products',
        'Low Stock Products',
        'Average Order Value (Dh)',
        'Top Product',
        'Top Product Sales'
      ]);

      // Calculate metrics for export
      const totalOrders = filteredOrders.length;
      const completedOrders = filteredOrders.filter(o => o.payment_status === 'completed');
      const totalRevenue = completedOrders.reduce((sum, order) => sum + order.total, 0);
      const pendingOrders = filteredOrders.filter(o => o.status === 'pending').length;
      const cancelledOrders = filteredOrders.filter(o => o.status === 'cancelled').length;
      const newUsers = filteredUsers.length;
      const activeUsers = users.filter(u => u.is_active).length;
      const totalProducts = products.length;
      const lowStockProducts = products.filter(p => p.stock <= (p.min_stock || 10)).length;
      const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      // Find top product
      const productSalesMap = new Map();
      filteredOrders.forEach(order => {
        if (order.order_items) {
          order.order_items.forEach((item: any) => {
            const existing = productSalesMap.get(item.product_id) || { sales: 0, name: item.product_name };
            existing.sales += item.quantity;
            productSalesMap.set(item.product_id, existing);
          });
        }
      });

      const topProduct = Array.from(productSalesMap.values())
        .sort((a, b) => b.sales - a.sales)[0];

      // Add summary row
      csvData.push([
        new Date().toISOString().split('T')[0],
        `${exportDateRange.start} to ${exportDateRange.end}`,
        totalOrders,
        totalRevenue.toFixed(2),
        completedOrders.length,
        pendingOrders,
        cancelledOrders,
        newUsers,
        activeUsers,
        totalProducts,
        lowStockProducts,
        avgOrderValue.toFixed(2),
        topProduct?.name || 'N/A',
        topProduct?.sales || 0
      ]);

      // Add daily breakdown
      csvData.push([]); // Empty row
      csvData.push(['Daily Breakdown']);
      csvData.push(['Date', 'Orders', 'Revenue (Dh)', 'New Users']);

      const dailyData = new Map();
      const currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        dailyData.set(dateStr, { orders: 0, revenue: 0, users: 0 });
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Populate daily data
      filteredOrders.forEach(order => {
        const dateStr = new Date(order.created_at).toISOString().split('T')[0];
        if (dailyData.has(dateStr)) {
          const day = dailyData.get(dateStr);
          day.orders++;
          if (order.payment_status === 'completed') {
            day.revenue += order.total;
          }
        }
      });

      filteredUsers.forEach(user => {
        const dateStr = new Date(user.created_at).toISOString().split('T')[0];
        if (dailyData.has(dateStr)) {
          dailyData.get(dateStr).users++;
        }
      });

      // Add daily rows
      Array.from(dailyData.entries())
        .sort(([a], [b]) => a.localeCompare(b))
        .forEach(([date, data]) => {
          csvData.push([date, data.orders, data.revenue.toFixed(2), data.users]);
        });

      // Convert to CSV string
      const csvContent = csvData.map(row =>
        row.map(cell => `"${cell}"`).join(',')
      ).join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `YalaOffice_Analytics_${exportDateRange.start}_to_${exportDateRange.end}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      setShowExportModal(false);
      alert('Analytics data exported successfully!');

    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Error exporting data: ' + (error as Error).message);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Period Selector and Actions */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Analytics & Reports</h3>
            <p className="text-sm text-gray-600">Real-time business intelligence and insights</p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2 px-3 py-2 text-teal-600 hover:text-teal-700 transition-colors"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
            <button
              onClick={() => setShowExportModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>Export Data</span>
            </button>
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="7days">Last 7 days</option>
              <option value="30days">Last 30 days</option>
              <option value="90days">Last 90 days</option>
              <option value="1year">Last year</option>
            </select>
          </div>
        </div>
      </div>

      {/* Real-time Statistics Cards */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 animate-pulse">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                </div>
                <div className="h-8 w-8 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      ) : analyticsData ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Revenue Card */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analyticsData.totalRevenue.toLocaleString()} Dh
                </p>
                <div className="flex items-center mt-1">
                  {(() => {
                    const growth = calculateGrowth(analyticsData.totalRevenue, analyticsData.previousPeriodRevenue);
                    return (
                      <>
                        <TrendingUp className={`h-4 w-4 mr-1 ${growth.trend === 'up' ? 'text-green-600' : 'text-red-600'}`} />
                        <span className={`text-sm ${growth.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                          {growth.trend === 'up' ? '+' : '-'}{growth.value.toFixed(1)}%
                        </span>
                      </>
                    );
                  })()}
                </div>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          {/* Total Orders Card */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analyticsData.totalOrders.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  {(() => {
                    const growth = calculateGrowth(analyticsData.totalOrders, analyticsData.previousPeriodOrders);
                    return (
                      <>
                        <TrendingUp className={`h-4 w-4 mr-1 ${growth.trend === 'up' ? 'text-green-600' : 'text-red-600'}`} />
                        <span className={`text-sm ${growth.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                          {growth.trend === 'up' ? '+' : '-'}{growth.value.toFixed(1)}%
                        </span>
                      </>
                    );
                  })()}
                </div>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          {/* Active Users Card */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analyticsData.activeUsers.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  {(() => {
                    const growth = calculateGrowth(analyticsData.activeUsers, analyticsData.previousPeriodUsers);
                    return (
                      <>
                        <TrendingUp className={`h-4 w-4 mr-1 ${growth.trend === 'up' ? 'text-green-600' : 'text-red-600'}`} />
                        <span className={`text-sm ${growth.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                          {growth.trend === 'up' ? '+' : '-'}{growth.value.toFixed(1)}%
                        </span>
                      </>
                    );
                  })()}
                </div>
              </div>
              <div className="bg-purple-100 p-3 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>

          {/* Product Sales Card */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Product Sales</p>
                <p className="text-2xl font-bold text-gray-900">
                  {analyticsData.productSales.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  {(() => {
                    const growth = calculateGrowth(analyticsData.productSales, analyticsData.previousPeriodSales);
                    return (
                      <>
                        <TrendingUp className={`h-4 w-4 mr-1 ${growth.trend === 'up' ? 'text-green-600' : 'text-red-600'}`} />
                        <span className={`text-sm ${growth.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                          {growth.trend === 'up' ? '+' : '-'}{growth.value.toFixed(1)}%
                        </span>
                      </>
                    );
                  })()}
                </div>
              </div>
              <div className="bg-amber-100 p-3 rounded-lg">
                <Package className="h-6 w-6 text-amber-600" />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <p className="text-center text-gray-500">Failed to load analytics data</p>
        </div>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend Chart */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
            <div className="text-sm text-gray-500">
              {selectedPeriod === '7days' && 'Daily Revenue (Last 7 Days)'}
              {selectedPeriod === '30days' && 'Daily Revenue (Last 30 Days)'}
              {selectedPeriod === '90days' && 'Weekly Revenue (Last 90 Days)'}
              {selectedPeriod === '1year' && 'Monthly Revenue (Last Year)'}
            </div>
          </div>
          {loading ? (
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg animate-pulse">
              <div className="text-gray-400">Loading chart...</div>
            </div>
          ) : analyticsData ? (
            <div className="h-64">
              <SimpleLineChart
                data={analyticsData.revenueTrend}
                color="rgb(20, 184, 166)"
                label="Revenue (Dh)"
              />
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <p className="text-gray-500">No revenue data available</p>
            </div>
          )}
        </div>

        {/* Order Volume Chart */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Order Volume</h3>
            <div className="text-sm text-gray-500">
              {selectedPeriod === '7days' && 'Daily Orders (Last 7 Days)'}
              {selectedPeriod === '30days' && 'Daily Orders (Last 30 Days)'}
              {selectedPeriod === '90days' && 'Weekly Orders (Last 90 Days)'}
              {selectedPeriod === '1year' && 'Monthly Orders (Last Year)'}
            </div>
          </div>
          {loading ? (
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg animate-pulse">
              <div className="text-gray-400">Loading chart...</div>
            </div>
          ) : analyticsData ? (
            <div className="h-64">
              <SimpleBarChart
                data={analyticsData.orderVolume}
                color="rgb(59, 130, 246)"
                label="Orders"
              />
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <p className="text-gray-500">No order data available</p>
            </div>
          )}
        </div>
      </div>

      {/* Detailed Reports */}
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Detailed Reports</h3>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="p-6 bg-gray-50 rounded-lg animate-pulse">
                  <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-4/6"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : detailedReports ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* User Analytics Report */}
              <div className="p-6 bg-blue-50 rounded-lg border border-blue-200 flex flex-col">
                <div className="flex items-center mb-4">
                  <Users className="h-6 w-6 text-blue-600 mr-3" />
                  <div>
                    <h4 className="text-lg font-semibold text-blue-900">User Analytics</h4>
                    <p className="text-sm text-blue-700">User behavior and engagement</p>
                  </div>
                </div>
                <div className="space-y-3 flex-grow">
                  <div className="flex justify-between">
                    <span className="text-sm text-blue-800">Total Users:</span>
                    <span className="font-semibold text-blue-900">{detailedReports.userAnalytics.totalUsers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-blue-800">Active Users:</span>
                    <span className="font-semibold text-blue-900">{detailedReports.userAnalytics.activeUsers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-blue-800">New Users (30d):</span>
                    <span className="font-semibold text-blue-900">{detailedReports.userAnalytics.newUsers}</span>
                  </div>
                  <div className="mt-4">
                    <p className="text-sm font-medium text-blue-800 mb-2">Users by Role:</p>
                    {detailedReports.userAnalytics.usersByRole.map((role, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-blue-700">{role.role}:</span>
                        <span className="text-blue-900">{role.count}</span>
                      </div>
                    ))}
                  </div>
                </div>
                <button
                  onClick={() => handleViewFullReport('user')}
                  className="mt-4 w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  View Full Report
                </button>
              </div>

              {/* Product Performance Report - Enhanced */}
              <div className="p-6 bg-green-50 rounded-lg border border-green-200 flex flex-col">
                <div className="flex items-center mb-4">
                  <Package className="h-6 w-6 text-green-600 mr-3" />
                  <div>
                    <h4 className="text-lg font-semibold text-green-900">Product Performance</h4>
                    <p className="text-sm text-green-700">Comprehensive product analytics and insights</p>
                  </div>
                </div>

                <div className="space-y-4 flex-grow">
                  {/* Top Products by Revenue */}
                  <div>
                    <p className="text-sm font-medium text-green-800 mb-2">Top Products by Revenue:</p>
                    {detailedReports.productPerformance.topProducts.slice(0, 3).map((product, index) => (
                      <div key={index} className="flex justify-between items-center text-sm mb-2 p-2 bg-white rounded">
                        <div className="flex-1">
                          <span className="text-green-700 font-medium truncate block">{product.name || `Product ${index + 1}`}</span>
                          <span className="text-green-600 text-xs">{product.sales} units sold</span>
                        </div>
                        <div className="text-right">
                          <span className="text-green-900 font-semibold">{product.revenue.toLocaleString()} Dh</span>
                          <div className="text-xs text-green-600">{product.margin.toFixed(1)}% margin</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Category Performance */}
                  <div>
                    <p className="text-sm font-medium text-green-800 mb-2">Category Performance:</p>
                    {detailedReports.productPerformance.categoryPerformance?.slice(0, 3).map((category, index) => (
                      <div key={index} className="flex justify-between text-sm mb-1">
                        <span className="text-green-700">{category.category}:</span>
                        <span className="text-green-900">{category.percentage.toFixed(1)}%</span>
                      </div>
                    )) || (
                      <div className="text-xs text-green-600 italic">Category data loading...</div>
                    )}
                  </div>

                  {/* Inventory Insights */}
                  <div>
                    <p className="text-sm font-medium text-green-800 mb-2">Inventory Insights:</p>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="bg-white p-2 rounded">
                        <div className="text-green-700">Turnover Rate</div>
                        <div className="font-semibold text-green-900">
                          {detailedReports.productPerformance.inventoryInsights?.turnoverRate?.toFixed(1) || '0.0'}x
                        </div>
                      </div>
                      <div className="bg-white p-2 rounded">
                        <div className="text-green-700">Low Stock</div>
                        <div className="font-semibold text-red-600">
                          {detailedReports.productPerformance.lowStockItems.length} items
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Low Stock Alerts */}
                  <div>
                    <p className="text-sm font-medium text-green-800 mb-2">Critical Stock Alerts:</p>
                    {detailedReports.productPerformance.lowStockItems.slice(0, 2).map((item, index) => (
                      <div key={index} className="flex justify-between text-sm mb-1 p-1 bg-red-50 rounded">
                        <span className="text-red-700 truncate mr-2">{item.name}:</span>
                        <span className="text-red-600 font-semibold">{item.stock} left</span>
                      </div>
                    ))}
                    {detailedReports.productPerformance.lowStockItems.length === 0 && (
                      <div className="text-xs text-green-600 italic">All products well stocked</div>
                    )}
                  </div>
                </div>

                {/* View Full Report Button - Aligned to bottom */}
                <button
                  onClick={() => handleViewFullReport('product')}
                  className="mt-4 w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                >
                  View Full Report
                </button>
              </div>

              {/* Sales Goals Report */}
              <div className="p-6 bg-purple-50 rounded-lg border border-purple-200 flex flex-col">
                <div className="flex items-center mb-4">
                  <Target className="h-6 w-6 text-purple-600 mr-3" />
                  <div>
                    <h4 className="text-lg font-semibold text-purple-900">Sales Goals</h4>
                    <p className="text-sm text-purple-700">Progress towards targets</p>
                  </div>
                </div>
                <div className="space-y-3 flex-grow">
                  <div className="flex justify-between">
                    <span className="text-sm text-purple-800">Monthly Target:</span>
                    <span className="font-semibold text-purple-900">{detailedReports.salesGoals.monthlyTarget.toLocaleString()} Dh</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-purple-800">Current Progress:</span>
                    <span className="font-semibold text-purple-900">{detailedReports.salesGoals.currentProgress.toLocaleString()} Dh</span>
                  </div>
                  <div className="mt-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-purple-800">Progress:</span>
                      <span className="text-purple-900">{detailedReports.salesGoals.progressPercentage.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-purple-200 rounded-full h-2">
                      <div
                        className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min(detailedReports.salesGoals.progressPercentage, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-purple-800">Daily Average:</span>
                    <span className="font-semibold text-purple-900">{detailedReports.salesGoals.dailyAverage.toLocaleString()} Dh</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-purple-800">Projected Total:</span>
                    <span className="font-semibold text-purple-900">{detailedReports.salesGoals.projectedTotal.toLocaleString()} Dh</span>
                  </div>
                </div>
                <button
                  onClick={() => handleViewFullReport('sales')}
                  className="mt-4 w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium"
                >
                  View Full Report
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">Failed to load detailed reports</p>
            </div>
          )}
        </div>
      </div>
      {/* Export Modal */}
      {showExportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Export Analytics Data</h2>
                <button
                  onClick={() => setShowExportModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Export Date Range
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">From</label>
                      <input
                        type="date"
                        value={exportDateRange.start}
                        onChange={(e) => setExportDateRange({ ...exportDateRange, start: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500 mb-1">To</label>
                      <input
                        type="date"
                        value={exportDateRange.end}
                        onChange={(e) => setExportDateRange({ ...exportDateRange, end: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quick Select
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => {
                        const end = new Date().toISOString().split('T')[0];
                        const start = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                        setExportDateRange({ start, end });
                      }}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50"
                    >
                      Last 7 days
                    </button>
                    <button
                      onClick={() => {
                        const end = new Date().toISOString().split('T')[0];
                        const start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                        setExportDateRange({ start, end });
                      }}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50"
                    >
                      Last 30 days
                    </button>
                    <button
                      onClick={() => {
                        const end = new Date().toISOString().split('T')[0];
                        const start = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                        setExportDateRange({ start, end });
                      }}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50"
                    >
                      Last 90 days
                    </button>
                    <button
                      onClick={() => {
                        const end = new Date().toISOString().split('T')[0];
                        const start = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                        setExportDateRange({ start, end });
                      }}
                      className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50"
                    >
                      Last year
                    </button>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowExportModal(false)}
                  className="flex-1 px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleExportData}
                  className="flex-1 bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 transition-colors flex items-center justify-center space-x-2"
                >
                  <Download className="h-4 w-4" />
                  <span>Export CSV</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detailed Report Modal */}
      {showDetailedReportModal && selectedReportType && (
        <DetailedReportModal
          isOpen={showDetailedReportModal}
          onClose={handleCloseDetailedReport}
          reportType={selectedReportType}
          data={detailedReports}
        />
      )}
    </div>
  );
};

// Simple Chart Components
const SimpleLineChart = ({ data, color, label }: { data: number[]; color: string; label: string }) => {
  const maxValue = Math.max(...data, 1);
  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = 100 - (value / maxValue) * 80;
    return `${x},${y}`;
  }).join(' ');

  return (
    <div className="relative h-full">
      <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
        <polyline
          fill="none"
          stroke={color}
          strokeWidth="2"
          points={points}
          vectorEffect="non-scaling-stroke"
        />
        {data.map((value, index) => {
          const x = (index / (data.length - 1)) * 100;
          const y = 100 - (value / maxValue) * 80;
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r="2"
              fill={color}
              vectorEffect="non-scaling-stroke"
            />
          );
        })}
      </svg>
      <div className="absolute bottom-0 left-0 text-xs text-gray-500">
        {label}: {data.reduce((sum, val) => sum + val, 0).toLocaleString()}
      </div>
    </div>
  );
};

const SimpleBarChart = ({ data, color, label }: { data: number[]; color: string; label: string }) => {
  const maxValue = Math.max(...data, 1);
  const barWidth = 100 / data.length;

  return (
    <div className="relative h-full">
      <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
        {data.map((value, index) => {
          const height = (value / maxValue) * 80;
          const x = index * barWidth;
          const y = 100 - height;
          return (
            <rect
              key={index}
              x={x}
              y={y}
              width={barWidth * 0.8}
              height={height}
              fill={color}
              opacity={0.8}
            />
          );
        })}
      </svg>
      <div className="absolute bottom-0 left-0 text-xs text-gray-500">
        {label}: {data.reduce((sum, val) => sum + val, 0).toLocaleString()}
      </div>
    </div>
  );
};

export default AnalyticsPage;
