import { useState } from 'react';
import { ArrowLeft, Home } from 'lucide-react';

interface PageNavigationProps {
  currentPage: string;
  onNavigate: (page: string) => void;
  breadcrumbs?: { label: string; page: string }[];
  title: string;
  subtitle?: string;
}

const PageNavigation = ({ 
  currentPage, 
  onNavigate, 
  breadcrumbs = [], 
  title, 
  subtitle 
}: PageNavigationProps) => {
  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      {/* Breadcrumbs */}
      <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
        <button
          onClick={() => onNavigate('dashboard')}
          className="flex items-center hover:text-orange-600 transition-colors"
        >
          <Home className="h-4 w-4 mr-1" />
          Dashboard
        </button>
        
        {breadcrumbs.map((crumb, index) => (
          <div key={index} className="flex items-center space-x-2">
            <span>/</span>
            <button
              onClick={() => onNavigate(crumb.page)}
              className="hover:text-orange-600 transition-colors"
            >
              {crumb.label}
            </button>
          </div>
        ))}
        
        {currentPage !== 'dashboard' && (
          <div className="flex items-center space-x-2">
            <span>/</span>
            <span className="text-gray-900 font-medium">{title}</span>
          </div>
        )}
      </div>

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {currentPage !== 'dashboard' && (
            <button
              onClick={() => onNavigate('dashboard')}
              className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
          )}
          
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            {subtitle && (
              <p className="text-gray-600 mt-1">{subtitle}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PageNavigation;
