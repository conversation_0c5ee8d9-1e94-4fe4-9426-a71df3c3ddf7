
import { useState, useEffect } from 'react';
import { Plug, Webhook, Download, Upload, Settings, RefreshCw } from 'lucide-react';
import { APIEndpoint, Webhook as WebhookType, ImportExportJob, ERPIntegration } from '../../types/integration';
import { getAPIEndpoints, getWebhooks, createExportJob, getERPIntegrations } from '../../services/integrationService';

const IntegrationDashboard = () => {
  const [activeTab, setActiveTab] = useState('api');
  const [apiEndpoints, setApiEndpoints] = useState<APIEndpoint[]>([]);
  const [webhooks, setWebhooks] = useState<WebhookType[]>([]);
  const [exportJobs, setExportJobs] = useState<ImportExportJob[]>([]);
  const [erpIntegrations, setErpIntegrations] = useState<ERPIntegration[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [endpoints, webhookData, erpData] = await Promise.all([
        getAPIEndpoints(),
        getWebhooks(),
        getERPIntegrations()
      ]);
      
      setApiEndpoints(endpoints);
      setWebhooks(webhookData);
      setErpIntegrations(erpData);
    } catch (error) {
      console.error('Error loading integration data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (entity: string, format: string) => {
    const job = await createExportJob(entity, format);
    setExportJobs(prev => [...prev, job]);
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Integration Management</h2>
          <button
            onClick={loadData}
            disabled={loading}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
          {[
            { id: 'api', label: 'API Endpoints', icon: Plug },
            { id: 'webhooks', label: 'Webhooks', icon: Webhook },
            { id: 'import-export', label: 'Import/Export', icon: Download },
            { id: 'erp', label: 'ERP Systems', icon: Settings }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* API Endpoints Tab */}
        {activeTab === 'api' && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">API Endpoints</h3>
              <span className="text-sm text-gray-500">{apiEndpoints.length} endpoints</span>
            </div>
            <div className="grid gap-4">
              {apiEndpoints.map(endpoint => (
                <div key={endpoint.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${
                        endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                        endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                        endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {endpoint.method}
                      </span>
                      <span className="font-medium">{endpoint.name}</span>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded ${
                      endpoint.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {endpoint.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{endpoint.description}</p>
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded">{endpoint.path}</code>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Webhooks Tab */}
        {activeTab === 'webhooks' && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Webhooks</h3>
              <span className="text-sm text-gray-500">{webhooks.length} webhooks</span>
            </div>
            <div className="grid gap-4">
              {webhooks.map(webhook => (
                <div key={webhook.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{webhook.name}</h4>
                    <span className={`px-2 py-1 text-xs rounded ${
                      webhook.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {webhook.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{webhook.url}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>Success: {webhook.successCount}</span>
                    <span>Failures: {webhook.failureCount}</span>
                    {webhook.lastTriggered && (
                      <span>Last: {new Date(webhook.lastTriggered).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Import/Export Tab */}
        {activeTab === 'import-export' && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Import/Export Operations</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium mb-3">Export Data</h4>
                <div className="space-y-2">
                  {['products', 'orders', 'customers', 'inventory'].map(entity => (
                    <div key={entity} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{entity}</span>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleExport(entity, 'csv')}
                          className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200"
                        >
                          CSV
                        </button>
                        <button
                          onClick={() => handleExport(entity, 'excel')}
                          className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded hover:bg-green-200"
                        >
                          Excel
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium mb-3">Recent Jobs</h4>
                <div className="space-y-2">
                  {exportJobs.slice(0, 5).map(job => (
                    <div key={job.id} className="flex items-center justify-between text-sm">
                      <span>{job.fileName}</span>
                      <span className={`px-2 py-1 text-xs rounded ${
                        job.status === 'completed' ? 'bg-green-100 text-green-800' :
                        job.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {job.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* ERP Systems Tab */}
        {activeTab === 'erp' && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">ERP Integrations</h3>
              <span className="text-sm text-gray-500">{erpIntegrations.length} systems</span>
            </div>
            <div className="grid gap-4">
              {erpIntegrations.map(erp => (
                <div key={erp.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium">{erp.name}</h4>
                      <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded uppercase">
                        {erp.type}
                      </span>
                    </div>
                    <span className={`px-2 py-1 text-xs rounded ${
                      erp.isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {erp.isConnected ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p>Sync Frequency: {erp.syncFrequency}</p>
                    {erp.lastSync && (
                      <p>Last Sync: {new Date(erp.lastSync).toLocaleString()}</p>
                    )}
                    <p>Mappings: {erp.mappings.length} fields</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default IntegrationDashboard;
