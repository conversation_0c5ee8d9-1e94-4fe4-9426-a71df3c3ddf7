
import { useState, useEffect } from 'react';
import { Heart, X, ShoppingCart, Star } from 'lucide-react';
import { Wishlist } from '../../types/customer';
import { getWishlist, removeFromWishlist } from '../../services/customerService';

interface WishlistManagerProps {
  customerId: string;
  onAddToCart: (productId: number) => void;
}

const WishlistManager = ({ customerId, onAddToCart }: WishlistManagerProps) => {
  const [wishlistItems, setWishlistItems] = useState<Wishlist[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadWishlist();
  }, [customerId]);

  const loadWishlist = async () => {
    try {
      setLoading(true);
      const items = await getWishlist(customerId);
      setWishlistItems(items);
    } catch (error) {
      console.error('Error loading wishlist:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveItem = async (productId: number) => {
    try {
      await removeFromWishlist(customerId, productId);
      setWishlistItems(prev => prev.filter(item => item.productId !== productId));
    } catch (error) {
      console.error('Error removing from wishlist:', error);
    }
  };

  const handleAddToCart = (productId: number) => {
    onAddToCart(productId);
    // Optionally remove from wishlist after adding to cart
    handleRemoveItem(productId);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Heart className="h-8 w-8 animate-pulse text-teal-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">My Wishlist</h2>
        <span className="text-sm text-gray-500">
          {wishlistItems.length} {wishlistItems.length === 1 ? 'item' : 'items'}
        </span>
      </div>

      {wishlistItems.length === 0 ? (
        <div className="text-center py-12">
          <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Your wishlist is empty</h3>
          <p className="text-gray-500">Start adding products you love to keep track of them</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {wishlistItems.map(item => (
            <div key={item.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative">
                <img
                  src="/placeholder.svg"
                  alt="Product"
                  className="w-full h-48 object-cover"
                />
                <button
                  onClick={() => handleRemoveItem(item.productId)}
                  className="absolute top-2 right-2 p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                >
                  <X className="h-4 w-4 text-gray-600" />
                </button>
              </div>

              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2">
                  Product #{item.productId}
                </h3>
                
                <div className="flex items-center space-x-1 mb-2">
                  {[1, 2, 3, 4, 5].map(star => (
                    <Star
                      key={star}
                      className="h-4 w-4 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                  <span className="text-sm text-gray-500 ml-1">(4.5)</span>
                </div>

                <p className="text-lg font-bold text-teal-600 mb-3">
                  150.00 Dh
                </p>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handleAddToCart(item.productId)}
                    className="flex-1 flex items-center justify-center space-x-2 bg-teal-600 text-white py-2 px-4 rounded-lg hover:bg-teal-700 transition-colors"
                  >
                    <ShoppingCart className="h-4 w-4" />
                    <span>Add to Cart</span>
                  </button>
                </div>
                
                <p className="text-xs text-gray-500 mt-2">
                  Added {new Date(item.addedAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default WishlistManager;
