
import { useState, useEffect } from 'react';
import { X, Upload, Image as ImageIcon, Building2 } from 'lucide-react';
import { Product, Category } from '../../types/inventory';
import { Branch } from '../../types/branch';
import ImageService from '../../services/imageService';
import { getBranches } from '../../services/branchService';

interface ProductFormProps {
  product: Product | null;
  categories: Category[];
  onSave: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'> & { branchId: string }) => void;
  onCancel: () => void;
  onImageUpdate?: (images: { featuredImage: string; thumbnailImages: string[] }) => void;
}

const ProductForm = ({ product, categories, onSave, onCancel, onImageUpdate }: ProductFormProps) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    brand: '',
    price: 0,
    resellerPrice: 0,
    image: '/placeholder.svg',
    featuredImage: '/placeholder.svg',
    thumbnailImages: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    rating: 0,
    stock: 0,
    minStock: 0,
    isActive: true,
    isNew: false,
    sku: '',
    weight: 0,
    dimensions: {
      length: 0,
      width: 0,
      height: 0
    },
    tags: [] as string[],
    branchId: ''
  });

  const [branches, setBranches] = useState<Branch[]>([]);
  const [loadingBranches, setLoadingBranches] = useState(true);

  const [tagInput, setTagInput] = useState('');
  const [featuredImage, setFeaturedImage] = useState<File | null>(null);
  const [galleryImages, setGalleryImages] = useState<File[]>([]);
  const [uploadingImages, setUploadingImages] = useState(false);

  // Load branches on component mount
  useEffect(() => {
    const loadBranches = async () => {
      try {
        setLoadingBranches(true);
        const branchData = await getBranches(true); // Only active branches
        setBranches(branchData);

        // Set default branch if creating new product
        if (!product && branchData.length > 0) {
          setFormData(prev => ({ ...prev, branchId: branchData[0].id }));
        }
      } catch (error) {
        console.error('Error loading branches:', error);
      } finally {
        setLoadingBranches(false);
      }
    };

    loadBranches();
  }, [product]);

  useEffect(() => {
    if (product) {
      console.log('ProductForm: Initializing form with product:', product);
      console.log('ProductForm: Available categories:', categories);

      // When editing, we need to handle category properly
      // The product.category might be a UUID (from database) or a name (display value)
      let categoryValue = product.category;

      // Check if the category is a UUID (database category) or a name (predefined category)
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(product.category);

      if (isUUID) {
        // It's a UUID, use it directly for the form
        categoryValue = product.category;
        console.log('ProductForm: Using UUID category:', categoryValue);
      } else {
        // It's a name, try to find the corresponding category ID
        const category = categories.find(cat => cat.name === product.category);
        if (category) {
          categoryValue = category.id;
          console.log('ProductForm: Found category ID for name:', product.category, '->', categoryValue);
        } else {
          // Keep the name if no matching category found
          categoryValue = product.category;
          console.log('ProductForm: No matching category found, keeping name:', categoryValue);
        }
      }

      setFormData({
        title: product.title,
        description: product.description,
        category: categoryValue,
        brand: product.brand,
        price: product.price,
        resellerPrice: product.resellerPrice,
        image: product.image,
        featuredImage: product.featuredImage,
        thumbnailImages: product.thumbnailImages,
        rating: product.rating,
        stock: product.stock,
        minStock: product.minStock,
        isActive: product.isActive,
        isNew: product.isNew,
        sku: product.sku,
        weight: product.weight || 0,
        dimensions: product.dimensions || { length: 0, width: 0, height: 0 },
        tags: product.tags || [],
        branchId: (product as any).branchId || '' // Add branch ID from product
      });

      // Load branch information for existing product if not already present
      if (product.id && !(product as any).branchId) {
        const loadProductBranch = async () => {
          try {
            const { getProductPrimaryBranch } = await import('../../services/branchInventoryService');
            const primaryBranchId = await getProductPrimaryBranch(product.id);
            if (primaryBranchId) {
              setFormData(prev => ({ ...prev, branchId: primaryBranchId }));
            }
          } catch (error) {
            console.error('Error loading product branch:', error);
          }
        };
        loadProductBranch();
      }
    }
  }, [product, categories]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setUploadingImages(true);

    try {
      console.log('ProductForm: Submitting form data:', formData);
      console.log('ProductForm: Featured image file:', featuredImage);
      console.log('ProductForm: Gallery images files:', galleryImages);

      let updatedFormData = { ...formData };

      // Upload featured image if a new one is selected
      if (featuredImage) {
        console.log('ProductForm: Uploading featured image...');
        const featuredImageResult = await ImageService.uploadImage(featuredImage, 'products/featured');

        if (featuredImageResult.success && featuredImageResult.url) {
          updatedFormData.featuredImage = featuredImageResult.url;
          console.log('ProductForm: Featured image uploaded:', featuredImageResult.url);
        } else {
          console.error('ProductForm: Featured image upload failed:', featuredImageResult.error);
          alert('Failed to upload featured image: ' + featuredImageResult.error);
          return;
        }
      }

      // Gallery images are now uploaded immediately when selected, so just use the current formData
      // Ensure we have valid gallery images (filter out placeholders)
      const validGalleryImages = updatedFormData.thumbnailImages.filter(img =>
        img && img !== '/placeholder.svg' && img.trim() !== ''
      );

      if (validGalleryImages.length > 0) {
        updatedFormData.thumbnailImages = validGalleryImages;
        console.log('ProductForm: Using existing gallery images:', validGalleryImages);
      } else {
        // If no valid gallery images, set to empty array or placeholder
        updatedFormData.thumbnailImages = [];
        console.log('ProductForm: No valid gallery images found');
      }

      console.log('ProductForm: Final form data with images:', updatedFormData);
      onSave(updatedFormData);

    } catch (error) {
      console.error('ProductForm: Error during image upload:', error);
      alert('Error uploading images: ' + (error as Error).message);
    } finally {
      setUploadingImages(false);
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !(formData.tags || []).includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: (prev.tags || []).filter(tag => tag !== tagToRemove)
    }));
  };

  const handleFeaturedImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFeaturedImage(e.target.files[0]);
    }
  };

  const handleFeaturedImageReplace = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      try {
        // Upload the new featured image immediately
        const uploadResult = await ImageService.uploadImage(file, 'products/featured');

        if (uploadResult.success && uploadResult.url) {
          // Update the featured image URL directly
          setFormData(prev => {
            const newData = { ...prev, featuredImage: uploadResult.url };
            // Notify parent of image update
            onImageUpdate?.({
              featuredImage: newData.featuredImage,
              thumbnailImages: newData.thumbnailImages
            });
            return newData;
          });
          setFeaturedImage(null); // Clear the file since it's already uploaded

          console.log('Featured image replaced successfully:', uploadResult.url);
        } else {
          alert('Failed to upload replacement featured image: ' + (uploadResult.error || 'Unknown error'));
        }
      } catch (error) {
        console.error('Error replacing featured image:', error);
        alert('Error replacing featured image: ' + (error as Error).message);
      }
    }
  };

  const handleGalleryImagesChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      const existingImages = formData.thumbnailImages.filter(img => img !== '/placeholder.svg');

      if (existingImages.length + files.length > 4) {
        alert(`Maximum 4 gallery images allowed. You can add ${4 - existingImages.length} more image(s).`);
        return;
      }

      // Upload images immediately and update the preview
      try {
        console.log('ProductForm: Uploading new gallery images immediately...');
        const galleryResults = await ImageService.uploadMultipleImages(files, 'products/gallery');

        const successfulUploads = galleryResults.filter(result => result.success);
        const failedUploads = galleryResults.filter(result => !result.success);

        if (failedUploads.length > 0) {
          console.error('ProductForm: Some gallery images failed to upload:', failedUploads);
          alert(`Failed to upload ${failedUploads.length} gallery image(s).`);
        }

        if (successfulUploads.length > 0) {
          const galleryUrls = successfulUploads.map(result => result.url!);
          const combinedImages = [...existingImages, ...galleryUrls];

          // Update formData immediately to show in preview
          setFormData(prev => {
            const newData = {
              ...prev,
              thumbnailImages: combinedImages
            };
            // Notify parent of image update
            onImageUpdate?.({
              featuredImage: newData.featuredImage,
              thumbnailImages: newData.thumbnailImages
            });
            return newData;
          });

          console.log('ProductForm: Gallery images uploaded and added to preview:', combinedImages);
        }
      } catch (error) {
        console.error('ProductForm: Error uploading gallery images:', error);
        alert('Error uploading gallery images: ' + (error as Error).message);
      }

      // Clear the file input
      e.target.value = '';
    }
  };

  const handleGalleryImageReplace = async (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      try {
        // Upload the new image
        const uploadResult = await ImageService.uploadImage(file, 'products/gallery');

        if (uploadResult.success && uploadResult.url) {
          // Replace the image at the specific index
          const newThumbnails = [...formData.thumbnailImages];
          newThumbnails[index] = uploadResult.url;
          setFormData(prev => {
            const newData = { ...prev, thumbnailImages: newThumbnails };
            // Notify parent of image update
            onImageUpdate?.({
              featuredImage: newData.featuredImage,
              thumbnailImages: newData.thumbnailImages
            });
            return newData;
          });

          console.log(`Gallery image ${index} replaced successfully:`, uploadResult.url);
        } else {
          alert('Failed to upload replacement image: ' + (uploadResult.error || 'Unknown error'));
        }
      } catch (error) {
        console.error('Error replacing gallery image:', error);
        alert('Error replacing image: ' + (error as Error).message);
      }
    }

    // Clear the file input
    e.target.value = '';
  };

  const handleGalleryImageDelete = (index: number) => {
    const newThumbnails = [...formData.thumbnailImages];
    newThumbnails.splice(index, 1);

    // If no images left, add placeholder
    if (newThumbnails.length === 0) {
      newThumbnails.push('/placeholder.svg');
    }

    setFormData(prev => {
      const newData = { ...prev, thumbnailImages: newThumbnails };
      // Notify parent of image update
      onImageUpdate?.({
        featuredImage: newData.featuredImage,
        thumbnailImages: newData.thumbnailImages
      });
      return newData;
    });
    console.log(`Gallery image ${index} deleted. Remaining images:`, newThumbnails);
  };

  // All product categories including the predefined ones
  const predefinedCategories = [
    'Writing Instruments',
    'Paper & Notebooks',
    'Office & Desk Accessories',
    'Technology',
    'Storage & Organization',
    'Art & Craft Supplies',
    'Printing & Copying',
    'Furniture',
    'School & Office Supplies',
    'Filing & Organization',
    'Greeting Cards & Gift Supplies',
    'Back-to-School Essentials',
    'Eco-Friendly Stationery',
    'Specialty & Luxury Stationery'
  ];

  // Helper function to get category ID by name
  const getCategoryIdByName = (categoryName: string): string => {
    const category = categories.find(cat => cat.name === categoryName);
    return category ? category.id : categoryName; // Return ID if found, otherwise return name for predefined categories
  };

  // Helper function to get category name by ID
  const getCategoryNameById = (categoryId: string): string => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : categoryId; // Return name if found, otherwise return the ID as is
  };

  // Combine categories from database with predefined ones
  const categoryNames = categories.map(cat => cat.name);
  const allCategoryNames = [...new Set([...categoryNames, ...predefinedCategories])];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {product ? 'Edit Product' : 'Add New Product'}
            </h2>
            <button onClick={onCancel} className="text-gray-400 hover:text-gray-600">
              <X className="h-6 w-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                <input
                  type="text"
                  required
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  SKU
                  <span className="text-xs text-gray-500 ml-1">(Stock Keeping Unit - unique identifier)</span>
                </label>
                <input
                  type="text"
                  required
                  value={formData.sku}
                  onChange={(e) => setFormData(prev => ({ ...prev, sku: e.target.value }))}
                  placeholder="e.g., PRD-001, ABC-123"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                required
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select
                  required
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="">Select Category</option>
                  {/* Database categories with IDs */}
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                  {/* Predefined categories without IDs */}
                  {predefinedCategories.filter(name => !categoryNames.includes(name)).map((categoryName, index) => (
                    <option key={`predefined-${index}`} value={categoryName}>
                      {categoryName}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <Building2 className="inline h-4 w-4 mr-1" />
                  Branch *
                </label>
                <select
                  required
                  value={formData.branchId}
                  onChange={(e) => setFormData(prev => ({ ...prev, branchId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  disabled={loadingBranches}
                >
                  <option value="">Select Branch</option>
                  {branches.map((branch) => (
                    <option key={branch.id} value={branch.id}>
                      {branch.name} - {branch.address.city}
                    </option>
                  ))}
                </select>
                {loadingBranches && (
                  <p className="text-sm text-gray-500 mt-1">Loading branches...</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Brand <span className="text-xs text-gray-500">(optional)</span>
                </label>
                <input
                  type="text"
                  value={formData.brand}
                  onChange={(e) => setFormData(prev => ({ ...prev, brand: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Image Upload Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Product Images</h3>
              
              {/* Featured Image */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Featured Image
                  <span className="text-xs text-gray-500 ml-1">(Recommended: 800px × 800px)</span>
                </label>

                {/* Current Featured Image Preview */}
                {(formData.featuredImage && formData.featuredImage !== '/placeholder.svg') && (
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2">Current featured image:</p>
                    <div className="relative inline-block">
                      <img
                        src={formData.featuredImage}
                        alt="Current featured image"
                        className="w-32 h-32 object-cover rounded-lg border border-gray-200"
                        onError={(e) => {
                          e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.title || 'Product')}&background=f97316&color=fff&size=128`;
                        }}
                      />
                      {/* Delete Featured Image Button */}
                      <button
                        type="button"
                        onClick={() => {
                          setFormData(prev => ({ ...prev, featuredImage: '/placeholder.svg' }));
                          setFeaturedImage(null);
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                        title="Delete featured image"
                      >
                        <X className="h-4 w-4" />
                      </button>
                      {/* Replace Featured Image Button */}
                      <label
                        htmlFor="featured-image-replace"
                        className="absolute -bottom-2 -right-2 bg-blue-500 text-white rounded-full p-1 hover:bg-blue-600 transition-colors cursor-pointer"
                        title="Replace featured image"
                      >
                        <ImageIcon className="h-4 w-4" />
                        <input
                          id="featured-image-replace"
                          type="file"
                          accept="image/*"
                          className="sr-only"
                          onChange={handleFeaturedImageReplace}
                        />
                      </label>
                    </div>
                  </div>
                )}

                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                  <div className="text-center">
                    <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                      <label htmlFor="featured-image" className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-900">
                          {formData.featuredImage && formData.featuredImage !== '/placeholder.svg' ? 'Replace featured image' : 'Upload featured image'}
                        </span>
                        <input
                          id="featured-image"
                          name="featured-image"
                          type="file"
                          accept="image/*"
                          className="sr-only"
                          onChange={handleFeaturedImageChange}
                        />
                      </label>
                    </div>
                    {featuredImage && (
                      <p className="mt-2 text-sm text-gray-500">
                        New image selected: {featuredImage.name}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Gallery Images */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Gallery Images
                  <span className="text-xs text-gray-500 ml-1">(Max 4 images, Recommended: 800px × 800px each)</span>
                </label>

                {/* Current Gallery Images Preview */}
                {formData.thumbnailImages && formData.thumbnailImages.length > 0 && formData.thumbnailImages.some(img => img !== '/placeholder.svg') && (
                  <div className="mb-4">
                    <p className="text-sm text-gray-600 mb-2">Current gallery images:</p>
                    <div className="flex flex-wrap gap-2">
                      {formData.thumbnailImages.filter(img => img !== '/placeholder.svg').map((image, originalIndex) => {
                        const actualIndex = formData.thumbnailImages.indexOf(image);
                        return (
                          <div key={actualIndex} className="relative group">
                            <img
                              src={image}
                              alt={`Gallery image ${originalIndex + 1}`}
                              className="w-20 h-20 object-cover rounded-lg border border-gray-200"
                              onError={(e) => {
                                e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(formData.title || 'Product')}&background=f97316&color=fff&size=80`;
                              }}
                            />
                            {/* Delete Gallery Image Button */}
                            <button
                              type="button"
                              onClick={() => handleGalleryImageDelete(actualIndex)}
                              className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                              title="Delete this image"
                            >
                              <X className="h-3 w-3" />
                            </button>
                            {/* Replace Gallery Image Button */}
                            <label
                              htmlFor={`gallery-replace-${actualIndex}`}
                              className="absolute -bottom-1 -right-1 bg-blue-500 text-white rounded-full p-1 hover:bg-blue-600 transition-colors opacity-0 group-hover:opacity-100 cursor-pointer"
                              title="Replace this image"
                            >
                              <ImageIcon className="h-3 w-3" />
                              <input
                                id={`gallery-replace-${actualIndex}`}
                                type="file"
                                accept="image/*"
                                className="sr-only"
                                onChange={(e) => handleGalleryImageReplace(e, actualIndex)}
                              />
                            </label>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                  <div className="text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                      <label htmlFor="gallery-images" className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-900">
                          {formData.thumbnailImages && formData.thumbnailImages.some(img => img !== '/placeholder.svg')
                            ? 'Replace gallery images (maximum 4)'
                            : 'Upload gallery images (maximum 4)'}
                        </span>
                        <input
                          id="gallery-images"
                          name="gallery-images"
                          type="file"
                          accept="image/*"
                          multiple
                          className="sr-only"
                          onChange={handleGalleryImagesChange}
                        />
                      </label>
                    </div>
                    {galleryImages.length > 0 && (
                      <p className="mt-2 text-sm text-gray-500">
                        New images selected: {galleryImages.length} image(s)
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Pricing */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Price (Dh)</label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  value={formData.price}
                  onChange={(e) => setFormData(prev => ({ ...prev, price: parseFloat(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Reseller Price (Dh)</label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  value={formData.resellerPrice}
                  onChange={(e) => setFormData(prev => ({ ...prev, resellerPrice: parseFloat(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Stock */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Current Stock</label>
                <input
                  type="number"
                  min="0"
                  required
                  value={formData.stock}
                  onChange={(e) => setFormData(prev => ({ ...prev, stock: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Minimum Stock</label>
                <input
                  type="number"
                  min="0"
                  required
                  value={formData.minStock}
                  onChange={(e) => setFormData(prev => ({ ...prev, minStock: parseInt(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tags</label>
              <div className="flex gap-2 mb-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                  placeholder="Add a tag..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                />
                <button
                  type="button"
                  onClick={handleAddTag}
                  className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700"
                >
                  Add
                </button>
              </div>
              <div className="flex flex-wrap gap-2">
                {(formData.tags || []).map(tag => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-2 py-1 bg-teal-100 text-teal-800 text-sm rounded-full"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-2 text-teal-600 hover:text-teal-800"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={uploadingImages}
                className={`px-6 py-2 bg-gradient-to-r from-teal-600 to-teal-700 text-white rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 flex items-center ${
                  uploadingImages ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {uploadingImages && (
                  <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                )}
                {uploadingImages
                  ? 'Uploading Images...'
                  : (product ? 'Update Product' : 'Create Product')
                }
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ProductForm;
