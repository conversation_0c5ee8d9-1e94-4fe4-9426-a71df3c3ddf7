
import { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Tag, AlertTriangle } from 'lucide-react';
import { Category } from '../../types/inventory';
import { useSyncedCategories, useCategoryOperations } from '../../hooks/useSyncedData';
import { updateCategory as updateCategoryService } from '../../services/liveCategoryService';
import { supabase } from '../../integrations/supabase/client';

interface CategoryManagementProps {
  currentUserId?: string;
}

const CategoryManagement = ({ currentUserId = 'USR-001' }: CategoryManagementProps) => {
  // Use synchronized data hooks
  const { data: categories, loading, error, refetch } = useSyncedCategories();
  const { createCategory, updateCategory, deleteCategory, loading: operationLoading, error: operationError } = useCategoryOperations();

  const [showForm, setShowForm] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#3B82F6'
  });

  // Set up real-time subscription for immediate UI updates
  useEffect(() => {
    const subscription = supabase
      .channel('categories_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'categories' }, (payload) => {
        console.log('CategoryManagement: Real-time category change detected:', payload);
        // Refresh categories data when changes occur
        refetch();
      })
      .subscribe();

    // Also listen to real-time service events
    const handleCategoryUpdate = (eventData: any) => {
      console.log('CategoryManagement: Real-time service category update:', eventData);
      refetch();
    };

    // Subscribe to real-time service events (if available)
    if (typeof window !== 'undefined' && (window as any).realTimeService) {
      (window as any).realTimeService.subscribe('category-updated', handleCategoryUpdate);
    }

    return () => {
      subscription.unsubscribe();
      if (typeof window !== 'undefined' && (window as any).realTimeService) {
        (window as any).realTimeService.unsubscribe('category-updated', handleCategoryUpdate);
      }
    };
  }, [refetch]);

  // Debug function to check current categories data
  const debugCategories = () => {
    console.log('CategoryManagement: Current categories data:', categories);
    console.log('CategoryManagement: Categories with isActive status:', categories.map(c => ({
      id: c.id,
      name: c.name,
      isActive: c.isActive
    })));
  };

  // Call debug function when categories change
  useEffect(() => {
    if (categories && categories.length > 0) {
      debugCategories();
    }
  }, [categories]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (selectedCategory) {
        const result = await updateCategory(selectedCategory.id, {
          name: formData.name,
          description: formData.description,
          color: formData.color
        });

        if (result.success) {
          setShowForm(false);
          setSelectedCategory(null);
          setFormData({ name: '', description: '', color: '#3B82F6' });
          // Ensure immediate UI update
          await refetch();
          alert('Category updated successfully!');
        } else {
          alert(result.error || 'Failed to update category');
        }
      } else {
        const result = await createCategory({
          name: formData.name,
          description: formData.description,
          color: formData.color
        });

        if (result.success) {
          setShowForm(false);
          setSelectedCategory(null);
          setFormData({ name: '', description: '', color: '#3B82F6' });
          // Ensure immediate UI update
          await refetch();
          alert('Category created successfully!');
        } else {
          alert(result.error || 'Failed to create category');
        }
      }
    } catch (error) {
      console.error('Error saving category:', error);
      alert('Error saving category');
    }
  };

  const handleEdit = (category: Category) => {
    setSelectedCategory(category);
    setFormData({
      name: category.name,
      description: category.description || '',
      color: category.color || '#3B82F6'
    });
    setShowForm(true);
  };

  const handleToggleActive = async (category: Category) => {
    try {
      console.log('CategoryManagement: Toggling category status:', {
        categoryId: category.id,
        currentStatus: category.isActive,
        newStatus: !category.isActive
      });

      // Use the hook's updateCategory function for consistency
      const result = await updateCategory(category.id, {
        isActive: !category.isActive
      });

      console.log('CategoryManagement: Update result:', result);

      if (result.success) {
        console.log('CategoryManagement: Update successful, refreshing data...');
        // Force refresh the data to ensure UI updates
        await refetch();
        console.log('CategoryManagement: Data refreshed');
        alert(`Category ${!category.isActive ? 'activated' : 'deactivated'} successfully!`);
      } else {
        console.error('CategoryManagement: Update failed:', result.error);
        alert(result.error || 'Failed to update category status');
      }
    } catch (error) {
      console.error('Error updating category:', error);
      alert('Error updating category: ' + (error as Error).message);
    }
  };

  const handleDeleteCategory = async (category: Category) => {
    // Show confirmation dialog
    const confirmMessage = `Are you sure you want to delete the category "${category.name}"?\n\nThis action cannot be undone and may affect products associated with this category.`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      console.log('CategoryManagement: Deleting category:', { categoryId: category.id, categoryName: category.name });

      // Check if category has associated products
      const { data: products, error: productsError } = await supabase
        .from('products')
        .select('id, title')
        .eq('category_id', category.id)
        .limit(5);

      if (productsError) {
        console.error('Error checking category products:', productsError);
        alert('Error checking category associations');
        return;
      }

      if (products && products.length > 0) {
        const productNames = products.map(p => p.title).join(', ');
        const moreProducts = products.length === 5 ? ' and possibly more' : '';

        const confirmWithProducts = window.confirm(
          `This category is associated with ${products.length} product(s): ${productNames}${moreProducts}.\n\nDeleting this category will remove these associations. Continue?`
        );

        if (!confirmWithProducts) {
          return;
        }
      }

      // Delete the category
      const { error: deleteError } = await supabase
        .from('categories')
        .delete()
        .eq('id', category.id);

      if (deleteError) {
        console.error('Error deleting category:', deleteError);
        alert('Error deleting category: ' + deleteError.message);
        return;
      }

      // Refresh the categories data
      refetch();
      alert('Category deleted successfully!');

    } catch (error) {
      console.error('Error deleting category:', error);
      alert('Error deleting category: ' + (error as Error).message);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Tag className="h-8 w-8 text-gray-400 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Category Management</h2>
          <p className="text-gray-600">{categories.length} categories total</p>
        </div>
        <button
          onClick={() => {
            setSelectedCategory(null);
            setFormData({ name: '', description: '' });
            setShowForm(true);
          }}
          className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Category</span>
        </button>
      </div>

      {/* Categories List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {categories.map(category => (
                <tr key={category.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Tag className="h-5 w-5 text-gray-400 mr-3" />
                      <div className="text-sm font-medium text-gray-900">{category.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500">{category.description || 'No description'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      category.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {category.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEdit(category)}
                        className="text-teal-600 hover:text-teal-900"
                        title="Edit category"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleToggleActive(category)}
                        className={`${category.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                        title={category.isActive ? 'Deactivate category' : 'Activate category'}
                      >
                        <AlertTriangle className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteCategory(category)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete category"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-gray-900">
                  {selectedCategory ? 'Edit Category' : 'Add New Category'}
                </h3>
                <button 
                  onClick={() => setShowForm(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <Plus className="h-6 w-6 rotate-45" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category Name</label>
                  <input
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="e.g., Office Supplies"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea
                    rows={3}
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Brief description of the category"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Color</label>
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      value={formData.color}
                      onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                      className="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer"
                    />
                    <input
                      type="text"
                      value={formData.color}
                      onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                      placeholder="#3B82F6"
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-4 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-2 bg-gradient-to-r from-teal-600 to-teal-700 text-white rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200"
                  >
                    {selectedCategory ? 'Update Category' : 'Create Category'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoryManagement;
