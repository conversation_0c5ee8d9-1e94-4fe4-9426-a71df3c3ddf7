
import { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Package, AlertTriangle, Search, Filter, Eye, X } from 'lucide-react';
import { Product, Category } from '../../types/inventory';
import { getCategories } from '../../services/inventoryService';
import { useSyncedProducts, useSyncedCategories, useProductOperations } from '../../hooks/useSyncedData';
import { formatPrice, getStockStatusColor, getStockStatusText } from '../../utils/inventoryUtils';
import ProductForm from './ProductForm';
import ImageService from '../../services/imageService';

const ProductManagement = () => {
  // Use synchronized data hooks
  const { data: products, loading, error, refetch } = useSyncedProducts();
  const { data: categories } = useSyncedCategories();
  const { createProduct, updateProduct, deleteProduct, loading: operationLoading, error: operationError } = useProductOperations();

  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [showLowStock, setShowLowStock] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [showProductInfo, setShowProductInfo] = useState(false);

  // Advanced filters
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);
  const [stockStatus, setStockStatus] = useState('all'); // all, in-stock, low-stock, out-of-stock
  const [sortBy, setSortBy] = useState('name'); // name, price, stock, created, updated
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [activeStatus, setActiveStatus] = useState('all'); // all, active, inactive
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({ start: '', end: '' });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const [paginatedProducts, setPaginatedProducts] = useState<Product[]>([]);
  const [totalPages, setTotalPages] = useState(0);
  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    console.log('ProductManagement: Products data updated:', products);
    filterProducts();
  }, [products, searchTerm, selectedCategory, showLowStock, priceRange, stockStatus, sortBy, sortOrder, selectedBrand, activeStatus, dateRange]);

  useEffect(() => {
    paginateProducts();
  }, [filteredProducts, currentPage, itemsPerPage]);

  const filterProducts = () => {
    if (!products || !Array.isArray(products)) {
      setFilteredProducts([]);
      return;
    }

    let filtered = [...products];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Brand filter
    if (selectedBrand) {
      filtered = filtered.filter(product => product.brand === selectedBrand);
    }

    // Price range filter
    filtered = filtered.filter(product =>
      product.price >= priceRange[0] && product.price <= priceRange[1]
    );

    // Stock status filter
    if (stockStatus !== 'all') {
      filtered = filtered.filter(product => {
        switch (stockStatus) {
          case 'in-stock':
            return product.stock > product.minStock;
          case 'low-stock':
            return product.stock <= product.minStock && product.stock > 0;
          case 'out-of-stock':
            return product.stock === 0;
          default:
            return true;
        }
      });
    }

    // Legacy low stock filter (for backward compatibility)
    if (showLowStock) {
      filtered = filtered.filter(product => product.stock <= product.minStock);
    }

    // Active/Inactive status filter
    if (activeStatus !== 'all') {
      filtered = filtered.filter(product => {
        switch (activeStatus) {
          case 'active':
            return product.isActive !== false;
          case 'inactive':
            return product.isActive === false;
          default:
            return true;
        }
      });
    }

    // Date range filter
    if (dateRange.start || dateRange.end) {
      filtered = filtered.filter(product => {
        const productDate = new Date(product.createdAt);
        const startDate = dateRange.start ? new Date(dateRange.start) : null;
        const endDate = dateRange.end ? new Date(dateRange.end) : null;

        if (startDate && endDate) {
          return productDate >= startDate && productDate <= endDate;
        } else if (startDate) {
          return productDate >= startDate;
        } else if (endDate) {
          return productDate <= endDate;
        }
        return true;
      });
    }

    // Sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        case 'stock':
          aValue = a.stock;
          bValue = b.stock;
          break;
        case 'created':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        case 'updated':
          aValue = new Date(a.updatedAt);
          bValue = new Date(b.updatedAt);
          break;
        case 'brand':
          aValue = a.brand.toLowerCase();
          bValue = b.brand.toLowerCase();
          break;
        default:
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredProducts(filtered);
    setTotalItems(filtered.length);
    setTotalPages(Math.ceil(filtered.length / itemsPerPage));

    // Reset to first page if current page is beyond available pages
    if (currentPage > Math.ceil(filtered.length / itemsPerPage) && filtered.length > 0) {
      setCurrentPage(1);
    }
  };

  const paginateProducts = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setPaginatedProducts(filteredProducts.slice(startIndex, endIndex));
  };

  // Utility functions
  const getUniqueValues = (key: keyof Product): string[] => {
    if (!products) return [];
    const values = products.map(product => product[key] as string).filter(Boolean);
    return [...new Set(values)].sort();
  };

  const getActiveBrands = () => getUniqueValues('brand');

  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setSelectedBrand('');
    setPriceRange([0, 10000]);
    setStockStatus('all');
    setActiveStatus('all');
    setDateRange({ start: '', end: '' });
    setShowLowStock(false);
    setSortBy('name');
    setSortOrder('asc');
    setCurrentPage(1);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (searchTerm) count++;
    if (selectedCategory) count++;
    if (selectedBrand) count++;
    if (priceRange[0] > 0 || priceRange[1] < 10000) count++;
    if (stockStatus !== 'all') count++;
    if (activeStatus !== 'all') count++;
    if (dateRange.start || dateRange.end) count++;
    if (showLowStock) count++;
    return count;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    const halfVisible = Math.floor(maxVisiblePages / 2);

    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  };

  const handleSaveProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'> & { branchId: string }) => {
    try {
      if (selectedProduct) {
        console.log('Updating product:', selectedProduct.id, productData);

        // Handle category properly - check if it's a UUID or a name
        let categoryId = productData.category;
        const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(productData.category);

        if (!isUUID) {
          // It's a predefined category name, try to find the corresponding category ID
          const category = categories.find(cat => cat.name === productData.category);
          if (category) {
            categoryId = category.id;
          } else {
            // If no matching category found, we need to create one or handle as string
            console.warn('Category not found in database:', productData.category);
            categoryId = productData.category; // Keep as string for now
          }
        }

        console.log('ProductManagement: Saving product with thumbnailImages:', productData.thumbnailImages);

        const result = await updateProduct(selectedProduct.id, {
          title: productData.title,
          description: productData.description,
          brand: productData.brand,
          price: productData.price,
          stock: productData.stock,
          min_stock: productData.minStock,
          category_id: categoryId,
          featured_image: productData.featuredImage,
          thumbnail_images: productData.thumbnailImages, // FIXED: Include gallery images
          sku: productData.sku,
          branchId: productData.branchId // Include branch ID for inventory sync
        });

        if (result.success) {
          setShowForm(false);
          setSelectedProduct(null);
          // Trigger a refetch to ensure UI is updated with latest data
          refetch();
          alert('Product updated successfully!');
        } else {
          console.error('Update failed:', result.error);
          alert(result.error || 'Failed to update product');
        }
      } else {
        console.log('Creating new product:', productData);

        // Handle category properly for new products too
        let categoryId = productData.category;
        const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(productData.category);

        if (!isUUID) {
          // It's a predefined category name, try to find the corresponding category ID
          const category = categories.find(cat => cat.name === productData.category);
          if (category) {
            categoryId = category.id;
          } else {
            console.warn('Category not found in database:', productData.category);
            categoryId = productData.category; // Keep as string for now
          }
        }

        console.log('ProductManagement: Creating product with thumbnailImages:', productData.thumbnailImages);

        const result = await createProduct({
          title: productData.title,
          description: productData.description,
          brand: productData.brand,
          price: productData.price,
          stock: productData.stock,
          min_stock: productData.minStock,
          category_id: categoryId,
          featured_image: productData.featuredImage,
          thumbnail_images: productData.thumbnailImages, // FIXED: Include gallery images
          sku: productData.sku,
          branchId: productData.branchId // Include branch ID for inventory creation
        });

        if (result.success) {
          setShowForm(false);
          setSelectedProduct(null);
          // Trigger a refetch to ensure UI is updated with latest data
          refetch();
          alert('Product created successfully!');
        } else {
          console.error('Create failed:', result.error);
          alert(result.error || 'Failed to create product');
        }
      }
    } catch (error) {
      console.error('Error saving product:', error);
      alert('Error saving product: ' + (error as Error).message);
    }
  };

  const handleDeleteProduct = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      try {
        console.log('Deleting product:', id);
        const result = await deleteProduct(id);

        if (result.success) {
          // Trigger a refetch to ensure UI is updated with latest data
          refetch();
          alert('Product deleted successfully!');
        } else {
          console.error('Delete failed:', result.error);
          alert(result.error || 'Failed to delete product');
        }
      } catch (error) {
        console.error('Error deleting product:', error);
        alert('Error deleting product: ' + (error as Error).message);
      }
    }
  };

  const lowStockCount = products.filter(p => p.stock <= p.minStock).length;

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Package className="h-8 w-8 text-gray-400 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Product Management</h2>
          <p className="text-gray-600">{products.length} products total</p>
        </div>
        <button
          onClick={() => {
            setSelectedProduct(null);
            setShowForm(true);
          }}
          className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Add Product</span>
        </button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Products</p>
              <p className="text-xl font-bold text-gray-900">{products.length}</p>
            </div>
            <Package className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Low Stock Alerts</p>
              <p className="text-xl font-bold text-red-600">{lowStockCount}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Products</p>
              <p className="text-xl font-bold text-green-600">{products.filter(p => p.isActive).length}</p>
            </div>
            <Package className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Categories</p>
              <p className="text-xl font-bold text-purple-600">{categories.length}</p>
            </div>
            <Filter className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Enhanced Filters */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Filters & Search</h3>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="text-teal-600 hover:text-teal-700 flex items-center space-x-1"
            >
              <Filter className="h-4 w-4" />
              <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>
            </button>
          </div>
        </div>

        <div className="p-4">
          {/* Basic Search and Sort */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              />
            </div>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="name">Sort by Name</option>
              <option value="price">Sort by Price</option>
              <option value="stock">Sort by Stock</option>
              <option value="brand">Sort by Brand</option>
              <option value="created">Sort by Created Date</option>
              <option value="updated">Sort by Updated Date</option>
            </select>

            <select
              value={sortOrder}
              onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="border-t border-gray-200 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.name}>{category.name}</option>
                  ))}
                </select>

                <select
                  value={selectedBrand}
                  onChange={(e) => setSelectedBrand(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="">All Brands</option>
                  {getActiveBrands().map(brand => (
                    <option key={brand} value={brand}>{brand}</option>
                  ))}
                </select>

                <select
                  value={stockStatus}
                  onChange={(e) => setStockStatus(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="all">All Stock Status</option>
                  <option value="in-stock">In Stock</option>
                  <option value="low-stock">Low Stock</option>
                  <option value="out-of-stock">Out of Stock</option>
                </select>

                <select
                  value={activeStatus}
                  onChange={(e) => setActiveStatus(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="all">All Products</option>
                  <option value="active">Active Only</option>
                  <option value="inactive">Inactive Only</option>
                </select>
              </div>

              {/* Date Range Filter */}
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Created From
                  </label>
                  <input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Created To
                  </label>
                  <input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Price Range Filter */}
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Range: {priceRange[0]} Dh - {priceRange[1]} Dh
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="number"
                    placeholder="Min"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([Number(e.target.value), priceRange[1]])}
                    className="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                  <span className="text-gray-500">to</span>
                  <input
                    type="number"
                    placeholder="Max"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], Number(e.target.value)])}
                    className="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                  <button
                    onClick={() => setPriceRange([0, 10000])}
                    className="text-teal-600 hover:text-teal-700 text-sm"
                  >
                    Reset
                  </button>
                </div>
              </div>

              {/* Filter Actions */}
              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">
                    {getActiveFilterCount()} active filter{getActiveFilterCount() !== 1 ? 's' : ''}
                  </span>
                  {getActiveFilterCount() > 0 && (
                    <button
                      onClick={clearAllFilters}
                      className="text-teal-600 hover:text-teal-700 text-sm font-medium flex items-center space-x-1"
                    >
                      <X className="h-4 w-4" />
                      <span>Clear All Filters</span>
                    </button>
                  )}
                </div>
                <div className="text-sm text-gray-600">
                  Showing {filteredProducts.length} of {products.length} products
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Products Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedProducts.map(product => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <img
                          src={ImageService.getBestImageUrl(product, 40)}
                          alt={product.title}
                          className="h-10 w-10 rounded-lg object-cover border border-gray-200"
                          onError={(e) => {
                            e.currentTarget.src = ImageService.generateFallbackImage(product.title, 40);
                          }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {product.title}
                        </div>
                        <div className="text-sm text-gray-500 truncate">
                          {product.brand || 'No brand'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.sku}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.category}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {(product as any).branchName || 'Multiple Branches'}
                    </div>
                    <div className="text-xs text-gray-500">
                      {(product as any).branchCode || 'Various locations'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatPrice(product.price)}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{product.stock} units</div>
                    <div className="text-xs text-gray-500">Min: {product.minStock}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockStatusColor(product.stock, product.minStock)}`}>
                      {getStockStatusText(product.stock, product.minStock)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowProductInfo(true);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        title="View Product Information"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedProduct(product);
                          setShowForm(true);
                        }}
                        className="text-teal-600 hover:text-teal-900"
                        title="Edit Product"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteProduct(product.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete Product"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-8">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || selectedCategory || showLowStock
                ? 'Try adjusting your filters'
                : 'Get started by adding a new product'
              }
            </p>
          </div>
        )}

        {/* Pagination Controls */}
        {filteredProducts.length > 0 && totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              {/* Pagination Info */}
              <div className="text-sm text-gray-700">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} products
              </div>

              {/* Pagination Controls */}
              <div className="flex items-center space-x-2">
                {/* First Page */}
                <button
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  First
                </button>

                {/* Previous Page */}
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {/* Page Numbers */}
                <div className="flex items-center space-x-1">
                  {getPageNumbers().map(page => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        page === currentPage
                          ? 'bg-teal-600 text-white'
                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </div>

                {/* Next Page */}
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>

                {/* Last Page */}
                <button
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Last
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Product Form Modal */}
      {showForm && (
        <ProductForm
          product={selectedProduct}
          categories={categories}
          onSave={handleSaveProduct}
          onCancel={() => {
            setShowForm(false);
            setSelectedProduct(null);
          }}
        />
      )}

      {/* Product Information Modal */}
      {showProductInfo && selectedProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Product Information</h2>
                <button
                  onClick={() => {
                    setShowProductInfo(false);
                    setSelectedProduct(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Product Images */}
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Featured Image</h3>
                    <img
                      src={ImageService.getBestImageUrl(selectedProduct, 400)}
                      alt={selectedProduct.title}
                      className="w-full h-64 object-cover rounded-lg border border-gray-200"
                      onError={(e) => {
                        e.currentTarget.src = ImageService.generateFallbackImage(selectedProduct.title, 400);
                      }}
                    />
                  </div>

                  {/* Gallery Images */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Gallery Images</h3>
                    {(() => {
                      console.log('ProductInfo: thumbnailImages data:', selectedProduct.thumbnailImages);
                      const validImages = selectedProduct.thumbnailImages?.filter(img =>
                        img &&
                        img !== '/placeholder.svg' &&
                        img.trim() !== '' &&
                        !img.includes('placeholder')
                      ) || [];
                      console.log('ProductInfo: valid gallery images:', validImages);

                      if (validImages.length === 0) {
                        return (
                          <div className="text-center py-8 text-gray-500">
                            <Package className="mx-auto h-12 w-12 text-gray-300 mb-2" />
                            <p>No gallery images uploaded</p>
                          </div>
                        );
                      }

                      return (
                        <div className="grid grid-cols-2 gap-2">
                          {validImages.map((image, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={image}
                                alt={`Gallery image ${index + 1}`}
                                className="w-full h-32 object-cover rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
                                onError={(e) => {
                                  console.error('ProductInfo: Gallery image failed to load:', image);
                                  e.currentTarget.src = ImageService.generateFallbackImage(selectedProduct.title, 128);
                                }}
                                onLoad={() => {
                                  console.log('ProductInfo: Gallery image loaded successfully:', image);
                                }}
                              />
                              {/* Image overlay with index */}
                              <div className="absolute top-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 py-0.5 rounded">
                                {index + 1}
                              </div>
                            </div>
                          ))}
                        </div>
                      );
                    })()}
                  </div>
                </div>

                {/* Product Details */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Basic Information</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Title</label>
                        <p className="text-gray-900">{selectedProduct.title}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">SKU</label>
                        <p className="text-gray-900">{selectedProduct.sku}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Brand</label>
                        <p className="text-gray-900">{selectedProduct.brand || 'No brand'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Category</label>
                        <p className="text-gray-900">{selectedProduct.category}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Description</label>
                        <p className="text-gray-900">{selectedProduct.description || 'No description'}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Pricing & Stock</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Price</label>
                        <p className="text-gray-900 font-semibold">{formatPrice(selectedProduct.price)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Reseller Price</label>
                        <p className="text-gray-900 font-semibold">{formatPrice(selectedProduct.resellerPrice)}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Stock</label>
                        <p className="text-gray-900">{selectedProduct.stock} units</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Min Stock</label>
                        <p className="text-gray-900">{selectedProduct.minStock} units</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Status & Attributes</h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <label className="text-sm font-medium text-gray-500">Stock Status:</label>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockStatusColor(selectedProduct.stock, selectedProduct.minStock)}`}>
                          {getStockStatusText(selectedProduct.stock, selectedProduct.minStock)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <label className="text-sm font-medium text-gray-500">Active:</label>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${selectedProduct.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {selectedProduct.isActive ? 'Yes' : 'No'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <label className="text-sm font-medium text-gray-500">New Product:</label>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${selectedProduct.isNew ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                          {selectedProduct.isNew ? 'Yes' : 'No'}
                        </span>
                      </div>
                      {selectedProduct.rating && (
                        <div>
                          <label className="text-sm font-medium text-gray-500">Rating</label>
                          <p className="text-gray-900">{selectedProduct.rating}/5</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {selectedProduct.tags && selectedProduct.tags.length > 0 && (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-3">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedProduct.tags.map((tag, index) => (
                          <span key={index} className="inline-flex px-2 py-1 text-xs font-medium bg-teal-100 text-teal-800 rounded-full">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Timestamps</h3>
                    <div className="space-y-2">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Created</label>
                        <p className="text-gray-900">{new Date(selectedProduct.createdAt).toLocaleString()}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-500">Last Updated</label>
                        <p className="text-gray-900">{new Date(selectedProduct.updatedAt).toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 mt-6">
                <button
                  onClick={() => {
                    setShowProductInfo(false);
                    setShowForm(true);
                  }}
                  className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
                >
                  Edit Product
                </button>
                <button
                  onClick={() => {
                    setShowProductInfo(false);
                    setSelectedProduct(null);
                  }}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductManagement;
