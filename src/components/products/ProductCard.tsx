
import { useState } from 'react';
import { Eye, Star, ShoppingCart, Heart, Scale } from 'lucide-react';
import { Product } from '../../types/inventory';
import { formatPrice } from '../../utils/inventoryUtils';
import ProductDetailsModal from './ProductDetailsModal';
import { useComparison } from '../../contexts/ComparisonContext';

interface ProductCardProps {
  product: Product;
  userType?: string;
  onAddToCart?: (product: Product) => void;
}

const ProductCard = ({ product, userType = 'client', onAddToCart }: ProductCardProps) => {
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const { addToComparison, removeFromComparison, isInComparison, comparisonCount, maxComparisons } = useComparison();

  const handleAddToCart = () => {
    if (onAddToCart) {
      onAddToCart(product);
    }
  };

  const handleComparisonToggle = () => {
    if (isInComparison(product.id)) {
      removeFromComparison(product.id);
    } else {
      if (comparisonCount < maxComparisons) {
        addToComparison(product.id);
      }
    }
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
        <div className="relative">
          <img
            src={product.featuredImage || product.image}
            alt={product.title}
            className="w-full h-48 object-cover"
          />
          <button
            onClick={() => setShowDetailsModal(true)}
            className="absolute top-3 right-3 bg-white bg-opacity-90 hover:bg-opacity-100 text-gray-700 p-2 rounded-full shadow-md transition-all"
            title="View Details"
          >
            <Eye className="h-4 w-4" />
          </button>
        </div>
        
        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{product.title}</h3>
          <p className="text-sm text-gray-600 mb-2">{product.brand}</p>
          <p className="text-sm text-gray-500 mb-3 line-clamp-2">{product.description}</p>
          
          <div className="flex justify-between items-center mb-3">
            <span className="text-xl font-bold text-gray-900">{formatPrice(product.price)}</span>
            {product.rating && (
              <div className="flex items-center">
                <span className="text-yellow-400">★</span>
                <span className="text-sm text-gray-600 ml-1">{product.rating}</span>
              </div>
            )}
          </div>
          
          <div className="flex justify-between items-center mb-3">
            <span className="text-sm text-gray-500">Stock: {product.stock}</span>
            <span className={`px-2 py-1 text-xs rounded-full ${
              product.stock > product.minStock 
                ? 'bg-green-100 text-green-800' 
                : product.stock > 0 
                  ? 'bg-yellow-100 text-yellow-800' 
                  : 'bg-red-100 text-red-800'
            }`}>
              {product.stock > product.minStock 
                ? 'In Stock' 
                : product.stock > 0 
                  ? 'Low Stock' 
                  : 'Out of Stock'
              }
            </span>
          </div>
          
          <div className="space-y-2">
            <div className="flex space-x-2">
              <button
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                disabled={product.stock === 0}
                onClick={handleAddToCart}
              >
                <ShoppingCart className="h-4 w-4 inline mr-2" />
                {product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
              </button>

              <button
                onClick={() => setShowDetailsModal(true)}
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                title="View Details"
              >
                <Eye className="h-4 w-4" />
              </button>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={handleComparisonToggle}
                className={`flex-1 px-3 py-2 rounded-lg border transition-colors ${
                  isInComparison(product.id)
                    ? 'bg-orange-50 border-orange-200 text-orange-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
                disabled={!isInComparison(product.id) && comparisonCount >= maxComparisons}
                title={isInComparison(product.id) ? 'Remove from comparison' : 'Add to comparison'}
              >
                <Scale className="h-4 w-4 inline mr-2" />
                {isInComparison(product.id) ? 'Remove' : 'Compare'}
              </button>

              <button
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                title="Add to Wishlist"
              >
                <Heart className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {showDetailsModal && (
        <ProductDetailsModal
          productId={product.id}
          isOpen={showDetailsModal}
          onClose={() => setShowDetailsModal(false)}
          userType={userType as 'client' | 'reseller'}
          onAddToCart={onAddToCart}
        />
      )}
    </>
  );
};

export default ProductCard;
