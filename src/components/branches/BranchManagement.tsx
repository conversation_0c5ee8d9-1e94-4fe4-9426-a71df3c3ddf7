
import { useState, useEffect } from 'react';
import { Building2, Package, ArrowRightLeft, TrendingUp, MapPin, Clock, Phone, Mail, Plus, Edit, Trash2, Eye, Search, Filter, Grid, List, ChevronLeft, ChevronRight, AlertTriangle, XCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { Branch, BranchInventory, StockTransfer, BranchPerformance } from '../../types/branch';
import {
  getBranches,
  getStockTransfers,
  createBranch,
  updateBranch,
  deleteBranch
} from '../../services/branchService';
import { getBranchInventory } from '../../services/branchInventoryService';
import { realTimeService } from '../../services/realTimeService';
import { liveDataService } from '../../services/liveDataService';
import { supabase } from '../../integrations/supabase/client';
import StockTransferModal from './StockTransferModal';
import StockTransferDetailsModal from './StockTransferDetailsModal';
import StockTransferEditModal from './StockTransferEditModal';
import BranchPerformanceChart from './BranchPerformanceChart';
import BranchFormModal from './BranchFormModal';
import BranchDetailsModal from './BranchDetailsModal';

interface BranchManagementProps {
  currentUserId?: string;
}

const BranchManagement = ({ currentUserId = 'USR-001' }: BranchManagementProps) => {
  const [activeTab, setActiveTab] = useState('branches');
  const [branches, setBranches] = useState<Branch[]>([]);
  const [filteredBranches, setFilteredBranches] = useState<Branch[]>([]);
  const [selectedBranch, setSelectedBranch] = useState<string>('');
  const [branchInventory, setBranchInventory] = useState<BranchInventory[]>([]);
  const [stockTransfers, setStockTransfers] = useState<StockTransfer[]>([]);
  const [branchPerformance, setBranchPerformance] = useState<BranchPerformance[]>([]);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showTransferDetailsModal, setShowTransferDetailsModal] = useState(false);
  const [showTransferEditModal, setShowTransferEditModal] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState<StockTransfer | null>(null);
  const [showBranchModal, setShowBranchModal] = useState(false);
  const [showBranchDetailsModal, setShowBranchDetailsModal] = useState(false);
  const [editingBranch, setEditingBranch] = useState<Branch | null>(null);
  const [viewingBranch, setViewingBranch] = useState<Branch | null>(null);
  const [loading, setLoading] = useState(true);

  // Real-time statistics state
  const [branchStats, setBranchStats] = useState({
    totalBranches: 0,
    activeBranches: 0,
    inactiveBranches: 0,
    pendingTransfers: 0,
    totalInventoryItems: 0,
    averagePerformance: 0
  });

  // Branch analytics state
  const [branchAnalytics, setBranchAnalytics] = useState({
    totalProducts: 0,
    lowStockItems: 0,
    outOfStockItems: 0,
    totalItems: 0,
    branchRevenue: 0,
    totalOrders: 0,
    customerCount: 0,
    averageOrderValue: 0,
    inventoryTurnover: 0
  });

  // Sales comparison state
  const [salesComparison, setSalesComparison] = useState<any[]>([]);
  const [statsLoading, setStatsLoading] = useState(false);

  // Transfer analytics state
  const [transferAnalytics, setTransferAnalytics] = useState({
    totalTransfers: 0,
    pendingTransfers: 0,
    completedTransfers: 0,
    rejectedTransfers: 0,
    totalItemsTransferred: 0,
    averageTransferTime: 0,
    recentTransfers: []
  });

  // Search, Filter & Display Options
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'city' | 'manager' | 'created'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadData();

    // Set up real-time subscriptions
    const unsubscribeBranchCreated = realTimeService.subscribe('branch-created', handleBranchCreated);
    const unsubscribeBranchUpdated = realTimeService.subscribe('branch-updated', handleBranchUpdated);
    const unsubscribeBranchDeleted = realTimeService.subscribe('branch-deleted', handleBranchDeleted);
    const unsubscribeTransferCreated = realTimeService.subscribe('transfer-created', handleTransferCreated);
    const unsubscribeTransferUpdated = realTimeService.subscribe('transfer-updated', handleTransferUpdated);
    const unsubscribeInventoryChanged = realTimeService.subscribe('inventory-changed', handleInventoryChanged);
    const unsubscribeStockUpdated = realTimeService.subscribe('stock-updated', handleInventoryChanged);

    // Branch inventory real-time subscriptions
    const unsubscribeBranchInventoryUpdated = realTimeService.subscribe('branch-inventory-updated', handleBranchInventoryUpdated);
    const unsubscribeBranchInventoryCreated = realTimeService.subscribe('branch-inventory-created', handleBranchInventoryCreated);
    const unsubscribeBranchInventorySynced = realTimeService.subscribe('branch-inventory-synced', handleBranchInventorySynced);

    // Set up Supabase real-time subscriptions for database changes
    const branchesSubscription = supabase
      .channel('branches-changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'branches' }, (payload) => {
        console.log('BranchManagement: Branches table changed:', payload);
        loadData(); // Refresh branch data
      })
      .subscribe();

    const ordersSubscription = supabase
      .channel('orders-changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'orders' }, (payload) => {
        console.log('BranchManagement: Orders table changed:', payload);
        // Refresh analytics and performance data
        if (selectedBranch) {
          loadBranchAnalytics(selectedBranch);
        }
        loadSalesComparison();
        loadData(); // Refresh performance data
      })
      .subscribe();

    const transfersSubscription = supabase
      .channel('transfers-changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'stock_transfers' }, (payload) => {
        console.log('BranchManagement: Stock transfers table changed:', payload);
        loadStockTransfers();
        loadTransferAnalytics();
      })
      .subscribe();

    const inventorySubscription = supabase
      .channel('inventory-changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'branch_inventory' }, (payload) => {
        console.log('BranchManagement: Branch inventory table changed:', payload);
        if (selectedBranch) {
          loadBranchData(selectedBranch);
          loadBranchAnalytics(selectedBranch);
        }
      })
      .subscribe();

    return () => {
      unsubscribeBranchCreated();
      unsubscribeBranchUpdated();
      unsubscribeBranchDeleted();
      unsubscribeTransferCreated();
      unsubscribeTransferUpdated();
      unsubscribeInventoryChanged();
      unsubscribeStockUpdated();
      unsubscribeBranchInventoryUpdated();
      unsubscribeBranchInventoryCreated();
      unsubscribeBranchInventorySynced();

      // Cleanup Supabase subscriptions
      branchesSubscription.unsubscribe();
      ordersSubscription.unsubscribe();
      transfersSubscription.unsubscribe();
      inventorySubscription.unsubscribe();
    };
  }, []);

  useEffect(() => {
    if (selectedBranch) {
      loadBranchData(selectedBranch);
      loadBranchAnalytics(selectedBranch);
    }
  }, [selectedBranch]);

  useEffect(() => {
    filterAndSortBranches();
  }, [branches, searchTerm, statusFilter, sortBy, sortOrder, currentPage, pageSize]);

  const loadData = async () => {
    try {
      console.log('BranchManagement: Loading branch data...');
      const [branchesData, transfersData, performanceData] = await Promise.all([
        getBranches(false), // Fetch all branches (active and inactive)
        getStockTransfers(),
        liveDataService.getBranchPerformance('monthly')
      ]);

      setBranches(branchesData);
      setStockTransfers(transfersData);
      setBranchPerformance(performanceData);

      if (branchesData.length > 0) {
        setSelectedBranch(branchesData[0].id);
      }

      // Load real-time statistics
      await loadBranchStatistics();
    } catch (error) {
      console.error('Error loading branch data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadBranchStatistics = async () => {
    try {
      setStatsLoading(true);
      console.log('BranchManagement: Loading branch statistics...');

      const stats = await liveDataService.getBranchStatistics();
      setBranchStats(stats);

      console.log('BranchManagement: Branch statistics loaded:', stats);
    } catch (error) {
      console.error('Error loading branch statistics:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  const loadBranchAnalytics = async (branchId: string) => {
    if (!branchId) return;

    try {
      console.log('BranchManagement: Loading analytics for branch:', branchId);

      const analytics = await liveDataService.getBranchAnalytics(branchId);
      setBranchAnalytics(analytics);

      console.log('BranchManagement: Branch analytics loaded:', analytics);
    } catch (error) {
      console.error('Error loading branch analytics:', error);
    }
  };

  const loadStockTransfers = async () => {
    try {
      console.log('BranchManagement: Loading stock transfers...');
      const transfersData = await getStockTransfers();
      setStockTransfers(transfersData);
      console.log('BranchManagement: Stock transfers loaded:', transfersData);
    } catch (error) {
      console.error('Error loading stock transfers:', error);
    }
  };

  const loadSalesComparison = async () => {
    try {
      console.log('BranchManagement: Loading sales comparison...');

      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);

      const comparison = await liveDataService.getBranchSalesComparison({
        start: startDate.toISOString(),
        end: endDate.toISOString()
      });

      setSalesComparison(comparison);
      console.log('BranchManagement: Sales comparison loaded:', comparison);
    } catch (error) {
      console.error('Error loading sales comparison:', error);
    }
  };

  const loadTransferAnalytics = async () => {
    try {
      console.log('BranchManagement: Loading transfer analytics...');

      const analytics = await liveDataService.getStockTransferAnalytics();
      setTransferAnalytics(analytics);

      console.log('BranchManagement: Transfer analytics loaded:', analytics);
    } catch (error) {
      console.error('Error loading transfer analytics:', error);
    }
  };

  const loadBranchData = async (branchId: string) => {
    try {
      const inventoryData = await getBranchInventory(branchId);
      setBranchInventory(inventoryData);
    } catch (error) {
      console.error('Error loading branch inventory:', error);
    }
  };

  const refreshAllData = async () => {
    console.log('BranchManagement: Refreshing all data...');
    setLoading(true);
    await loadData();
    await loadBranchStatistics();
    await loadSalesComparison();
    await loadTransferAnalytics();
    if (selectedBranch) {
      await loadBranchData(selectedBranch);
      await loadBranchAnalytics(selectedBranch);
    }
  };

  // Real-time event handlers
  const handleBranchCreated = (event: any) => {
    console.log('BranchManagement: Branch created event received:', event);
    if (event && event.branch && event.branch.id) {
      setBranches(prev => [event.branch, ...prev]);
    } else {
      console.error('BranchManagement: Invalid branch created event data:', event);
      // Refresh data to ensure consistency
      loadData();
    }
  };

  const handleBranchUpdated = (event: any) => {
    console.log('BranchManagement: Branch updated event received:', event);
    if (event && event.branchId && event.newData) {
      setBranches(prev => prev.map(branch =>
        branch.id === event.branchId ? event.newData : branch
      ));
    } else {
      console.error('BranchManagement: Invalid branch updated event data:', event);
      // Refresh data to ensure consistency
      loadData();
    }
  };

  const handleBranchDeleted = (event: any) => {
    console.log('BranchManagement: Branch deleted event received:', event);
    if (event && event.branchId) {
      setBranches(prev => prev.filter(branch => branch.id !== event.branchId));
      if (selectedBranch === event.branchId) {
        setSelectedBranch('');
      }
    } else {
      console.error('BranchManagement: Invalid branch deleted event data:', event);
      // Refresh data to ensure consistency
      loadData();
    }
  };

  const handleTransferCreated = (event: any) => {
    console.log('BranchManagement: Transfer created event received:', event);
    setStockTransfers(prev => [event.transfer, ...prev]);
  };

  // Branch inventory real-time event handlers
  const handleBranchInventoryUpdated = (event: any) => {
    console.log('BranchManagement: Branch inventory updated event received:', event);
    if (event && event.branchId === selectedBranch) {
      // Refresh branch inventory for the selected branch
      loadBranchData(selectedBranch);
    }
  };

  const handleBranchInventoryCreated = (event: any) => {
    console.log('BranchManagement: Branch inventory created event received:', event);
    if (event && event.branchId === selectedBranch) {
      // Refresh branch inventory for the selected branch
      loadBranchData(selectedBranch);
    }
  };

  const handleBranchInventorySynced = (event: any) => {
    console.log('BranchManagement: Branch inventory synced event received:', event);
    if (event && event.branchId === selectedBranch) {
      // Refresh branch inventory for the selected branch
      loadBranchData(selectedBranch);
    }
  };

  const handleTransferUpdated = (event: any) => {
    console.log('BranchManagement: Transfer updated event received:', event);
    setStockTransfers(prev => prev.map(transfer =>
      transfer.id === event.transferId ? event.transfer : transfer
    ));
  };

  const handleInventoryChanged = (event: any) => {
    console.log('BranchManagement: Inventory changed event received:', event);
    // Reload branch inventory if we're viewing a specific branch
    if (selectedBranch && event.branchId === selectedBranch) {
      loadBranchData(selectedBranch);
    }
  };

  // Filter and sort branches
  const filterAndSortBranches = () => {
    let filtered = [...branches];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(branch =>
        branch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.address.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.managerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.phone.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(branch =>
        statusFilter === 'active' ? branch.isActive : !branch.isActive
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'city':
          aValue = a.address.city.toLowerCase();
          bValue = b.address.city.toLowerCase();
          break;
        case 'manager':
          aValue = a.managerName.toLowerCase();
          bValue = b.managerName.toLowerCase();
          break;
        case 'created':
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    // Calculate pagination
    const total = filtered.length;
    setTotalPages(Math.ceil(total / pageSize));

    // Apply pagination
    const startIndex = (currentPage - 1) * pageSize;
    const paginatedBranches = filtered.slice(startIndex, startIndex + pageSize);

    setFilteredBranches(paginatedBranches);
  };

  const handleCreateBranch = async (branchData: Omit<Branch, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      console.log('BranchManagement: Creating branch:', branchData);
      const newBranch = await createBranch(branchData, currentUserId);

      if (newBranch) {
        // Only emit statistics update event (branch-created is already emitted by branchService)
        realTimeService.emit('statistics-updated', {
          type: 'branch-created',
          branchId: newBranch.id,
          userId: currentUserId
        });

        setShowBranchModal(false);
        setEditingBranch(null);
        alert('Branch created successfully!');
      } else {
        throw new Error('Failed to create branch - no data returned');
      }
    } catch (error) {
      console.error('Error creating branch:', error);
      alert('Error creating branch: ' + (error as Error).message);
    }
  };

  const handleUpdateBranch = async (branchData: Omit<Branch, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!editingBranch) return;
    try {
      console.log('BranchManagement: Updating branch:', editingBranch.id, branchData);
      const updatedBranch = await updateBranch(editingBranch.id, branchData, currentUserId);

      if (updatedBranch) {
        // Only emit statistics update event (branch-updated is already emitted by branchService)
        realTimeService.emit('statistics-updated', {
          type: 'branch-updated',
          branchId: editingBranch.id,
          userId: currentUserId
        });

        setShowBranchModal(false);
        setEditingBranch(null);
        alert('Branch updated successfully!');
      } else {
        throw new Error('Failed to update branch - no data returned');
      }
    } catch (error) {
      console.error('Error updating branch:', error);
      alert('Error updating branch: ' + (error as Error).message);
    }
  };

  const handleDeleteBranch = async (branchId: string) => {
    if (window.confirm('Are you sure you want to delete this branch? This action cannot be undone.')) {
      try {
        console.log('BranchManagement: Deleting branch:', branchId);
        const success = await deleteBranch(branchId, currentUserId);

        if (success) {
          // Only emit statistics update event (branch-deleted is already emitted by branchService)
          realTimeService.emit('statistics-updated', {
            type: 'branch-deleted',
            branchId,
            userId: currentUserId
          });

          if (selectedBranch === branchId) {
            setSelectedBranch('');
          }

          alert('Branch deleted successfully!');
        } else {
          throw new Error('Failed to delete branch');
        }
      } catch (error) {
        console.error('Error deleting branch:', error);
        alert('Error deleting branch: ' + (error as Error).message);
      }
    }
  };

  const openEditModal = (branch: Branch) => {
    setEditingBranch(branch);
    setShowBranchModal(true);
  };

  const openAddModal = () => {
    setEditingBranch(null);
    setShowBranchModal(true);
  };

  const openDetailsModal = (branch: Branch) => {
    setViewingBranch(branch);
    setShowBranchDetailsModal(true);
  };

  const closeDetailsModal = () => {
    setViewingBranch(null);
    setShowBranchDetailsModal(false);
  };

  // Transfer modal handlers
  const openTransferDetailsModal = (transfer: StockTransfer) => {
    setSelectedTransfer(transfer);
    setShowTransferDetailsModal(true);
  };

  const closeTransferDetailsModal = () => {
    setSelectedTransfer(null);
    setShowTransferDetailsModal(false);
  };

  const openTransferEditModal = (transfer: StockTransfer) => {
    setSelectedTransfer(transfer);
    setShowTransferEditModal(true);
  };

  const closeTransferEditModal = () => {
    setSelectedTransfer(null);
    setShowTransferEditModal(false);
  };

  const handleTransferModalUpdated = () => {
    console.log('BranchManagement: Transfer updated from modal, refreshing data...');
    loadStockTransfers();
    loadTransferAnalytics();
    closeTransferEditModal();
  };

  const handleToggleBranchStatus = async (branchId: string, currentStatus: boolean) => {
    try {
      console.log('BranchManagement: Toggling branch status:', branchId, 'from', currentStatus, 'to', !currentStatus);
      const branch = branches.find(b => b.id === branchId);
      if (!branch) return;

      const updatedBranch = await updateBranch(branchId, { isActive: !currentStatus }, currentUserId);

      if (updatedBranch) {
        // Emit statistics update event
        realTimeService.emit('statistics-updated', {
          type: 'branch-status-changed',
          branchId,
          newStatus: !currentStatus,
          userId: currentUserId
        });

        console.log('Branch status updated successfully');
      } else {
        throw new Error('Failed to update branch status');
      }
    } catch (error) {
      console.error('Error toggling branch status:', error);
      alert('Error updating branch status: ' + (error as Error).message);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Building2 className="h-8 w-8 text-gray-400 animate-spin" />
      </div>
    );
  }

  const selectedBranchData = branches.find(b => b.id === selectedBranch);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Branch Management</h2>
          <div className="flex items-center space-x-4 text-sm">
            <span className="text-gray-600">
              {branches.length} total branches
            </span>
            <span className="flex items-center text-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              {branches.filter(b => b.isActive).length} active
            </span>
            <span className="flex items-center text-red-600">
              <div className="w-2 h-2 bg-red-500 rounded-full mr-1"></div>
              {branches.filter(b => !b.isActive).length} inactive
            </span>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <button
            onClick={openAddModal}
            className="bg-gradient-to-r from-green-600 to-green-700 text-white px-4 py-2 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 flex items-center space-x-2 justify-center"
          >
            <Plus className="h-4 w-4" />
            <span className="hidden sm:inline">Add Branch</span>
          </button>
          <button
            onClick={() => setShowTransferModal(true)}
            className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 flex items-center space-x-2 justify-center"
          >
            <ArrowRightLeft className="h-4 w-4" />
            <span className="hidden sm:inline">New Transfer</span>
          </button>
        </div>
      </div>

      {/* Search, Filter & Display Controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search branches..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </select>

          {/* Sort By */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'name' | 'city' | 'manager' | 'created')}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          >
            <option value="name">Sort by Name</option>
            <option value="city">Sort by City</option>
            <option value="manager">Sort by Manager</option>
            <option value="created">Sort by Created Date</option>
          </select>

          {/* Sort Order & View Mode */}
          <div className="flex space-x-2">
            <select
              value={sortOrder}
              onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>

            <div className="flex border border-gray-300 rounded-lg">
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-teal-600 text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                title="List View"
              >
                <List className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-teal-600 text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                title="Grid View"
              >
                <Grid className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Pagination Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Show:</span>
            <select
              value={pageSize}
              onChange={(e) => {
                setPageSize(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
            </select>
            <span className="text-sm text-gray-600">per page</span>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="p-2 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            <span className="text-sm text-gray-600">
              Page {currentPage} of {totalPages}
            </span>

            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="p-2 border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Branch Info Card - Mobile Optimized */}
      {selectedBranchData && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center space-x-3 mb-4 sm:mb-0">
              <div className="bg-teal-100 p-3 rounded-lg">
                <Building2 className="h-6 w-6 text-teal-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{selectedBranchData.name}</h3>
                <p className="text-sm text-gray-500">Code: {selectedBranchData.code}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => openEditModal(selectedBranchData)}
                className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
                title="Edit Branch"
              >
                <Edit className="h-4 w-4" />
              </button>
              <button
                onClick={() => handleDeleteBranch(selectedBranchData.id)}
                className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                title="Delete Branch"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm mt-4">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">{selectedBranchData.address.city}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">
                {selectedBranchData.operatingHours.open} - {selectedBranchData.operatingHours.close}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">{selectedBranchData.phone}</span>
            </div>
          </div>
        </div>
      )}

      {/* Quick Stats - Mobile Responsive Grid with Real-time Data */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200 relative">
          {statsLoading && (
            <div className="absolute top-2 right-2">
              <RefreshCw className="h-4 w-4 text-gray-400 animate-spin" />
            </div>
          )}
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm text-gray-600">Total Branches</p>
              <p className="text-lg sm:text-xl font-bold text-gray-900">{branchStats.totalBranches}</p>
              <p className="text-xs text-green-600">
                {branchStats.activeBranches} active • {branchStats.inactiveBranches} inactive
              </p>
            </div>
            <Building2 className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200 relative">
          {statsLoading && (
            <div className="absolute top-2 right-2">
              <RefreshCw className="h-4 w-4 text-gray-400 animate-spin" />
            </div>
          )}
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm text-gray-600">Pending Transfers</p>
              <p className="text-lg sm:text-xl font-bold text-orange-600">{branchStats.pendingTransfers}</p>
              <p className="text-xs text-gray-500">
                Stock movements awaiting approval
              </p>
            </div>
            <ArrowRightLeft className="h-6 w-6 sm:h-8 sm:w-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200 relative">
          {statsLoading && (
            <div className="absolute top-2 right-2">
              <RefreshCw className="h-4 w-4 text-gray-400 animate-spin" />
            </div>
          )}
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm text-gray-600">Inventory Items</p>
              <p className="text-lg sm:text-xl font-bold text-green-600">{branchStats.totalInventoryItems}</p>
              <p className="text-xs text-gray-500">
                Across all branches
              </p>
            </div>
            <Package className="h-6 w-6 sm:h-8 sm:w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200 relative">
          {statsLoading && (
            <div className="absolute top-2 right-2">
              <RefreshCw className="h-4 w-4 text-gray-400 animate-spin" />
            </div>
          )}
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs sm:text-sm text-gray-600">Avg Performance</p>
              <p className="text-lg sm:text-xl font-bold text-purple-600">
                {branchStats.averagePerformance.toFixed(1)}
              </p>
              <p className="text-xs text-gray-500">
                Inventory turnover rate
              </p>
            </div>
            <TrendingUp className="h-6 w-6 sm:h-8 sm:w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Refresh Button */}
      <div className="flex justify-end">
        <button
          onClick={loadBranchStatistics}
          disabled={statsLoading}
          className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors disabled:opacity-50"
          title="Refresh Statistics"
        >
          <RefreshCw className={`h-4 w-4 ${statsLoading ? 'animate-spin' : ''}`} />
          <span>Refresh Stats</span>
        </button>
      </div>

      {/* Navigation Tabs - Mobile Scrollable */}
      <div className="bg-white rounded-lg p-1 shadow-sm border border-gray-200 overflow-x-auto">
        <div className="flex space-x-1 min-w-max sm:min-w-0">
          {[
            { id: 'branches', label: 'Branches', icon: Building2 },
            { id: 'analytics', label: 'Branch Analytics', icon: TrendingUp },
            { id: 'transfers', label: 'Transfers', icon: ArrowRightLeft },
            { id: 'performance', label: 'Performance', icon: Package }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center justify-center space-x-2 py-2 px-3 sm:py-3 sm:px-4 rounded-md transition-all duration-200 whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <tab.icon className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="font-medium text-sm sm:text-base">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'branches' && (
        <div className="bg-white rounded-lg border border-gray-200">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
            </div>
          ) : filteredBranches.length === 0 ? (
            <div className="text-center py-12">
              <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No branches found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || statusFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Get started by adding your first branch'}
              </p>
              {!searchTerm && statusFilter === 'all' && (
                <button
                  onClick={openAddModal}
                  className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors"
                >
                  Add First Branch
                </button>
              )}
            </div>
          ) : (
            <>
              {/* List View */}
              {viewMode === 'list' && (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">City</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manager</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredBranches.map(branch => (
                        <tr key={branch.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{branch.name}</div>
                              <div className="text-sm text-gray-500">{branch.code}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">{branch.address.city}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="space-y-1">
                              <div className="flex items-center">
                                <Phone className="h-4 w-4 text-gray-400 mr-2" />
                                <span className="text-sm text-gray-900">{branch.phone}</span>
                              </div>
                              <div className="flex items-center">
                                <Mail className="h-4 w-4 text-gray-400 mr-2" />
                                <span className="text-sm text-gray-900">{branch.email}</span>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-900">{branch.managerName}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <Clock className="h-4 w-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">
                                {branch.operatingHours.open} - {branch.operatingHours.close}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              branch.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {branch.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => openDetailsModal(branch)}
                                className="text-blue-600 hover:text-blue-900"
                                title="View Details"
                              >
                                <Eye className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => openEditModal(branch)}
                                className="text-teal-600 hover:text-teal-900"
                                title="Edit Branch"
                              >
                                <Edit className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleToggleBranchStatus(branch.id, branch.isActive)}
                                className={`${branch.isActive ? 'text-orange-600 hover:text-orange-900' : 'text-green-600 hover:text-green-900'}`}
                                title={branch.isActive ? 'Deactivate Branch' : 'Activate Branch'}
                              >
                                {branch.isActive ? (
                                  <XCircle className="h-4 w-4" />
                                ) : (
                                  <CheckCircle className="h-4 w-4" />
                                )}
                              </button>
                              <button
                                onClick={() => handleDeleteBranch(branch.id)}
                                className="text-red-600 hover:text-red-900"
                                title="Delete Branch"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Grid View */}
              {viewMode === 'grid' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                  {filteredBranches.map(branch => (
                    <div key={branch.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">{branch.name}</h3>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                            branch.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {branch.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>

                      <div className="space-y-3 text-sm mb-4">
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">{branch.address.street}, {branch.address.city}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">{branch.phone}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Mail className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">{branch.email}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span className="text-gray-600">
                            {branch.operatingHours.open} - {branch.operatingHours.close}
                          </span>
                        </div>
                      </div>

                      <div className="flex justify-end space-x-2 pt-4 border-t border-gray-100">
                        <button
                          onClick={() => openDetailsModal(branch)}
                          className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => openEditModal(branch)}
                          className="p-2 text-teal-600 hover:text-teal-800 hover:bg-teal-50 rounded transition-colors"
                          title="Edit Branch"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleToggleBranchStatus(branch.id, branch.isActive)}
                          className={`p-2 rounded transition-colors ${
                            branch.isActive
                              ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50'
                              : 'text-green-600 hover:text-green-800 hover:bg-green-50'
                          }`}
                          title={branch.isActive ? 'Deactivate Branch' : 'Activate Branch'}
                        >
                          {branch.isActive ? (
                            <XCircle className="h-4 w-4" />
                          ) : (
                            <CheckCircle className="h-4 w-4" />
                          )}
                        </button>
                        <button
                          onClick={() => handleDeleteBranch(branch.id)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-colors"
                          title="Delete Branch"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      )}

      {activeTab === 'transfers' && (
        <div className="space-y-6">
          {/* Stock Transfer Analytics */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Stock Transfer Analytics</h3>
              <button
                onClick={loadTransferAnalytics}
                className="flex items-center space-x-2 px-3 py-1 text-sm text-teal-600 hover:text-teal-800 hover:bg-teal-50 rounded-lg transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Refresh Analytics</span>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">Total Transfers</p>
                    <p className="text-2xl font-bold">{transferAnalytics.totalTransfers || stockTransfers.length}</p>
                    <p className="text-xs text-blue-200 mt-1">All time transfers</p>
                  </div>
                  <ArrowRightLeft className="h-8 w-8 text-blue-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-100 text-sm">Pending Transfers</p>
                    <p className="text-2xl font-bold">{transferAnalytics.pendingTransfers || stockTransfers.filter(t => t.status === 'pending').length}</p>
                    <p className="text-xs text-yellow-200 mt-1">Awaiting approval</p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">Completed Transfers</p>
                    <p className="text-2xl font-bold">{transferAnalytics.completedTransfers || stockTransfers.filter(t => t.status === 'completed').length}</p>
                    <p className="text-xs text-green-200 mt-1">Successfully processed</p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">Items Moved</p>
                    <p className="text-2xl font-bold">{transferAnalytics.totalItemsTransferred}</p>
                    <p className="text-xs text-purple-200 mt-1">Total quantity transferred</p>
                  </div>
                  <Package className="h-8 w-8 text-purple-200" />
                </div>
              </div>
            </div>

            {/* Additional Transfer Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600">Average Transfer Time</p>
                  <p className="text-xl font-bold text-gray-900">{transferAnalytics.averageTransferTime} days</p>
                  <p className="text-xs text-gray-500">From request to completion</p>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600">Rejection Rate</p>
                  <p className="text-xl font-bold text-gray-900">
                    {transferAnalytics.totalTransfers > 0
                      ? ((transferAnalytics.rejectedTransfers / transferAnalytics.totalTransfers) * 100).toFixed(1)
                      : 0}%
                  </p>
                  <p className="text-xs text-gray-500">Rejected transfers</p>
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600">Success Rate</p>
                  <p className="text-xl font-bold text-gray-900">
                    {transferAnalytics.totalTransfers > 0
                      ? ((transferAnalytics.completedTransfers / transferAnalytics.totalTransfers) * 100).toFixed(1)
                      : 0}%
                  </p>
                  <p className="text-xs text-gray-500">Completed transfers</p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Stock Transfers */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Recent Stock Transfers</h3>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowTransferModal(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  <span>New Transfer</span>
                </button>
                <button
                  onClick={() => {
                    console.log('BranchManagement: Refreshing stock transfers...');
                    loadStockTransfers();
                  }}
                  className="flex items-center space-x-2 px-3 py-1 text-sm text-teal-600 hover:text-teal-800 hover:bg-teal-50 rounded-lg transition-colors"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>Refresh</span>
                </button>
              </div>
            </div>

            {stockTransfers.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transfer ID</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Branch</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Branch</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {stockTransfers.slice(0, 20).map((transfer) => {
                      const fromBranch = branches.find(b => b.id === transfer.fromBranchId);
                      const toBranch = branches.find(b => b.id === transfer.toBranchId);

                      return (
                        <tr key={transfer.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {transfer.id.slice(0, 8)}...
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {fromBranch?.name || 'Unknown Branch'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {toBranch?.name || 'Unknown Branch'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {transfer.productName || 'Unknown Product'}
                            </div>
                            <div className="text-sm text-gray-500">
                              SKU: {transfer.productSku || 'N/A'}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">
                              {transfer.quantity} units
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              transfer.status === 'completed' ? 'bg-green-100 text-green-800' :
                              transfer.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              (transfer.status === 'in-transit' || transfer.status === 'in_transit') ? 'bg-blue-100 text-blue-800' :
                              transfer.status === 'approved' ? 'bg-green-100 text-green-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {transfer.status === 'in_transit' ? 'In Transit' : transfer.status.charAt(0).toUpperCase() + transfer.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {new Date(transfer.requestedAt).toLocaleDateString()}
                            </div>
                            <div className="text-sm text-gray-500">
                              {new Date(transfer.requestedAt).toLocaleTimeString()}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-2">
                              <button
                                onClick={() => openTransferDetailsModal(transfer)}
                                className="text-blue-600 hover:text-blue-900"
                                title="View Details"
                              >
                                <Eye className="h-4 w-4" />
                              </button>
                              {transfer.status !== 'completed' && (
                                <button
                                  onClick={() => openTransferEditModal(transfer)}
                                  className="text-teal-600 hover:text-teal-900"
                                  title="Update Status"
                                >
                                  <Edit className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8">
                <ArrowRightLeft className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Stock Transfers</h3>
                <p className="text-gray-600 mb-4">
                  No stock transfers have been created yet. Create your first transfer to get started.
                </p>
                <button
                  onClick={() => setShowTransferModal(true)}
                  className="inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Transfer
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="space-y-6">
          {/* Branch Selection for Analytics */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Branch Analytics</h3>
              <select
                value={selectedBranch}
                onChange={(e) => setSelectedBranch(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              >
                <option value="">Select a branch</option>
                {branches.filter(b => b.isActive).map(branch => (
                  <option key={branch.id} value={branch.id}>{branch.name}</option>
                ))}
              </select>
            </div>

            {!selectedBranch ? (
              <div className="text-center py-12">
                <TrendingUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Branch</h3>
                <p className="text-gray-600">
                  Choose a branch from the dropdown above to view its analytics and performance metrics
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Analytics Cards with Real Database Data */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {/* Total Products */}
                  <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-blue-100 text-sm">Total Products</p>
                        <p className="text-2xl font-bold">{branchAnalytics.totalProducts}</p>
                        <p className="text-xs text-blue-200 mt-1">Unique products in branch</p>
                      </div>
                      <Package className="h-8 w-8 text-blue-200" />
                    </div>
                  </div>

                  {/* Low Stock Items */}
                  <div className="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-red-100 text-sm">Low Stock Items</p>
                        <p className="text-2xl font-bold">{branchAnalytics.lowStockItems}</p>
                        <p className="text-xs text-red-200 mt-1">Below minimum threshold</p>
                      </div>
                      <AlertTriangle className="h-8 w-8 text-red-200" />
                    </div>
                  </div>

                  {/* Out of Stock */}
                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-orange-100 text-sm">Out of Stock</p>
                        <p className="text-2xl font-bold">{branchAnalytics.outOfStockItems}</p>
                        <p className="text-xs text-orange-200 mt-1">Zero inventory items</p>
                      </div>
                      <XCircle className="h-8 w-8 text-orange-200" />
                    </div>
                  </div>

                  {/* Total Items */}
                  <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-100 text-sm">Total Items</p>
                        <p className="text-2xl font-bold">{branchAnalytics.totalItems}</p>
                        <p className="text-xs text-green-200 mt-1">Current stock count</p>
                      </div>
                      <Package className="h-8 w-8 text-green-200" />
                    </div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-900">Performance Metrics</h4>
                    <button
                      onClick={() => loadBranchAnalytics(selectedBranch)}
                      className="flex items-center space-x-2 px-3 py-1 text-sm text-teal-600 hover:text-teal-800 hover:bg-teal-50 rounded-lg transition-colors"
                    >
                      <RefreshCw className="h-4 w-4" />
                      <span>Refresh</span>
                    </button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">Branch Revenue</p>
                      <p className="text-2xl font-bold text-gray-900">
                        ${branchAnalytics.branchRevenue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                      </p>
                      <p className="text-sm text-green-600">From completed orders</p>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">Total Orders</p>
                      <p className="text-2xl font-bold text-gray-900">{branchAnalytics.totalOrders}</p>
                      <p className="text-sm text-blue-600">Completed orders</p>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">Customer Count</p>
                      <p className="text-2xl font-bold text-gray-900">{branchAnalytics.customerCount}</p>
                      <p className="text-sm text-purple-600">Unique customers served</p>
                    </div>
                  </div>

                  {/* Additional Metrics */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">Average Order Value</p>
                      <p className="text-2xl font-bold text-gray-900">
                        ${branchAnalytics.averageOrderValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                      </p>
                      <p className="text-sm text-teal-600">Per completed order</p>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">Inventory Turnover</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {branchAnalytics.inventoryTurnover.toFixed(2)}x
                      </p>
                      <p className="text-sm text-indigo-600">Annual turnover rate</p>
                    </div>
                  </div>
                </div>

                {/* Branch Sales Comparison */}
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-900">Branch Sales Comparison</h4>
                    <button
                      onClick={loadSalesComparison}
                      className="flex items-center space-x-2 px-3 py-1 text-sm text-teal-600 hover:text-teal-800 hover:bg-teal-50 rounded-lg transition-colors"
                    >
                      <RefreshCw className="h-4 w-4" />
                      <span>Refresh</span>
                    </button>
                  </div>

                  {salesComparison.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Sales</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Order Value</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Growth Rate</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {salesComparison.map((branch) => (
                            <tr key={branch.branchId} className={`hover:bg-gray-50 ${branch.branchId === selectedBranch ? 'bg-teal-50' : ''}`}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div className="flex-shrink-0">
                                    <Building2 className="h-8 w-8 text-gray-400" />
                                  </div>
                                  <div className="ml-4">
                                    <div className="text-sm font-medium text-gray-900">{branch.branchName}</div>
                                    {branch.branchId === selectedBranch && (
                                      <div className="text-xs text-teal-600">Current Branch</div>
                                    )}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">
                                  ${branch.totalSales.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">{branch.totalOrders}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">
                                  ${branch.averageOrderValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  branch.growthRate >= 0
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {branch.growthRate >= 0 ? '+' : ''}{branch.growthRate.toFixed(1)}%
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <TrendingUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Sales Data</h3>
                      <p className="text-gray-600">
                        Sales comparison data will appear here once orders are processed
                      </p>
                    </div>
                  )}
                </div>

                {/* Branch Inventory Details */}
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-lg font-semibold text-gray-900">Branch Inventory (Top 10)</h4>
                    <button
                      onClick={() => {
                        console.log('BranchManagement: Loading branch inventory for:', selectedBranch);
                        loadBranchData(selectedBranch);
                      }}
                      className="flex items-center space-x-2 px-3 py-1 text-sm text-teal-600 hover:text-teal-800 hover:bg-teal-50 rounded-lg transition-colors"
                    >
                      <RefreshCw className="h-4 w-4" />
                      <span>Refresh</span>
                    </button>
                  </div>

                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SKU</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Stock</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Min Stock</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {branchInventory.slice(0, 10).map(item => (
                          <tr key={`${item.branchId}-${item.productId}`} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                  <div className="h-8 w-8 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <Package className="h-4 w-4 text-gray-500" />
                                  </div>
                                </div>
                                <div>
                                  <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                                  <div className="text-sm text-gray-500">{item.category}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">{item.sku}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">{item.stock}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">{item.minStock}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                item.stock === 0
                                  ? 'bg-red-100 text-red-800'
                                  : item.stock <= item.minStock
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : 'bg-green-100 text-green-800'
                              }`}>
                                {item.stock === 0 ? 'Out of Stock' : item.stock <= item.minStock ? 'Low Stock' : 'In Stock'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-gray-900">
                                {new Date(item.lastUpdated).toLocaleDateString()}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {branchInventory.length > 10 && (
                      <div className="text-center py-4 text-sm text-gray-600">
                        Showing 10 of {branchInventory.length} items
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'performance' && (
        <BranchPerformanceChart data={branchPerformance} />
      )}

      {/* Branch Form Modal */}
      {showBranchModal && (
        <BranchFormModal
          branch={editingBranch}
          onClose={() => {
            setShowBranchModal(false);
            setEditingBranch(null);
          }}
          onSave={editingBranch ? handleUpdateBranch : handleCreateBranch}
        />
      )}

      {/* Stock Transfer Modal */}
      {showTransferModal && (
        <StockTransferModal
          branches={branches}
          currentUserId={currentUserId}
          onClose={() => setShowTransferModal(false)}
          onTransferCreated={() => {
            console.log('BranchManagement: Transfer created, refreshing data...');
            refreshAllData();
            setShowTransferModal(false);
          }}
        />
      )}

      {/* Stock Transfer Details Modal */}
      {showTransferDetailsModal && selectedTransfer && (
        <StockTransferDetailsModal
          transfer={selectedTransfer}
          onClose={closeTransferDetailsModal}
          onEdit={openTransferEditModal}
        />
      )}

      {/* Stock Transfer Edit Modal */}
      {showTransferEditModal && selectedTransfer && (
        <StockTransferEditModal
          transfer={selectedTransfer}
          currentUserId={currentUserId}
          onClose={closeTransferEditModal}
          onTransferUpdated={handleTransferModalUpdated}
        />
      )}

      {/* Branch Details Modal */}
      {showBranchDetailsModal && viewingBranch && (
        <BranchDetailsModal
          branch={viewingBranch}
          onClose={closeDetailsModal}
          onEdit={() => {
            closeDetailsModal();
            openEditModal(viewingBranch);
          }}
        />
      )}
    </div>
  );
};

export default BranchManagement;
