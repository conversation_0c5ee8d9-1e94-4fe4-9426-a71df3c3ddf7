
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, YAxis, CartesianGrid, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '../ui/chart';
import { BranchPerformance } from '../../types/branch';

interface BranchPerformanceChartProps {
  data: BranchPerformance[];
}

const COLORS = ['#0ea5e9', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

const BranchPerformanceChart = ({ data }: BranchPerformanceChartProps) => {
  const chartConfig = {
    totalSales: {
      label: "Total Sales (Dh)",
      color: "hsl(var(--chart-1))",
    },
    totalOrders: {
      label: "Total Orders",
      color: "hsl(var(--chart-2))",
    },
    customerCount: {
      label: "Customers",
      color: "hsl(var(--chart-3))",
    },
  };

  const salesData = data.map(branch => ({
    name: branch.branchName.replace('YalaOffice ', ''),
    sales: branch.totalSales,
    orders: branch.totalOrders,
    customers: branch.customerCount
  }));

  return (
    <div className="space-y-6">
      {/* Sales Comparison Chart */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Branch Sales Comparison</h3>
        <ChartContainer config={chartConfig} className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={salesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Bar dataKey="sales" fill="var(--color-totalSales)" radius={4} />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Performance Metrics Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {data.map((branch, index) => (
          <div key={branch.branchId} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div 
                className="w-4 h-4 rounded"
                style={{ backgroundColor: COLORS[index % COLORS.length] }}
              />
              <h4 className="text-lg font-semibold text-gray-900">{branch.branchName}</h4>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-teal-600">{branch.totalSales.toLocaleString()}</p>
                <p className="text-sm text-gray-600">Total Sales (Dh)</p>
              </div>
              
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-orange-600">{branch.totalOrders}</p>
                <p className="text-sm text-gray-600">Total Orders</p>
              </div>
              
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-green-600">{branch.averageOrderValue.toFixed(0)}</p>
                <p className="text-sm text-gray-600">Avg Order Value</p>
              </div>
              
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-purple-600">{branch.inventoryTurnover.toFixed(1)}</p>
                <p className="text-sm text-gray-600">Inventory Turnover</p>
              </div>
            </div>

            {/* Top Products */}
            <div className="mt-4">
              <h5 className="text-sm font-medium text-gray-700 mb-2">Top Products</h5>
              <div className="space-y-2">
                {branch.topProducts.slice(0, 2).map((product, idx) => (
                  <div key={product.productId} className="flex justify-between items-center text-sm">
                    <span className="text-gray-600 truncate">{product.productName}</span>
                    <span className="font-medium text-gray-900">{product.unitsSold} units</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BranchPerformanceChart;
