
import { useState, useEffect } from 'react';
import { X, ArrowRightLeft, Package } from 'lucide-react';
import { Branch } from '../../types/branch';
import { Product } from '../../types/inventory';
import { createStockTransfer } from '../../services/branchService';
import { getProducts } from '../../services/inventoryService';
import { realTimeService } from '../../services/realTimeService';

interface StockTransferModalProps {
  branches: Branch[];
  onClose: () => void;
  onTransferCreated: () => void;
  currentUserId?: string;
}

const StockTransferModal = ({ branches, onClose, onTransferCreated, currentUserId = 'USR-001' }: StockTransferModalProps) => {
  const [formData, setFormData] = useState({
    fromBranchId: '',
    toBranchId: '',
    productId: '',
    quantity: '',
    notes: ''
  });
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingProducts, setLoadingProducts] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoadingProducts(true);
      const productsData = await getProducts();
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading products:', error);
      setErrors(prev => ({ ...prev, products: 'Failed to load products' }));
    } finally {
      setLoadingProducts(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.fromBranchId) {
      newErrors.fromBranchId = 'Source branch is required';
    }

    if (!formData.toBranchId) {
      newErrors.toBranchId = 'Destination branch is required';
    }

    if (formData.fromBranchId === formData.toBranchId) {
      newErrors.toBranchId = 'Source and destination branches must be different';
    }

    if (!formData.productId) {
      newErrors.productId = 'Product is required';
    }

    if (!formData.quantity || parseInt(formData.quantity) <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      console.log('StockTransferModal: Creating transfer:', formData);

      const newTransfer = await createStockTransfer({
        fromBranchId: formData.fromBranchId,
        toBranchId: formData.toBranchId,
        productId: formData.productId,
        quantity: parseInt(formData.quantity),
        status: 'pending',
        requestedBy: currentUserId,
        notes: formData.notes || undefined
      });

      if (newTransfer) {
        // Emit real-time event
        realTimeService.emit('transfer-created', {
          transfer: newTransfer,
          userId: currentUserId
        });

        console.log('StockTransferModal: Transfer created successfully:', newTransfer);
        onTransferCreated();
      } else {
        throw new Error('Failed to create transfer');
      }
    } catch (error) {
      console.error('Error creating stock transfer:', error);
      setErrors(prev => ({
        ...prev,
        submit: 'Error creating stock transfer: ' + (error as Error).message
      }));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <ArrowRightLeft className="h-5 w-5 text-teal-600" />
            <h3 className="text-lg font-semibold text-gray-900">New Stock Transfer</h3>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Error Display */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              From Branch *
            </label>
            <select
              value={formData.fromBranchId}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, fromBranchId: e.target.value }));
                setErrors(prev => ({ ...prev, fromBranchId: '' }));
              }}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent ${
                errors.fromBranchId ? 'border-red-300' : 'border-gray-300'
              }`}
              required
            >
              <option value="">Select source branch</option>
              {branches.filter(b => b.isActive).map(branch => (
                <option key={branch.id} value={branch.id}>{branch.name}</option>
              ))}
            </select>
            {errors.fromBranchId && (
              <p className="mt-1 text-sm text-red-600">{errors.fromBranchId}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              To Branch *
            </label>
            <select
              value={formData.toBranchId}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, toBranchId: e.target.value }));
                setErrors(prev => ({ ...prev, toBranchId: '' }));
              }}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent ${
                errors.toBranchId ? 'border-red-300' : 'border-gray-300'
              }`}
              required
            >
              <option value="">Select destination branch</option>
              {branches.filter(b => b.isActive && b.id !== formData.fromBranchId).map(branch => (
                <option key={branch.id} value={branch.id}>{branch.name}</option>
              ))}
            </select>
            {errors.toBranchId && (
              <p className="mt-1 text-sm text-red-600">{errors.toBranchId}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product *
            </label>
            {loadingProducts ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600"></div>
                <span className="ml-2 text-sm text-gray-600">Loading products...</span>
              </div>
            ) : errors.products ? (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-600">{errors.products}</p>
                <button
                  type="button"
                  onClick={loadProducts}
                  className="mt-2 text-sm text-teal-600 hover:text-teal-800"
                >
                  Retry loading products
                </button>
              </div>
            ) : (
              <>
                <select
                  value={formData.productId}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, productId: e.target.value }));
                    setErrors(prev => ({ ...prev, productId: '' }));
                  }}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent ${
                    errors.productId ? 'border-red-300' : 'border-gray-300'
                  }`}
                  required
                >
                  <option value="">Select product to transfer</option>
                  {products.filter(p => p.isActive && p.stock > 0).map(product => (
                    <option key={product.id} value={product.id}>
                      {product.title} (Stock: {product.stock})
                    </option>
                  ))}
                </select>
                {errors.productId && (
                  <p className="mt-1 text-sm text-red-600">{errors.productId}</p>
                )}
                {products.length === 0 && (
                  <p className="mt-1 text-sm text-gray-500">No products available for transfer</p>
                )}
              </>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity *
            </label>
            <input
              type="number"
              min="1"
              value={formData.quantity}
              onChange={(e) => {
                setFormData(prev => ({ ...prev, quantity: e.target.value }));
                setErrors(prev => ({ ...prev, quantity: '' }));
              }}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent ${
                errors.quantity ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter quantity to transfer"
              required
            />
            {errors.quantity && (
              <p className="mt-1 text-sm text-red-600">{errors.quantity}</p>
            )}
            {formData.productId && (
              <p className="mt-1 text-sm text-gray-500">
                Available stock: {products.find(p => p.id === formData.productId)?.stock || 0}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none"
              rows={3}
              placeholder="Optional notes about the transfer"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 disabled:opacity-50"
            >
              {loading ? 'Creating...' : 'Create Transfer'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StockTransferModal;
