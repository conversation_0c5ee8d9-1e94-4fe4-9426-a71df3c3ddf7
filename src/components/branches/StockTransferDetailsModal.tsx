import { useState, useEffect } from 'react';
import { X, ArrowRightLeft, Package, User, Clock, CheckCircle, AlertTriangle, XCircle, Building2, FileText } from 'lucide-react';
import { StockTransfer } from '../../types/branch';
import { liveDataService } from '../../services/liveDataService';
import { supabase } from '../../integrations/supabase/client';

interface StockTransferDetailsModalProps {
  transfer: StockTransfer;
  onClose: () => void;
  onEdit?: (transfer: StockTransfer) => void;
}

interface TransferDetails {
  id: string;
  from_branch_id: string;
  to_branch_id: string;
  product_id: string;
  quantity: number;
  status: string;
  requested_by: string;
  approved_by?: string;
  notes?: string;
  requested_at: string;
  approved_at?: string;
  completed_at?: string;
  rejected_at?: string;
  rejection_reason?: string;
  from_branch?: { name: string; code: string };
  to_branch?: { name: string; code: string };
  product?: { title: string; sku: string; price: number };
  requester?: { full_name: string; email: string };
  approver?: { full_name: string; email: string };
}

const StockTransferDetailsModal = ({ transfer, onClose, onEdit }: StockTransferDetailsModalProps) => {
  const [transferDetails, setTransferDetails] = useState<TransferDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTransferDetails();
  }, [transfer.id]);

  const loadTransferDetails = async () => {
    try {
      setLoading(true);
      console.log('StockTransferDetailsModal: Loading transfer details for:', transfer.id);

      const { data, error } = await supabase
        .from('stock_transfers')
        .select(`
          *,
          from_branch:branches!stock_transfers_from_branch_id_fkey(name, code),
          to_branch:branches!stock_transfers_to_branch_id_fkey(name, code),
          product:products(title, sku, price),
          requester:users!stock_transfers_requested_by_fkey(full_name, email),
          approver:users!stock_transfers_approved_by_fkey(full_name, email)
        `)
        .eq('id', transfer.id)
        .single();

      if (error) {
        console.error('Error loading transfer details:', error);
        setError('Failed to load transfer details');
        return;
      }

      setTransferDetails(data);
      console.log('StockTransferDetailsModal: Transfer details loaded:', data);
    } catch (error) {
      console.error('Error in loadTransferDetails:', error);
      setError('Failed to load transfer details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'in-transit':
      case 'in_transit':
        return <ArrowRightLeft className="h-5 w-5 text-blue-600" />;
      case 'rejected':
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'in-transit':
      case 'in_transit':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatStatusDisplay = (status: string) => {
    switch (status) {
      case 'in_transit':
        return 'In Transit';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading transfer details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !transferDetails) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full p-8">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error || 'Transfer details not found'}</p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-teal-600 to-amber-500 text-white rounded-t-xl">
          <div className="flex items-center space-x-3">
            <div className="bg-white bg-opacity-20 p-3 rounded-lg">
              <ArrowRightLeft className="h-8 w-8 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">Stock Transfer Details</h2>
              <p className="text-teal-100">Transfer ID: {transferDetails.id.slice(0, 8)}...</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {onEdit && transferDetails.status === 'pending' && (
              <button
                onClick={() => onEdit(transfer)}
                className="px-4 py-2 bg-white bg-opacity-20 text-white rounded-lg hover:bg-opacity-30 transition-colors"
              >
                Edit Transfer
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Status and Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                {getStatusIcon(transferDetails.status)}
                <h3 className="font-semibold text-gray-900">Status</h3>
              </div>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(transferDetails.status)}`}>
                {formatStatusDisplay(transferDetails.status)}
              </span>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Package className="h-5 w-5 text-teal-600" />
                <h3 className="font-semibold text-gray-900">Quantity</h3>
              </div>
              <p className="text-2xl font-bold text-gray-900">{transferDetails.quantity}</p>
              <p className="text-sm text-gray-600">units</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="h-5 w-5 text-teal-600" />
                <h3 className="font-semibold text-gray-900">Requested</h3>
              </div>
              <p className="text-sm text-gray-900">{formatDate(transferDetails.requested_at)}</p>
            </div>
          </div>

          {/* Branch Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Building2 className="h-5 w-5 text-red-600" />
                <h3 className="font-semibold text-gray-900">From Branch</h3>
              </div>
              <p className="text-lg font-medium text-gray-900">{transferDetails.from_branch?.name}</p>
              <p className="text-sm text-gray-600">Code: {transferDetails.from_branch?.code}</p>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Building2 className="h-5 w-5 text-green-600" />
                <h3 className="font-semibold text-gray-900">To Branch</h3>
              </div>
              <p className="text-lg font-medium text-gray-900">{transferDetails.to_branch?.name}</p>
              <p className="text-sm text-gray-600">Code: {transferDetails.to_branch?.code}</p>
            </div>
          </div>

          {/* Product Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <Package className="h-5 w-5 text-teal-600" />
              <h3 className="font-semibold text-gray-900">Product Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600">Product Name</p>
                <p className="font-medium text-gray-900">{transferDetails.product?.title || 'Unknown Product'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">SKU</p>
                <p className="font-medium text-gray-900">{transferDetails.product?.sku || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Unit Price</p>
                <p className="font-medium text-gray-900">${transferDetails.product?.price?.toFixed(2) || '0.00'}</p>
              </div>
            </div>
          </div>

          {/* People Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <User className="h-5 w-5 text-teal-600" />
                <h3 className="font-semibold text-gray-900">Requested By</h3>
              </div>
              <p className="font-medium text-gray-900">{transferDetails.requester?.full_name || 'Unknown User'}</p>
              <p className="text-sm text-gray-600">{transferDetails.requester?.email || 'No email'}</p>
            </div>

            {transferDetails.approver && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <h3 className="font-semibold text-gray-900">Approved By</h3>
                </div>
                <p className="font-medium text-gray-900">{transferDetails.approver.full_name}</p>
                <p className="text-sm text-gray-600">{transferDetails.approver.email}</p>
                {transferDetails.approved_at && (
                  <p className="text-xs text-gray-500 mt-1">
                    Approved: {formatDate(transferDetails.approved_at)}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Timeline */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-4">
              <Clock className="h-5 w-5 text-teal-600" />
              <h3 className="font-semibold text-gray-900">Transfer Timeline</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                <div>
                  <p className="font-medium text-gray-900">Transfer Requested</p>
                  <p className="text-sm text-gray-600">{formatDate(transferDetails.requested_at)}</p>
                </div>
              </div>

              {transferDetails.approved_at && (
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                  <div>
                    <p className="font-medium text-gray-900">Transfer Approved</p>
                    <p className="text-sm text-gray-600">{formatDate(transferDetails.approved_at)}</p>
                  </div>
                </div>
              )}

              {transferDetails.completed_at && (
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-teal-600 rounded-full mt-2"></div>
                  <div>
                    <p className="font-medium text-gray-900">Transfer Completed</p>
                    <p className="text-sm text-gray-600">{formatDate(transferDetails.completed_at)}</p>
                  </div>
                </div>
              )}

              {transferDetails.rejected_at && (
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-2 h-2 bg-red-600 rounded-full mt-2"></div>
                  <div>
                    <p className="font-medium text-gray-900">Transfer Rejected</p>
                    <p className="text-sm text-gray-600">{formatDate(transferDetails.rejected_at)}</p>
                    {transferDetails.rejection_reason && (
                      <p className="text-sm text-red-600 mt-1">Reason: {transferDetails.rejection_reason}</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          {transferDetails.notes && (
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <FileText className="h-5 w-5 text-teal-600" />
                <h3 className="font-semibold text-gray-900">Notes</h3>
              </div>
              <p className="text-gray-700 whitespace-pre-wrap">{transferDetails.notes}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Close
            </button>
            {onEdit && transferDetails.status === 'pending' && (
              <button
                onClick={() => onEdit(transfer)}
                className="px-6 py-2 bg-gradient-to-r from-teal-600 to-teal-700 text-white rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200"
              >
                Edit Transfer
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StockTransferDetailsModal;
