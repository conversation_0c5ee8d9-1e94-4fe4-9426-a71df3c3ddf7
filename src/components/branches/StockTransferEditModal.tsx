import { useState, useEffect } from 'react';
import { X, ArrowRightLeft, Package, CheckCircle, XCircle, AlertTriangle, Save } from 'lucide-react';
import { StockTransfer } from '../../types/branch';
import { supabase } from '../../integrations/supabase/client';
import { realTimeService } from '../../services/realTimeService';

interface StockTransferEditModalProps {
  transfer: StockTransfer;
  onClose: () => void;
  onTransferUpdated: () => void;
  currentUserId?: string;
}

interface EditFormData {
  status: string;
  notes: string;
  rejectionReason?: string;
}

const StockTransferEditModal = ({ 
  transfer, 
  onClose, 
  onTransferUpdated, 
  currentUserId = 'USR-001' 
}: StockTransferEditModalProps) => {
  const [formData, setFormData] = useState<EditFormData>({
    status: transfer.status,
    notes: transfer.notes || '',
    rejectionReason: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [transferDetails, setTransferDetails] = useState<any>(null);

  useEffect(() => {
    loadTransferDetails();
  }, [transfer.id]);

  const loadTransferDetails = async () => {
    try {
      const { data, error } = await supabase
        .from('stock_transfers')
        .select(`
          *,
          from_branch:branches!stock_transfers_from_branch_id_fkey(name, code),
          to_branch:branches!stock_transfers_to_branch_id_fkey(name, code),
          product:products(title, sku, price)
        `)
        .eq('id', transfer.id)
        .single();

      if (error) {
        console.error('Error loading transfer details:', error);
        setError('Failed to load transfer details');
        return;
      }

      setTransferDetails(data);
    } catch (error) {
      console.error('Error in loadTransferDetails:', error);
      setError('Failed to load transfer details');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      console.log('StockTransferEditModal: Updating transfer:', transfer.id, formData);

      // Convert status to match database schema (in_transit instead of in-transit)
      const dbStatus = formData.status === 'in-transit' ? 'in_transit' : formData.status;

      const updateData: any = {
        status: dbStatus,
        notes: formData.notes
      };

      // Add status-specific fields
      if (dbStatus === 'approved' || dbStatus === 'in_transit') {
        updateData.approved_by = currentUserId;
        updateData.approved_at = new Date().toISOString();
      } else if (dbStatus === 'completed') {
        updateData.completed_at = new Date().toISOString();
        if (!transferDetails.approved_by) {
          updateData.approved_by = currentUserId;
          updateData.approved_at = new Date().toISOString();
        }
      } else if (dbStatus === 'cancelled') {
        // For rejected/cancelled transfers, we'll just set status to cancelled
        // since the database doesn't have rejected_at or rejection_reason columns
        // We can store the rejection reason in the notes field
        if (formData.rejectionReason) {
          updateData.notes = formData.notes ?
            `${formData.notes}\n\nRejection Reason: ${formData.rejectionReason}` :
            `Rejection Reason: ${formData.rejectionReason}`;
        }
      }

      const { error } = await supabase
        .from('stock_transfers')
        .update(updateData)
        .eq('id', transfer.id);

      if (error) {
        console.error('Error updating transfer:', error);
        console.error('Update data that failed:', updateData);
        console.error('Transfer ID:', transfer.id);
        setError(`Failed to update transfer: ${error.message || error.details || 'Unknown error'}`);
        return;
      }

      // Emit real-time event
      realTimeService.emit('transfer-updated', {
        transferId: transfer.id,
        newStatus: formData.status,
        userId: currentUserId
      });

      console.log('StockTransferEditModal: Transfer updated successfully');
      onTransferUpdated();
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      setError('Failed to update transfer');
    } finally {
      setLoading(false);
    }
  };

  const getStatusOptions = () => {
    const currentStatus = transfer.status;
    const options = [];

    // Convert database status to display format
    const getDisplayLabel = (status: string) => {
      switch (status) {
        case 'in_transit':
          return 'In Transit';
        case 'cancelled':
          return 'Cancelled';
        default:
          return status.charAt(0).toUpperCase() + status.slice(1);
      }
    };

    // Always allow current status
    options.push({ value: currentStatus, label: getDisplayLabel(currentStatus) });

    // Add valid next statuses based on database schema
    switch (currentStatus) {
      case 'pending':
        options.push(
          { value: 'approved', label: 'Approved' },
          { value: 'in-transit', label: 'In Transit' }, // Display format
          { value: 'cancelled', label: 'Cancelled' }
        );
        break;
      case 'approved':
        options.push(
          { value: 'in-transit', label: 'In Transit' }, // Display format
          { value: 'completed', label: 'Completed' },
          { value: 'cancelled', label: 'Cancelled' }
        );
        break;
      case 'in-transit':
      case 'in_transit': // Handle both formats
        options.push(
          { value: 'completed', label: 'Completed' },
          { value: 'cancelled', label: 'Cancelled' }
        );
        break;
    }

    // Remove duplicates
    return options.filter((option, index, self) =>
      index === self.findIndex(o => o.value === option.value)
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'cancelled':
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'in-transit':
      case 'in_transit':
        return <ArrowRightLeft className="h-5 w-5 text-blue-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    }
  };

  if (!transferDetails) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading transfer details...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-teal-600 to-amber-500 text-white rounded-t-xl">
          <div className="flex items-center space-x-3">
            <div className="bg-white bg-opacity-20 p-3 rounded-lg">
              <ArrowRightLeft className="h-8 w-8 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold">Edit Stock Transfer</h2>
              <p className="text-teal-100">Transfer ID: {transfer.id.slice(0, 8)}...</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <XCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Transfer Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Transfer Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">From Branch</p>
                <p className="font-medium">{transferDetails.from_branch?.name}</p>
              </div>
              <div>
                <p className="text-gray-600">To Branch</p>
                <p className="font-medium">{transferDetails.to_branch?.name}</p>
              </div>
              <div>
                <p className="text-gray-600">Product</p>
                <p className="font-medium">{transferDetails.product?.title}</p>
              </div>
              <div>
                <p className="text-gray-600">Quantity</p>
                <p className="font-medium">{transferDetails.quantity} units</p>
              </div>
            </div>
          </div>

          {/* Status Update */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <div className="flex items-center space-x-2">
                {getStatusIcon(formData.status)}
                <span>Transfer Status *</span>
              </div>
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              required
            >
              {getStatusOptions().map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Cancellation Reason (only show if status is cancelled) */}
          {formData.status === 'cancelled' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cancellation Reason *
              </label>
              <textarea
                value={formData.rejectionReason}
                onChange={(e) => setFormData(prev => ({ ...prev, rejectionReason: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                rows={3}
                placeholder="Please provide a reason for cancelling this transfer..."
                required={formData.status === 'cancelled'}
              />
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              rows={4}
              placeholder="Add any additional notes or comments..."
            />
          </div>

          {/* Actions */}
          <div className="flex space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 disabled:opacity-50 flex items-center justify-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{loading ? 'Updating...' : 'Update Transfer'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StockTransferEditModal;
