
import { useState, useEffect } from 'react';
import { Bell } from 'lucide-react';
import NotificationCenter from '../notifications/NotificationCenter';
import { getUserNotifications } from '../../services/notificationService';

interface NotificationBellProps {
  userId: string;
  className?: string;
}

const NotificationBell = ({ userId, className = "" }: NotificationBellProps) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    loadUnreadCount();
    // Poll for new notifications every 30 seconds
    const interval = setInterval(loadUnreadCount, 30000);
    return () => clearInterval(interval);
  }, [userId]);

  const loadUnreadCount = async () => {
    try {
      const notifications = await getUserNotifications(userId);
      const count = notifications.filter(n => !n.isRead).length;
      setUnreadCount(count);
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  const handleNotificationRead = (newUnreadCount: number) => {
    setUnreadCount(newUnreadCount);
  };

  return (
    <>
      <button
        onClick={() => setShowNotifications(true)}
        className={`relative p-2 bg-white bg-opacity-20 rounded-full hover:bg-opacity-30 transition-all ${className}`}
      >
        <Bell className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      <NotificationCenter
        userId={userId}
        isOpen={showNotifications}
        onClose={() => setShowNotifications(false)}
        onNotificationRead={handleNotificationRead}
      />
    </>
  );
};

export default NotificationBell;
