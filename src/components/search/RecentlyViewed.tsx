
import { useState, useEffect } from 'react';
import { Clock, X, ShoppingCart } from 'lucide-react';
import { Product } from '../../types/inventory';

interface RecentlyViewedProps {
  userType: string;
  onAddToCart: (product: Product) => void;
}

const RecentlyViewed = ({ userType, onAddToCart }: RecentlyViewedProps) => {
  const [recentProducts, setRecentProducts] = useState<Product[]>([]);

  useEffect(() => {
    loadRecentlyViewed();
  }, []);

  const loadRecentlyViewed = () => {
    const stored = localStorage.getItem('recentlyViewedProducts');
    if (stored) {
      try {
        const products = JSON.parse(stored);
        setRecentProducts(products.slice(0, 8)); // Show last 8 products
      } catch (error) {
        console.error('Error loading recently viewed products:', error);
      }
    }
  };

  const addToRecentlyViewed = (product: Product) => {
    const stored = localStorage.getItem('recentlyViewedProducts');
    let recentProducts: Product[] = [];
    
    if (stored) {
      try {
        recentProducts = JSON.parse(stored);
      } catch (error) {
        console.error('Error parsing recently viewed products:', error);
      }
    }

    // Remove if already exists
    recentProducts = recentProducts.filter(p => p.id !== product.id);
    
    // Add to beginning
    recentProducts.unshift(product);
    
    // Keep only last 20 products
    recentProducts = recentProducts.slice(0, 20);
    
    localStorage.setItem('recentlyViewedProducts', JSON.stringify(recentProducts));
    setRecentProducts(recentProducts.slice(0, 8));
  };

  const removeFromRecentlyViewed = (productId: string) => {
    const stored = localStorage.getItem('recentlyViewedProducts');
    if (stored) {
      try {
        let products = JSON.parse(stored);
        products = products.filter((p: Product) => p.id !== productId);
        localStorage.setItem('recentlyViewedProducts', JSON.stringify(products));
        setRecentProducts(products.slice(0, 8));
      } catch (error) {
        console.error('Error removing from recently viewed:', error);
      }
    }
  };

  const clearRecentlyViewed = () => {
    localStorage.removeItem('recentlyViewedProducts');
    setRecentProducts([]);
  };

  // Export function to be used by ProductGrid
  (window as any).addToRecentlyViewed = addToRecentlyViewed;

  if (recentProducts.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center space-x-2">
          <Clock className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Recently Viewed</h3>
        </div>
        <button
          onClick={clearRecentlyViewed}
          className="text-sm text-gray-500 hover:text-gray-700"
        >
          Clear All
        </button>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
        {recentProducts.map(product => {
          const price = userType === 'reseller' ? product.resellerPrice : product.price;
          
          return (
            <div key={product.id} className="relative group">
              <div className="bg-gray-50 rounded-lg overflow-hidden">
                <div className="relative">
                  <img
                    src={product.image}
                    alt={product.title}
                    className="w-full h-24 object-cover"
                  />
                  <button
                    onClick={() => removeFromRecentlyViewed(product.id)}
                    className="absolute top-1 right-1 bg-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity shadow-sm"
                  >
                    <X className="h-3 w-3 text-gray-400" />
                  </button>
                </div>
                
                <div className="p-2">
                  <h4 className="text-xs font-medium text-gray-900 line-clamp-2 mb-1">
                    {product.title}
                  </h4>
                  <p className="text-xs font-bold text-teal-600 mb-2">
                    {price.toFixed(2)} Dh
                  </p>
                  
                  <button
                    onClick={() => onAddToCart(product)}
                    disabled={product.stock === 0}
                    className={`w-full py-1 rounded text-xs transition-colors ${
                      product.stock === 0
                        ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                        : 'bg-teal-600 text-white hover:bg-teal-700'
                    }`}
                  >
                    <ShoppingCart className="h-3 w-3 inline mr-1" />
                    {product.stock === 0 ? 'Out' : 'Add'}
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default RecentlyViewed;
