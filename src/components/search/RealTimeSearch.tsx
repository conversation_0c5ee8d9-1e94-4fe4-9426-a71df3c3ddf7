import { useState, useEffect, useCallback, useMemo } from 'react';
import { Search, Filter, X, SortAsc, SortDesc, Grid, List } from 'lucide-react';
import { Product } from '../../types/inventory';
import { getProducts } from '../../services/inventoryService';
import { realTimeService } from '../../services/realTimeService';
import { debounce } from '../../utils/debounce';

export interface SearchFilters {
  searchTerm: string;
  category: string;
  priceRange: [number, number];
  minRating: number;
  inStock: boolean;
  isNew: boolean;
  brand: string;
  sortBy: 'relevance' | 'price-low' | 'price-high' | 'rating' | 'newest' | 'popularity';
  availability: string;
  discount: boolean;
  freeShipping: boolean;
}

interface RealTimeSearchProps {
  onFiltersChange: (filters: SearchFilters) => void;
  onViewModeChange: (mode: 'grid' | 'list') => void;
  initialFilters?: Partial<SearchFilters>;
  userType?: string;
}

const RealTimeSearch = ({ 
  onFiltersChange, 
  onViewModeChange, 
  initialFilters = {},
  userType = 'client'
}: RealTimeSearchProps) => {
  const [filters, setFilters] = useState<SearchFilters>({
    searchTerm: '',
    category: '',
    priceRange: [0, 1000],
    minRating: 0,
    inStock: false,
    isNew: false,
    brand: '',
    sortBy: 'relevance',
    availability: 'all',
    discount: false,
    freeShipping: false,
    ...initialFilters
  });

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [brands, setBrands] = useState<string[]>([]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchTerm: string) => {
      if (searchTerm.length > 2) {
        generateSearchSuggestions(searchTerm);
      } else {
        setSearchSuggestions([]);
        setShowSuggestions(false);
      }
    }, 300),
    []
  );

  useEffect(() => {
    loadInitialData();
    
    // Subscribe to real-time product updates
    const unsubscribe = realTimeService.subscribe('product-updated', (event) => {
      // Refresh search results when products are updated
      loadInitialData();
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    // Emit filter changes to parent and sync across sessions
    onFiltersChange(filters);
    
    // Sync search state across user sessions
    realTimeService.emit('search-filters-updated', {
      userId: userType,
      filters
    });
  }, [filters, onFiltersChange, userType]);

  useEffect(() => {
    debouncedSearch(filters.searchTerm);
  }, [filters.searchTerm, debouncedSearch]);

  const loadInitialData = async () => {
    try {
      const productsData = await getProducts();
      setProducts(productsData);
      
      // Extract unique categories and brands
      const uniqueCategories = [...new Set(productsData.map(p => p.category))];
      const uniqueBrands = [...new Set(productsData.map(p => p.brand))];
      
      setCategories(uniqueCategories);
      setBrands(uniqueBrands);
    } catch (error) {
      console.error('Error loading search data:', error);
    }
  };

  const generateSearchSuggestions = (searchTerm: string) => {
    const suggestions = products
      .filter(product => 
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.category.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .map(product => product.title)
      .slice(0, 5);
    
    setSearchSuggestions(suggestions);
    setShowSuggestions(suggestions.length > 0);
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSearchTermChange = (value: string) => {
    handleFilterChange('searchTerm', value);
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleFilterChange('searchTerm', suggestion);
    setShowSuggestions(false);
  };

  const clearFilters = () => {
    setFilters({
      searchTerm: '',
      category: '',
      priceRange: [0, 1000],
      minRating: 0,
      inStock: false,
      isNew: false,
      brand: '',
      sortBy: 'relevance',
      availability: 'all',
      discount: false,
      freeShipping: false
    });
  };

  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode);
    onViewModeChange(mode);
  };

  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.category) count++;
    if (filters.brand) count++;
    if (filters.inStock) count++;
    if (filters.isNew) count++;
    if (filters.discount) count++;
    if (filters.freeShipping) count++;
    if (filters.minRating > 0) count++;
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 1000) count++;
    return count;
  }, [filters]);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      {/* Main Search Bar */}
      <div className="relative mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search products, brands, categories..."
            value={filters.searchTerm}
            onChange={(e) => handleSearchTermChange(e.target.value)}
            onFocus={() => setShowSuggestions(searchSuggestions.length > 0)}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent text-lg"
          />
          {filters.searchTerm && (
            <button
              onClick={() => handleSearchTermChange('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Search Suggestions */}
        {showSuggestions && searchSuggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 mt-1">
            {searchSuggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="w-full text-left px-4 py-2 hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Filter Controls */}
      <div className="flex flex-wrap items-center gap-4 mb-4">
        {/* Quick Filters */}
        <div className="flex items-center space-x-2">
          <select
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>

          <select
            value={filters.brand}
            onChange={(e) => handleFilterChange('brand', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="">All Brands</option>
            {brands.map(brand => (
              <option key={brand} value={brand}>{brand}</option>
            ))}
          </select>

          <select
            value={filters.sortBy}
            onChange={(e) => handleFilterChange('sortBy', e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="relevance">Relevance</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
            <option value="rating">Rating</option>
            <option value="newest">Newest</option>
            <option value="popularity">Popularity</option>
          </select>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden">
          <button
            onClick={() => handleViewModeChange('grid')}
            className={`px-3 py-2 ${viewMode === 'grid' ? 'bg-orange-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
          >
            <Grid className="h-4 w-4" />
          </button>
          <button
            onClick={() => handleViewModeChange('list')}
            className={`px-3 py-2 ${viewMode === 'list' ? 'bg-orange-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
          >
            <List className="h-4 w-4" />
          </button>
        </div>

        {/* Advanced Filters Toggle */}
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          <Filter className="h-4 w-4" />
          <span>Filters</span>
          {activeFiltersCount > 0 && (
            <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
              {activeFiltersCount}
            </span>
          )}
        </button>

        {/* Clear Filters */}
        {activeFiltersCount > 0 && (
          <button
            onClick={clearFilters}
            className="text-orange-600 hover:text-orange-700 font-medium"
          >
            Clear all
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className="border-t border-gray-200 pt-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price Range: {filters.priceRange[0]} - {filters.priceRange[1]} Dh
              </label>
              <input
                type="range"
                min="0"
                max="1000"
                step="10"
                value={filters.priceRange[1]}
                onChange={(e) => handleFilterChange('priceRange', [filters.priceRange[0], parseInt(e.target.value)])}
                className="w-full"
              />
            </div>

            {/* Rating Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Rating
              </label>
              <select
                value={filters.minRating}
                onChange={(e) => handleFilterChange('minRating', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              >
                <option value={0}>Any Rating</option>
                <option value={1}>1+ Stars</option>
                <option value={2}>2+ Stars</option>
                <option value={3}>3+ Stars</option>
                <option value={4}>4+ Stars</option>
                <option value={5}>5 Stars</option>
              </select>
            </div>

            {/* Availability */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Availability
              </label>
              <select
                value={filters.availability}
                onChange={(e) => handleFilterChange('availability', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              >
                <option value="all">All Products</option>
                <option value="in-stock">In Stock</option>
                <option value="low-stock">Low Stock</option>
                <option value="out-of-stock">Out of Stock</option>
              </select>
            </div>

            {/* Special Filters */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Special Filters</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.isNew}
                    onChange={(e) => handleFilterChange('isNew', e.target.checked)}
                    className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                  />
                  <span className="ml-2 text-sm">New Products</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.discount}
                    onChange={(e) => handleFilterChange('discount', e.target.checked)}
                    className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                  />
                  <span className="ml-2 text-sm">On Sale</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.freeShipping}
                    onChange={(e) => handleFilterChange('freeShipping', e.target.checked)}
                    className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                  />
                  <span className="ml-2 text-sm">Free Shipping</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RealTimeSearch;
