
import { useState, useEffect } from 'react';
import { Bot, Plus, Settings, Mail, Package, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Switch } from '../ui/switch';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Textarea } from '../ui/textarea';
import { useToast } from '../../hooks/use-toast';
import { 
  getAutomationRules, 
  createAutomationRule, 
  updateAutomationRule,
  getStockReorderRules,
  createStockReorderRule,
  getApprovalWorkflows,
  createApprovalWorkflow
} from '../../services/automationService';
import { AutomationRule, StockReorderRule, ApprovalWorkflow } from '../../types/automation';

const WorkflowAutomation = () => {
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([]);
  const [stockRules, setStockRules] = useState<StockReorderRule[]>([]);
  const [approvalWorkflows, setApprovalWorkflows] = useState<ApprovalWorkflow[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateRule, setShowCreateRule] = useState(false);
  const [activeTab, setActiveTab] = useState<'rules' | 'stock' | 'approvals'>('rules');
  const { toast } = useToast();

  useEffect(() => {
    loadAutomationData();
  }, []);

  const loadAutomationData = async () => {
    setLoading(true);
    try {
      const [rules, stockRulesData, workflows] = await Promise.all([
        getAutomationRules(),
        getStockReorderRules(),
        getApprovalWorkflows()
      ]);
      setAutomationRules(rules);
      setStockRules(stockRulesData);
      setApprovalWorkflows(workflows);
    } catch (error) {
      console.error('Error loading automation data:', error);
      toast({
        title: "Error",
        description: "Failed to load automation data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggleRule = async (ruleId: string, isActive: boolean) => {
    try {
      await updateAutomationRule(ruleId, { isActive });
      setAutomationRules(rules => 
        rules.map(rule => 
          rule.id === ruleId ? { ...rule, isActive } : rule
        )
      );
      toast({
        title: "Rule Updated",
        description: `Automation rule ${isActive ? 'enabled' : 'disabled'}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update rule",
        variant: "destructive"
      });
    }
  };

  const getRuleIcon = (type: string) => {
    switch (type) {
      case 'stock_reorder': return Package;
      case 'email_notification': return Mail;
      case 'scheduled_report': return Clock;
      default: return Bot;
    }
  };

  const getRuleTypeColor = (type: string) => {
    switch (type) {
      case 'stock_reorder': return 'bg-blue-100 text-blue-800';
      case 'email_notification': return 'bg-green-100 text-green-800';
      case 'scheduled_report': return 'bg-purple-100 text-purple-800';
      case 'approval_workflow': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Workflow Automation</h2>
          <p className="text-gray-600">Automate repetitive tasks and streamline operations</p>
        </div>
        <Dialog open={showCreateRule} onOpenChange={setShowCreateRule}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Rule
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Automation Rule</DialogTitle>
              <DialogDescription>
                Set up automated workflows to streamline your operations
              </DialogDescription>
            </DialogHeader>
            <CreateRuleForm onSuccess={() => { setShowCreateRule(false); loadAutomationData(); }} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-white rounded-lg p-1 shadow-sm border border-gray-200">
        {[
          { id: 'rules', label: 'Automation Rules', icon: Bot },
          { id: 'stock', label: 'Stock Reorder', icon: Package },
          { id: 'approvals', label: 'Approval Workflows', icon: CheckCircle }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-4 w-4" />
            <span className="font-medium text-sm">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Automation Rules Tab */}
      {activeTab === 'rules' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {automationRules.map((rule) => {
            const Icon = getRuleIcon(rule.type);
            return (
              <Card key={rule.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Icon className="h-5 w-5 text-gray-600" />
                      <div>
                        <CardTitle className="text-lg">{rule.name}</CardTitle>
                        <Badge className={getRuleTypeColor(rule.type)}>
                          {rule.type.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                    <Switch
                      checked={rule.isActive}
                      onCheckedChange={(checked) => handleToggleRule(rule.id, checked)}
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Conditions:</p>
                      {rule.conditions.map((condition, idx) => (
                        <p key={idx} className="text-sm text-gray-600">
                          {condition.field} {condition.operator.replace('_', ' ')} {condition.value}
                        </p>
                      ))}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Actions:</p>
                      {rule.actions.map((action, idx) => (
                        <p key={idx} className="text-sm text-gray-600">
                          {action.type.replace('_', ' ')}
                        </p>
                      ))}
                    </div>
                    {rule.schedule && (
                      <div>
                        <p className="text-sm font-medium text-gray-700">Schedule:</p>
                        <p className="text-sm text-gray-600">
                          {rule.schedule.frequency} at {rule.schedule.time}
                          {rule.schedule.dayOfWeek && ` (Day ${rule.schedule.dayOfWeek})`}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Stock Reorder Tab */}
      {activeTab === 'stock' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Automatic Stock Reordering</CardTitle>
              <CardDescription>
                Set minimum stock levels and automatic reorder quantities for products
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stockRules.map((rule) => (
                  <div key={rule.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium">Product ID: {rule.productId}</p>
                      <p className="text-sm text-gray-600">
                        Reorder {rule.reorderQuantity} units when stock drops below {rule.minStockLevel}
                      </p>
                      {rule.supplierId && (
                        <p className="text-xs text-gray-500">Supplier: {rule.supplierId}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-3">
                      {rule.lastTriggered && (
                        <div className="text-right">
                          <p className="text-xs text-gray-500">Last triggered:</p>
                          <p className="text-xs text-gray-600">
                            {new Date(rule.lastTriggered).toLocaleDateString()}
                          </p>
                        </div>
                      )}
                      <Switch checked={rule.isActive} />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Approval Workflows Tab */}
      {activeTab === 'approvals' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Approval Workflows</CardTitle>
              <CardDescription>
                Define approval processes for large orders, transfers, and discounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {approvalWorkflows.map((workflow) => (
                  <div key={workflow.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium">{workflow.name}</p>
                      <p className="text-sm text-gray-600">
                        Requires approval for {workflow.type} over {workflow.threshold.toLocaleString()} Dh
                      </p>
                      <p className="text-xs text-gray-500">
                        {workflow.requiredApprovals} approval(s) required from {workflow.approvers.length} approvers
                      </p>
                    </div>
                    <div className="flex items-center gap-3">
                      <Badge variant={workflow.isActive ? "default" : "secondary"}>
                        {workflow.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <Button size="sm" variant="outline">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

// Create Rule Form Component
const CreateRuleForm = ({ onSuccess }: { onSuccess: () => void }) => {
  const [formData, setFormData] = useState({
    name: '',
    type: 'stock_reorder' as AutomationRule['type'],
    conditions: [{ field: 'stock', operator: 'less_than' as const, value: 20 }],
    actions: [{ type: 'reorder_stock' as const, parameters: { quantity: 100 } }]
  });
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createAutomationRule({
        ...formData,
        isActive: true
      });
      toast({
        title: "Rule Created",
        description: "Automation rule has been created successfully",
      });
      onSuccess();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create automation rule",
        variant: "destructive"
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Rule Name</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>
      
      <div>
        <Label htmlFor="type">Rule Type</Label>
        <Select value={formData.type} onValueChange={(value: AutomationRule['type']) => setFormData({ ...formData, type: value })}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="stock_reorder">Stock Reorder</SelectItem>
            <SelectItem value="email_notification">Email Notification</SelectItem>
            <SelectItem value="scheduled_report">Scheduled Report</SelectItem>
            <SelectItem value="approval_workflow">Approval Workflow</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end gap-3">
        <Button type="submit">Create Rule</Button>
      </div>
    </form>
  );
};

export default WorkflowAutomation;
