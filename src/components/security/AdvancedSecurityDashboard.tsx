import React, { useState, useEffect } from 'react';
import {
  Shield,
  AlertTriangle,
  Users,
  Activity,
  Lock,
  Eye,
  Clock,
  TrendingUp,
  Server,
  Database,
  Wifi,
  HardDrive,
  Cpu,
  RefreshCw
} from 'lucide-react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { AdvancedSecurityService, SecurityMetrics, SystemHealth } from '../../services/securityService';
import { SystemHealthService, SystemHealthOverview } from '../../services/systemHealthService';
import { AdvancedAuditService, ActivitySummary } from '../../services/auditService';

interface AdvancedSecurityDashboardProps {
  onNavigate?: (page: string) => void;
}

const AdvancedSecurityDashboard: React.FC<AdvancedSecurityDashboardProps> = ({ onNavigate }) => {
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealthOverview | null>(null);
  const [activitySummary, setActivitySummary] = useState<ActivitySummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    try {
      setLoading(true);
      const [metrics, health, activity] = await Promise.all([
        AdvancedSecurityService.getSecurityMetrics(),
        SystemHealthService.getSystemHealthOverview(),
        AdvancedAuditService.getActivitySummary()
      ]);

      setSecurityMetrics(metrics);
      setSystemHealth(health);
      setActivitySummary(activity);
    } catch (error) {
      console.error('Error loading security data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadSecurityData();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-blue-600 bg-blue-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Security Dashboard</h1>
          <p className="text-gray-600">System security monitoring and threat detection</p>
        </div>
        
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">System Status</p>
              <p className={`text-lg font-bold capitalize ${getStatusColor(systemHealth?.overallStatus || 'unknown').split(' ')[0]}`}>
                {systemHealth?.overallStatus || 'Unknown'}
              </p>
            </div>
            <div className={`h-12 w-12 rounded-lg flex items-center justify-center ${getStatusColor(systemHealth?.overallStatus || 'unknown')}`}>
              <Server className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Alerts</p>
              <p className="text-2xl font-bold text-red-600">{securityMetrics?.criticalAlerts || 0}</p>
            </div>
            <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Sessions</p>
              <p className="text-2xl font-bold text-blue-600">{securityMetrics?.activeSessions || 0}</p>
            </div>
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Failed Logins (24h)</p>
              <p className="text-2xl font-bold text-orange-600">{securityMetrics?.failedLogins || 0}</p>
            </div>
            <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Lock className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* System Health Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">System Performance</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Cpu className="h-5 w-5 text-blue-600" />
                <span className="text-gray-700">CPU Usage</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${systemHealth?.systemMetrics.cpuUsage || 0}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {systemHealth?.systemMetrics.cpuUsage?.toFixed(1) || 0}%
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <HardDrive className="h-5 w-5 text-green-600" />
                <span className="text-gray-700">Memory Usage</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full" 
                    style={{ width: `${systemHealth?.systemMetrics.memoryUsage || 0}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {systemHealth?.systemMetrics.memoryUsage?.toFixed(1) || 0}%
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Database className="h-5 w-5 text-purple-600" />
                <span className="text-gray-700">Disk Usage</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-purple-600 h-2 rounded-full" 
                    style={{ width: `${systemHealth?.systemMetrics.diskUsage || 0}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {systemHealth?.systemMetrics.diskUsage?.toFixed(1) || 0}%
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Wifi className="h-5 w-5 text-orange-600" />
                <span className="text-gray-700">Response Time</span>
              </div>
              <span className="text-sm font-medium text-gray-900">
                {systemHealth?.systemMetrics.responseTime || 0}ms
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Database Health</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">Status</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(systemHealth?.databaseHealth.status || 'unknown')}`}>
                {systemHealth?.databaseHealth.status || 'Unknown'}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700">Connections</span>
              <span className="text-sm font-medium text-gray-900">
                {systemHealth?.databaseHealth.connectionCount || 0} / {systemHealth?.databaseHealth.maxConnections || 100}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700">Avg Response Time</span>
              <span className="text-sm font-medium text-gray-900">
                {systemHealth?.databaseHealth.queryPerformance.averageResponseTime || 0}ms
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700">Slow Queries</span>
              <span className="text-sm font-medium text-gray-900">
                {systemHealth?.databaseHealth.queryPerformance.slowQueries || 0}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-700">Failed Queries</span>
              <span className="text-sm font-medium text-gray-900">
                {systemHealth?.databaseHealth.queryPerformance.failedQueries || 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Security Events and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Security Events</h3>
          <div className="space-y-3">
            {securityMetrics?.recentEvents.slice(0, 5).map((event, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${getSeverityColor(event.severity).split(' ')[1]}`}></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{event.eventType.replace('_', ' ')}</p>
                    <p className="text-xs text-gray-600">{event.userEmail}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-xs font-medium ${getSeverityColor(event.severity).split(' ')[0]}`}>
                    {event.severity}
                  </p>
                  <p className="text-xs text-gray-500">
                    {new Date(event.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Security Alerts</h3>
          <div className="space-y-3">
            {securityMetrics?.recentAlerts.slice(0, 5).map((alert, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <AlertTriangle className={`h-4 w-4 ${getSeverityColor(alert.severity).split(' ')[0]}`} />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{alert.title}</p>
                    <p className="text-xs text-gray-600">{alert.description}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-xs font-medium ${getSeverityColor(alert.severity).split(' ')[0]}`}>
                    {alert.severity}
                  </p>
                  <p className="text-xs text-gray-500">
                    {new Date(alert.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Activity Summary */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">{activitySummary?.totalActions || 0}</div>
            <div className="text-sm text-gray-600 mt-1">Total Actions</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">{activitySummary?.uniqueUsers || 0}</div>
            <div className="text-sm text-gray-600 mt-1">Active Users</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600">{activitySummary?.securityEvents || 0}</div>
            <div className="text-sm text-gray-600 mt-1">Security Events</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-red-600">{activitySummary?.failedActions || 0}</div>
            <div className="text-sm text-gray-600 mt-1">Failed Actions</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSecurityDashboard;
