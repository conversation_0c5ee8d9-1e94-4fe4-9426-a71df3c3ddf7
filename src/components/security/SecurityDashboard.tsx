
import { useState, useEffect } from 'react';
import { Shield, Users, FileText, Settings, Eye, EyeOff, Lock } from 'lucide-react';
import RoleManagement from './RoleManagement';
import AuditTrailViewer from './AuditTrailViewer';
import SecuritySettings from './SecuritySettings';
import PasswordManagement from './PasswordManagement';

interface SecurityDashboardProps {
  userRole?: string;
}

const SecurityDashboard = ({ userRole = 'admin' }: SecurityDashboardProps) => {
  const [activeTab, setActiveTab] = useState('password');

  const getTabsForRole = () => {
    if (userRole === 'client' || userRole === 'reseller') {
      return [
        { id: 'password', label: 'Password Management', icon: Lock }
      ];
    }

    // Admin and other roles get full access
    return [
      { id: 'roles', label: 'Role Management', icon: Users },
      { id: 'audit', label: 'Audit Trail', icon: FileText },
      { id: 'settings', label: 'Security Settings', icon: Settings }
    ];
  };

  const tabs = getTabsForRole();

  // Set default tab based on available tabs
  useEffect(() => {
    if (tabs.length > 0 && !tabs.find(tab => tab.id === activeTab)) {
      setActiveTab(tabs[0].id);
    }
  }, [userRole, tabs, activeTab]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Security Dashboard</h2>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm border border-gray-200 dark:border-gray-700">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
          >
            <tab.icon className="h-5 w-5" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'password' && <PasswordManagement />}
      {activeTab === 'roles' && <RoleManagement />}
      {activeTab === 'audit' && <AuditTrailViewer />}
      {activeTab === 'settings' && <SecuritySettings />}
    </div>
  );
};

export default SecurityDashboard;
