
import { useState, useEffect } from 'react';
import { Settings, Save } from 'lucide-react';
import { SecuritySettings as SecuritySettingsType } from '../../types/security';

const SecuritySettings = () => {
  const [settings, setSettings] = useState<SecuritySettingsType | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      // Mock security settings - in production this would come from the database
      const mockSettings: SecuritySettingsType = {
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: true,
          maxAge: 90
        },
        sessionSettings: {
          timeout: 30,
          maxConcurrentSessions: 3,
          requireReauth: true
        },
        twoFactorRequired: false,
        loginAttempts: {
          maxAttempts: 5,
          lockoutDuration: 15
        }
      };
      setSettings(mockSettings);
    } catch (error) {
      console.error('Error loading security settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    setSaving(true);
    try {
      // Mock save settings - in production this would call the security service
      console.log('Saving security settings:', settings);
      // Show success message
    } catch (error) {
      console.error('Error saving security settings:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading || !settings) {
    return <div className="text-center py-8">Loading settings...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Security Settings</h3>
          <p className="text-gray-600 dark:text-gray-300">Configure system-wide security policies</p>
        </div>
        <button
          onClick={handleSave}
          disabled={saving}
          className="bg-teal-600 text-white py-2 px-4 rounded-lg hover:bg-teal-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
        >
          <Save className="h-5 w-5" />
          <span>{saving ? 'Saving...' : 'Save Settings'}</span>
        </button>
      </div>

      {/* Password Policy */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Password Policy</h4>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Minimum Length
            </label>
            <input
              type="number"
              value={settings.passwordPolicy.minLength}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                passwordPolicy: { ...prev.passwordPolicy, minLength: parseInt(e.target.value) }
              } : null)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Maximum Age (days)
            </label>
            <input
              type="number"
              value={settings.passwordPolicy.maxAge}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                passwordPolicy: { ...prev.passwordPolicy, maxAge: parseInt(e.target.value) }
              } : null)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
        <div className="grid md:grid-cols-2 gap-4 mt-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={settings.passwordPolicy.requireUppercase}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                passwordPolicy: { ...prev.passwordPolicy, requireUppercase: e.target.checked }
              } : null)}
              className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">Require uppercase letters</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={settings.passwordPolicy.requireLowercase}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                passwordPolicy: { ...prev.passwordPolicy, requireLowercase: e.target.checked }
              } : null)}
              className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">Require lowercase letters</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={settings.passwordPolicy.requireNumbers}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                passwordPolicy: { ...prev.passwordPolicy, requireNumbers: e.target.checked }
              } : null)}
              className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">Require numbers</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={settings.passwordPolicy.requireSpecialChars}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                passwordPolicy: { ...prev.passwordPolicy, requireSpecialChars: e.target.checked }
              } : null)}
              className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">Require special characters</span>
          </label>
        </div>
      </div>

      {/* Session Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Session Settings</h4>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Session Timeout (minutes)
            </label>
            <input
              type="number"
              value={settings.sessionSettings.timeout}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                sessionSettings: { ...prev.sessionSettings, timeout: parseInt(e.target.value) }
              } : null)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Max Concurrent Sessions
            </label>
            <input
              type="number"
              value={settings.sessionSettings.maxConcurrentSessions}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                sessionSettings: { ...prev.sessionSettings, maxConcurrentSessions: parseInt(e.target.value) }
              } : null)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
      </div>

      {/* Login Security */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Login Security</h4>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Max Login Attempts
            </label>
            <input
              type="number"
              value={settings.loginAttempts.maxAttempts}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                loginAttempts: { ...prev.loginAttempts, maxAttempts: parseInt(e.target.value) }
              } : null)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Lockout Duration (minutes)
            </label>
            <input
              type="number"
              value={settings.loginAttempts.lockoutDuration}
              onChange={(e) => setSettings(prev => prev ? {
                ...prev,
                loginAttempts: { ...prev.loginAttempts, lockoutDuration: parseInt(e.target.value) }
              } : null)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
        <div className="mt-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Additional security settings can be configured here.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SecuritySettings;
