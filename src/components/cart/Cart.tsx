import { useState, useEffect } from 'react';
import { X, Plus, Minus, Trash2, ShoppingBag, Tag } from 'lucide-react';
import { validatePromoCode } from '../../services/promoCodeService';
import { PromoCode } from '../../types/promoCode';
import { createOrder } from '../../services/orderService';
import CheckoutModal from '../checkout/CheckoutModal';

interface CartItem {
  id: number;
  title: string;
  price: number;
  quantity: number;
  image: string;
  category: string;
}

interface CartProps {
  isOpen: boolean;
  onClose: () => void;
  items: CartItem[];
  onUpdateQuantity: (id: number, quantity: number) => void;
  onRemoveItem: (id: number) => void;
  onClearCart: () => void;
  userType?: string;
}

const Cart = ({ isOpen, onClose, items, onUpdateQuantity, onRemoveItem, onClearCart, userType = 'client' }: CartProps) => {
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState<PromoCode | null>(null);
  const [promoError, setPromoError] = useState('');
  const [isValidatingPromo, setIsValidatingPromo] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);

  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const deliveryFee = subtotal > 200 ? 0 : 25;
  const discountAmount = appliedPromo ? calculateDiscount(subtotal, appliedPromo) : 0;
  const total = subtotal + deliveryFee - discountAmount;

  function calculateDiscount(amount: number, promo: PromoCode): number {
    if (promo.type === 'percentage') {
      const discount = (amount * promo.value) / 100;
      return promo.maxDiscount ? Math.min(discount, promo.maxDiscount) : discount;
    } else {
      return promo.value;
    }
  }

  const handleApplyPromo = async () => {
    if (!promoCode.trim()) return;

    setIsValidatingPromo(true);
    setPromoError('');

    try {
      const validation = await validatePromoCode(promoCode, subtotal);
      if (validation.isValid) {
        setAppliedPromo(validation.promoCode!);
        setPromoError('');
      } else {
        setPromoError(validation.error || 'Invalid promo code');
        setAppliedPromo(null);
      }
    } catch (error) {
      setPromoError('Error validating promo code');
      setAppliedPromo(null);
    } finally {
      setIsValidatingPromo(false);
    }
  };

  const handleRemovePromo = () => {
    setAppliedPromo(null);
    setPromoCode('');
    setPromoError('');
  };

  const handleCheckout = async (orderData: any) => {
    try {
      console.log('Processing order:', orderData);
      
      // Create the order with proper structure
      const orderPayload = {
        customerId: 'current-user-id', // In real app, get from auth context
        customerName: orderData.customerName,
        customerEmail: orderData.customerEmail,
        items: items.map(item => ({
          id: item.id,
          title: item.title,
          category: item.category,
          price: item.price,
          quantity: item.quantity,
          image: item.image
        })),
        subtotal: subtotal,
        deliveryFee: deliveryFee,
        total: total - discountAmount,
        status: 'pending' as const,
        paymentMethod: orderData.paymentMethod === 'card' ? 'Credit Card' : 'Cash on Delivery',
        paymentStatus: 'pending' as const,
        deliveryAddress: `${orderData.address}, ${orderData.city}, ${orderData.postalCode}`,
        selectedBranch: 'Main Branch', // You can make this dynamic
        promoCode: appliedPromo?.code,
        notes: orderData.notes
      };

      const newOrder = await createOrder(orderPayload);
      console.log('Order created successfully:', newOrder);
      
      // Clear cart and reset promo
      onClearCart();
      setAppliedPromo(null);
      setPromoCode('');
      
      // Show success message (you can add a toast here)
      alert(`Order placed successfully! Order ID: ${newOrder.id}`);
      
    } catch (error) {
      console.error('Error creating order:', error);
      alert('Error placing order. Please try again.');
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50">
        <div className="bg-white w-full sm:w-96 sm:max-w-md h-full sm:h-auto sm:max-h-[90vh] sm:rounded-2xl shadow-2xl flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <ShoppingBag className="h-6 w-6 text-teal-600" />
              <h2 className="text-xl font-bold text-gray-900">Shopping Cart</h2>
              <span className="bg-teal-100 text-teal-800 text-sm font-medium px-2 py-1 rounded-full">
                {items.length}
              </span>
            </div>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-6">
            {items.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingBag className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-lg">Your cart is empty</p>
                <p className="text-gray-400">Add some products to get started</p>
              </div>
            ) : (
              <div className="space-y-4">
                {items.map(item => (
                  <div key={item.id} className="flex items-center space-x-4 bg-gray-50 p-4 rounded-lg">
                    <img src={item.image} alt={item.title} className="w-16 h-16 object-cover rounded-lg" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 text-sm">{item.title}</h3>
                      <p className="text-gray-500 text-xs">{item.category}</p>
                      <p className="text-teal-600 font-bold">{item.price.toFixed(2)} DH</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onUpdateQuantity(item.id, Math.max(0, item.quantity - 1))}
                        className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                      >
                        {item.quantity === 1 ? <Trash2 className="h-4 w-4" /> : <Minus className="h-4 w-4" />}
                      </button>
                      <span className="w-8 text-center font-medium">{item.quantity}</span>
                      <button
                        onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                        className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300"
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}

                {/* Promo Code Section */}
                {items.length > 0 && (
                  <div className="border-t pt-4">
                    <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                      <Tag className="h-4 w-4 mr-2" />
                      Promo Code
                    </h3>
                    {appliedPromo ? (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-green-800 font-medium text-sm">{appliedPromo.code}</p>
                            <p className="text-green-600 text-xs">{appliedPromo.description}</p>
                          </div>
                          <button
                            onClick={handleRemovePromo}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex space-x-2">
                        <input
                          type="text"
                          placeholder="Enter code"
                          value={promoCode}
                          onChange={(e) => setPromoCode(e.target.value)}
                          className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500"
                        />
                        <button
                          onClick={handleApplyPromo}
                          disabled={isValidatingPromo || !promoCode.trim()}
                          className="bg-teal-600 text-white px-3 py-2 text-sm rounded-lg hover:bg-teal-700 disabled:bg-gray-400"
                        >
                          {isValidatingPromo ? '...' : 'Apply'}
                        </button>
                      </div>
                    )}
                    {promoError && (
                      <p className="text-red-600 text-xs mt-2">{promoError}</p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer with totals and checkout */}
          {items.length > 0 && (
            <div className="border-t p-6 space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>{subtotal.toFixed(2)} DH</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Delivery</span>
                  <span>{deliveryFee === 0 ? 'Free' : `${deliveryFee.toFixed(2)} DH`}</span>
                </div>
                {discountAmount > 0 && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount</span>
                    <span>-{discountAmount.toFixed(2)} DH</span>
                  </div>
                )}
                <div className="border-t pt-2 flex justify-between font-bold">
                  <span>Total</span>
                  <span>{total.toFixed(2)} DH</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <button
                  onClick={() => setShowCheckout(true)}
                  className="w-full bg-teal-600 text-white py-3 px-4 rounded-lg hover:bg-teal-700 transition-colors"
                >
                  Proceed to Checkout
                </button>
                <button
                  onClick={onClearCart}
                  className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors text-sm"
                >
                  Clear Cart
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <CheckoutModal
        isOpen={showCheckout}
        onClose={() => setShowCheckout(false)}
        cartItems={items}
        subtotal={subtotal}
        deliveryFee={deliveryFee}
        discountAmount={discountAmount}
        appliedPromo={appliedPromo}
        onCheckout={handleCheckout}
      />
    </>
  );
};

export default Cart;
