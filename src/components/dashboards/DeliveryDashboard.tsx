
import { useState, useEffect } from 'react';
import { Truck, MapPin, Clock, CheckCircle, Package, Phone, Navigation, User, Key } from 'lucide-react';
import { liveDashboardService, DeliveryStats } from '../../services/liveDashboardService';
import ProfileManagement from '../profile/ProfileManagement';
import PasswordManagement from '../security/PasswordManagement';

interface DeliveryDashboardProps {
  user: any;
  onNavigateFromHeader?: (navigateFunction: (page: string) => void) => void;
}

const DeliveryDashboard = ({ user: initialUser, onNavigateFromHeader }: DeliveryDashboardProps) => {
  const [user, setUser] = useState(initialUser);

  // Initialize state from URL parameters and localStorage
  const getInitialTab = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlTab = urlParams.get('tab');
    const storedTab = localStorage.getItem('delivery_active_tab');
    return urlTab || storedTab || 'assigned';
  };

  const [activeTab, setActiveTab] = useState(getInitialTab());
  const [deliveryStats, setDeliveryStats] = useState<DeliveryStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);

  useEffect(() => {
    loadDeliveryStats();
  }, []);

  const loadDeliveryStats = async () => {
    try {
      setStatsLoading(true);
      const stats = await liveDashboardService.getDeliveryDashboardStats(user.id);
      setDeliveryStats(stats);
    } catch (error) {
      console.error('Error loading delivery stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  // Use live stats or fallback
  const displayStats = deliveryStats || {
    assignedOrders: 0,
    completedToday: 0,
    inTransit: 0,
    totalDistance: 0
  };

  const assignedOrders = [
    {
      id: 'ORD-001',
      customer: 'Ahmed Mansouri',
      phone: '+212 6 12 34 56 78',
      address: '123 Rue Mohammed V, Casablanca',
      items: 3,
      total: 245.50,
      status: 'assigned',
      priority: 'high',
      orderDate: '2024-06-14',
      deliveryDate: '2024-06-15'
    },
    {
      id: 'ORD-002',
      customer: 'Fatima Zahra',
      phone: '+212 6 87 65 43 21',
      address: '456 Boulevard Hassan II, Rabat',
      items: 5,
      total: 189.25,
      status: 'picked',
      priority: 'medium',
      orderDate: '2024-06-13',
      deliveryDate: '2024-06-15'
    },
    {
      id: 'ORD-003',
      customer: 'Mohamed Alami',
      phone: '+212 6 55 44 33 22',
      address: '789 Avenue des FAR, Marrakech',
      items: 2,
      total: 312.75,
      status: 'out_for_delivery',
      priority: 'low',
      orderDate: '2024-06-14',
      deliveryDate: '2024-06-15'
    }
  ];

  const [deliveryRoutes, setDeliveryRoutes] = useState([
    {
      id: 'route-1',
      name: 'Morning Route - Casablanca Center',
      orders: ['ORD-001', 'ORD-004', 'ORD-007'],
      estimatedTime: '3h 30min',
      distance: '25 km',
      status: 'active'
    },
    {
      id: 'route-2',
      name: 'Afternoon Route - Rabat Districts',
      orders: ['ORD-002', 'ORD-005'],
      estimatedTime: '2h 15min',
      distance: '18 km',
      status: 'planned'
    }
  ]);

  const updateOrderStatus = (orderId: string, newStatus: string) => {
    console.log(`Updating order ${orderId} to status: ${newStatus}`);
    // In a real app, this would update the backend
  };

  const handleUserUpdate = (updatedUser: any) => {
    setUser(updatedUser);
  };

  const handleNavigate = (page: string) => {
    setActiveTab(page);

    // Update URL parameters
    const url = new URL(window.location.href);
    url.searchParams.set('tab', page);

    // Update URL without page reload
    window.history.replaceState({}, '', url.toString());

    // Store in localStorage for persistence
    localStorage.setItem('delivery_active_tab', page);
  };

  // Provide navigation function to header
  useEffect(() => {
    if (onNavigateFromHeader) {
      onNavigateFromHeader(handleNavigate);
    }
  }, [onNavigateFromHeader]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned': return 'bg-blue-100 text-blue-800';
      case 'picked': return 'bg-yellow-100 text-yellow-800';
      case 'out_for_delivery': return 'bg-orange-100 text-orange-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'assigned': return 'Assigned';
      case 'picked': return 'Picked';
      case 'out_for_delivery': return 'Out for Delivery';
      case 'delivered': return 'Delivered';
      default: return status;
    }
  };

  const handleOptimizeRoute = (routeId: string) => {
    console.log(`Optimizing route: ${routeId}`);
    // Simulate route optimization
    alert('Route optimized successfully!');
  };

  const handleStartRoute = (routeId: string) => {
    console.log(`Starting route: ${routeId}`);
    setDeliveryRoutes(prev => prev.map(route => 
      route.id === routeId ? { ...route, status: 'active' } : route
    ));
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-8 text-white">
        <h2 className="text-3xl font-bold mb-2">Welcome back, {user.fullName}!</h2>
        <p className="text-lg opacity-90">Delivery Personnel Dashboard</p>
      </div>

      {/* Quick Stats */}
      {activeTab !== 'profile' && activeTab !== 'password' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Assigned Orders</p>
                <p className="text-2xl font-bold text-blue-600">{statsLoading ? '...' : displayStats.assignedOrders}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed Today</p>
                <p className="text-2xl font-bold text-green-600">{statsLoading ? '...' : displayStats.completedToday}</p>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">In Transit</p>
                <p className="text-2xl font-bold text-orange-600">{statsLoading ? '...' : displayStats.inTransit}</p>
              </div>
              <div className="bg-orange-100 p-3 rounded-lg">
                <Truck className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Distance Today</p>
                <p className="text-2xl font-bold text-purple-600">{statsLoading ? '...' : displayStats.totalDistance} km</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-lg">
                <Navigation className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-white rounded-lg p-1 shadow-sm border border-gray-200">
        {[
          { id: 'assigned', label: 'Assigned Orders', icon: Package },
          { id: 'history', label: 'Delivery History', icon: Clock },
          { id: 'route', label: 'Route Planning', icon: MapPin },
          { id: 'password', label: 'Password', icon: Key },
          { id: 'profile', label: 'Profile Settings', icon: User }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => handleNavigate(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-5 w-5" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Password Tab */}
      {activeTab === 'password' && (
        <PasswordManagement />
      )}

      {/* Profile Tab */}
      {activeTab === 'profile' && (
        <ProfileManagement user={user} onUserUpdate={handleUserUpdate} />
      )}

      {/* Assigned Orders Tab */}
      {activeTab === 'assigned' && (
        <div className="space-y-4">
          {assignedOrders.map(order => (
            <div key={order.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{order.id}</h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
                      {getStatusLabel(order.status)}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      order.priority === 'high' ? 'bg-red-100 text-red-800' :
                      order.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {order.priority} priority
                    </span>
                  </div>
                  <p className="text-gray-600 font-medium">{order.customer}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                    <span>Order: {new Date(order.orderDate).toLocaleDateString()}</span>
                    <span>Delivery: {new Date(order.deliveryDate).toLocaleDateString()}</span>
                  </div>
                </div>
                <p className="text-xl font-bold text-teal-600">{order.total} Dh</p>
              </div>

              <div className="grid md:grid-cols-2 gap-4 mb-4">
                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-600">{order.phone}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Package className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-600">{order.items} items</span>
                </div>
              </div>

              <div className="flex items-start space-x-3 mb-4">
                <MapPin className="h-5 w-5 text-gray-400 mt-1" />
                <p className="text-gray-600">{order.address}</p>
              </div>

              <div className="flex space-x-3">
                {order.status === 'assigned' && (
                  <button
                    onClick={() => updateOrderStatus(order.id, 'picked')}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Mark as Picked
                  </button>
                )}
                {order.status === 'picked' && (
                  <button
                    onClick={() => updateOrderStatus(order.id, 'out_for_delivery')}
                    className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    Out for Delivery
                  </button>
                )}
                {order.status === 'out_for_delivery' && (
                  <button
                    onClick={() => updateOrderStatus(order.id, 'delivered')}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Mark as Delivered
                  </button>
                )}
                <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                  <MapPin className="h-4 w-4" />
                  <span>Navigate</span>
                </button>
                <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                  <Phone className="h-4 w-4" />
                  <span>Call</span>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* History Tab */}
      {activeTab === 'history' && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Delivery History</h3>
          <div className="text-center py-12">
            <Clock className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">Delivery history</p>
            <p className="text-gray-400">View completed deliveries and performance</p>
          </div>
        </div>
      )}

      {/* Route Planning Tab */}
      {activeTab === 'route' && (
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Route Planning</h3>
            
            <div className="grid gap-6">
              {deliveryRoutes.map(route => (
                <div key={route.id} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900">{route.name}</h4>
                      <p className="text-gray-600">{route.orders.length} orders</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      route.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                    }`}>
                      {route.status}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{route.estimatedTime}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Navigation className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{route.distance}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Package className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{route.orders.join(', ')}</span>
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={() => handleOptimizeRoute(route.id)}
                      className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition-colors"
                    >
                      Optimize Route
                    </button>
                    {route.status !== 'active' && (
                      <button
                        onClick={() => handleStartRoute(route.id)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        Start Route
                      </button>
                    )}
                    <button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                      <MapPin className="h-4 w-4" />
                      <span>View Map</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Route Optimization Tips</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Routes are optimized based on distance and traffic patterns</li>
                <li>• High priority orders are scheduled first</li>
                <li>• Consider lunch breaks and fuel stops in your planning</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeliveryDashboard;
