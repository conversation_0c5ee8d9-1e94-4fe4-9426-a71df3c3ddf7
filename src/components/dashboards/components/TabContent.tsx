
import InventoryChart from '../../analytics/InventoryChart';
import BranchManagement from '../../branches/BranchManagement';
import ProductManagement from '../../inventory/ProductManagement';
import CategoryManagement from '../../inventory/CategoryManagement';
import ProductManagementNew from '../../management/ProductManagement';
import CategoryManagementNew from '../../management/CategoryManagement';
import BranchManagementNew from '../../management/BranchManagement';
import QuickStats from './QuickStats';
import LowStockAlert from './LowStockAlert';
import ReportsAndAnalytics from '../../analytics/ReportsAndAnalytics';
import WorkflowAutomation from '../../automation/WorkflowAutomation';
import IntegrationDashboard from '../../integrations/IntegrationDashboard';
import SystemAdministration from '../../system/SystemAdministration';
import AdvancedAnalyticsDashboard from '../../analytics/AdvancedAnalyticsDashboard';
import SecurityDashboard from '../../security/SecurityDashboard';
import AdminOverviewDashboard from './AdminOverviewDashboard';
import AdminUserManagement from './AdminUserManagement';
import AdminOrderManagement from './AdminOrderManagement';
import AdminNotificationCenter from './AdminNotificationCenter';
import ClientManagement from '../../clients/ClientManagement';
import AdminInvoiceManagement from './AdminInvoiceManagement';
import { LowStockAlert as LowStockAlertType } from '../../../types/inventory';
import { DashboardStats } from '../../../services/liveDashboardService';

interface TabContentProps {
  activeTab: string;
  lowStockAlerts?: LowStockAlertType[];
  onRefreshLowStock?: () => void;
  userRole?: string;
  user?: any;
  onNavigate?: (page: string) => void;
  dashboardStats?: DashboardStats | null;
  statsLoading?: boolean;
}

const TabContent = ({ activeTab, lowStockAlerts = [], onRefreshLowStock = () => {}, userRole = 'admin', user, onNavigate, dashboardStats, statsLoading = false }: TabContentProps) => {
  // Admin-specific content
  if (userRole === 'admin') {
    switch (activeTab) {
      case 'overview':
        return <AdminOverviewDashboard onNavigate={onNavigate} dashboardStats={dashboardStats} statsLoading={statsLoading} />;
      case 'users':
        return <AdminUserManagement />;
      case 'clients':
        return <ClientManagement currentUserId="USR-001" userRole="admin" />;
      case 'branches':
        return <BranchManagement />;
      case 'products':
        return <ProductManagement />;
      case 'categories':
        return <CategoryManagement />;
      case 'orders':
        return <AdminOrderManagement />;
      case 'customers':
        return (
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Management</h3>
            <p className="text-gray-600">Customer management features coming soon...</p>
          </div>
        );
      case 'analytics':
        return <ReportsAndAnalytics />;
      case 'advanced-analytics':
        return <AdvancedAnalyticsDashboard />;
      case 'automation':
        return <WorkflowAutomation />;
      case 'integrations':
        return <IntegrationDashboard />;
      case 'security':
        return <SecurityDashboard />;
      case 'system':
        return <SystemAdministration />;
      case 'notifications':
        return <AdminNotificationCenter />;
      case 'invoices':
        return <AdminInvoiceManagement />;
      case 'reports':
        return <AdvancedAnalyticsDashboard />;
      default:
        return <AdminOverviewDashboard />;
    }
  }

  // Manager and other roles content
  switch (activeTab) {
    case 'clients':
      return <ClientManagement currentUserId={user?.id || 'current-user'} userRole={userRole} />;
    case 'product-management':
      return <ProductManagementNew userType={userRole} userId={user?.id || 'current-user'} />;
    case 'category-management':
      return <CategoryManagementNew userType={userRole} userId={user?.id || 'current-user'} />;
    case 'branch-management':
      return <BranchManagementNew userType={userRole} userId={user?.id || 'current-user'} />;
    case 'inventory':
      return (
        <div className="space-y-6">
          {/* Redesigned Inventory Overview */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Inventory Overview</h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="text-lg font-medium text-gray-700 mb-4">Stock Movements (Last 5 Days)</h4>
                <InventoryChart
                  categoryData={[
                    { category: 'Writing Instruments', revenue: 10000, unitsSold: 200 },
                    { category: 'Paper & Notebooks', revenue: 8000, unitsSold: 120 },
                    { category: 'Office & Desk Accessories', revenue: 5000, unitsSold: 80 },
                    { category: 'Technology', revenue: 12000, unitsSold: 95 },
                    { category: 'Art & Craft Supplies', revenue: 3500, unitsSold: 65 }
                  ]}
                  stockMovements={[
                    { date: '2024-06-01', inbound: 50, outbound: 40 },
                    { date: '2024-06-02', inbound: 35, outbound: 60 },
                    { date: '2024-06-03', inbound: 75, outbound: 45 },
                    { date: '2024-06-04', inbound: 42, outbound: 55 },
                    { date: '2024-06-05', inbound: 68, outbound: 38 }
                  ]}
                  type="bar"
                />
              </div>
              
              <div>
                <h4 className="text-lg font-medium text-gray-700 mb-4">Revenue by Category</h4>
                <InventoryChart
                  categoryData={[
                    { category: 'Writing Instruments', revenue: 10000, unitsSold: 200 },
                    { category: 'Paper & Notebooks', revenue: 8000, unitsSold: 120 },
                    { category: 'Office & Desk Accessories', revenue: 5000, unitsSold: 80 },
                    { category: 'Technology', revenue: 12000, unitsSold: 95 },
                    { category: 'Art & Craft Supplies', revenue: 3500, unitsSold: 65 }
                  ]}
                  stockMovements={[
                    { date: '2024-06-01', inbound: 50, outbound: 40 },
                    { date: '2024-06-02', inbound: 35, outbound: 60 },
                    { date: '2024-06-03', inbound: 75, outbound: 45 },
                    { date: '2024-06-04', inbound: 42, outbound: 55 },
                    { date: '2024-06-05', inbound: 68, outbound: 38 }
                  ]}
                  type="pie"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <LowStockAlert lowStockAlerts={lowStockAlerts} onRefresh={onRefreshLowStock} />
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h4 className="text-lg font-medium text-gray-700 mb-4">Quick Actions</h4>
              <div className="space-y-3">
                <button className="w-full text-left px-4 py-3 bg-teal-50 text-teal-700 rounded-lg hover:bg-teal-100 transition-colors">
                  Add New Product
                </button>
                <button className="w-full text-left px-4 py-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors">
                  Update Stock Levels
                </button>
                <button className="w-full text-left px-4 py-3 bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 transition-colors">
                  Generate Stock Report
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    case 'products':
      return <ProductManagement />;
    case 'branches':
      return <BranchManagement />;
    case 'categories':
      return <CategoryManagement />;
    case 'orders':
      return (
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Management</h3>
          <p className="text-gray-600">Order management features coming soon...</p>
        </div>
      );
    case 'customers':
      return (
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Management</h3>
          <p className="text-gray-600">Customer management features coming soon...</p>
        </div>
      );
    case 'analytics':
      return <ReportsAndAnalytics />;
    case 'advanced-analytics':
      return <AdvancedAnalyticsDashboard />;
    case 'automation':
      return <WorkflowAutomation />;
    case 'integrations':
      return <IntegrationDashboard />;
    case 'security':
      return <SecurityDashboard />;
    case 'system':
      return <SystemAdministration />;
    case 'reports':
      return (
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Reports</h3>
          <p className="text-gray-600">Advanced reporting features coming soon...</p>
        </div>
      );
    default:
      return (
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Dashboard</h3>
          <p className="text-gray-600">Select a tab to view content.</p>
        </div>
      );
  }
};

export default TabContent;
