
interface DashboardHeaderProps {
  userFullName: string;
}

const DashboardHeader = ({ userFullName }: DashboardHeaderProps) => {
  return (
    <div className="bg-gradient-to-r from-teal-600 to-orange-500 rounded-2xl p-6 sm:p-8 text-white">
      <h2 className="text-2xl sm:text-3xl font-bold mb-2">Welcome back, {userFullName}!</h2>
      <p className="text-base sm:text-lg opacity-90">Store Manager Dashboard</p>
    </div>
  );
};

export default DashboardHeader;
