
import { BarChart3, Users, Package, ShoppingCart, User, Tag, Settings, FolderPlus, MapPin, UserCheck } from 'lucide-react';

interface NavigationTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  userRole: string;
}

const NavigationTabs = ({ activeTab, onTabChange, userRole }: NavigationTabsProps) => {
  const adminTabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'clients', label: 'Clients', icon: UserCheck },
    { id: 'products', label: 'Products', icon: Package },
    { id: 'orders', label: 'Orders', icon: ShoppingCart },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'profile', label: 'Profile', icon: User }
  ];

  const managerTabs = [
    { id: 'inventory', label: 'Inventory', icon: Package },
    { id: 'orders', label: 'Orders', icon: ShoppingCart },
    { id: 'clients', label: 'Clients', icon: User<PERSON>heck },
    { id: 'product-management', label: 'Products', icon: Settings },
    { id: 'category-management', label: 'Categories', icon: FolderPlus },
    { id: 'branch-management', label: 'Branches', icon: MapPin },
    { id: 'promo-codes', label: 'Promo Codes', icon: Tag },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'profile', label: 'Profile', icon: User }
  ];

  const deliveryTabs = [
    { id: 'tasks', label: 'Delivery Tasks', icon: ShoppingCart },
    { id: 'profile', label: 'Profile', icon: User }
  ];

  const clientTabs = [
    { id: 'shop', label: 'Shop', icon: Package },
    { id: 'orders', label: 'My Orders', icon: ShoppingCart },
    { id: 'profile', label: 'Profile', icon: User }
  ];

  const getTabs = () => {
    switch (userRole) {
      case 'admin':
        return adminTabs;
      case 'manager':
        return managerTabs;
      case 'delivery':
        return deliveryTabs;
      case 'client':
      case 'reseller':
        return clientTabs;
      default:
        return clientTabs;
    }
  };

  const tabs = getTabs();

  return (
    <div className="bg-gray-100 p-3 rounded-lg">
      {/* Desktop and Tablet View - Single Line */}
      <div className="hidden md:flex gap-2 overflow-x-auto scrollbar-hide">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-colors whitespace-nowrap text-sm font-medium flex-shrink-0 ${
              activeTab === tab.id
                ? 'bg-white text-teal-600 shadow-md border-2 border-teal-200'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
            }`}
          >
            <tab.icon className="h-4 w-4" />
            <span className="hidden lg:inline">{tab.label}</span>
            <span className="lg:hidden">{tab.label.split(' ')[0]}</span>
          </button>
        ))}
      </div>

      {/* Mobile View - Compact Grid */}
      <div className="md:hidden grid grid-cols-2 gap-2">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`flex items-center justify-center space-x-2 px-3 py-3 rounded-lg transition-colors text-sm font-medium ${
              activeTab === tab.id
                ? 'bg-white text-teal-600 shadow-md border-2 border-teal-200'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
            }`}
          >
            <tab.icon className="h-4 w-4" />
            <span className="text-xs">{tab.label.split(' ')[0]}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default NavigationTabs;
