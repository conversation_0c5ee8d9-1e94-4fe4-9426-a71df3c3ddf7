
import { Users, Package, BarChart3 } from 'lucide-react';
import { Card, CardContent } from '../../ui/card';

interface QuickActionsCardsProps {
  onAddUser: () => void;
  onAddProduct: () => void;
  onGenerateReport: () => void;
}

const QuickActionsCards = ({ onAddUser, onAddProduct, onGenerateReport }: QuickActionsCardsProps) => {
  return (
    <div>
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card 
          className="bg-gradient-to-br from-teal-50 to-teal-100 border-teal-200 hover:shadow-md transition-all duration-300 cursor-pointer hover:scale-105"
          onClick={onAddUser}
        >
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-teal-600 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-teal-800">Add New User</h3>
                <p className="text-sm text-teal-600">Create user account</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card 
          className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-md transition-all duration-300 cursor-pointer hover:scale-105"
          onClick={onAddProduct}
        >
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                <Package className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-purple-800">Add Product</h3>
                <p className="text-sm text-purple-600">Add new inventory</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card 
          className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-md transition-all duration-300 cursor-pointer hover:scale-105"
          onClick={onGenerateReport}
        >
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-orange-800">Generate Report</h3>
                <p className="text-sm text-orange-600">System analytics</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default QuickActionsCards;
