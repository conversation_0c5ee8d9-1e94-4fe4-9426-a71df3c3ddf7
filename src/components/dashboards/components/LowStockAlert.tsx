
import { Package, Plus } from 'lucide-react';
import { LowStockAlert as LowStockAlertType } from '../../../types/inventory';

interface LowStockAlertProps {
  lowStockAlerts: LowStockAlertType[];
  onRefresh: () => void;
}

const LowStockAlert = ({ lowStockAlerts, onRefresh }: LowStockAlertProps) => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h3 className="text-lg sm:text-xl font-bold text-gray-900">Low Stock Alert</h3>
        <button 
          onClick={onRefresh}
          className="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-4 py-2 rounded-lg hover:from-teal-700 hover:to-teal-800 transition-all duration-200 flex items-center space-x-2 w-full sm:w-auto justify-center"
        >
          <Plus className="h-4 w-4" />
          <span>Refresh</span>
        </button>
      </div>
      {lowStockAlerts.length === 0 ? (
        <div className="text-center py-8">
          <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">No low stock alerts</p>
        </div>
      ) : (
        <div className="space-y-3">
          {lowStockAlerts.map((alert) => (
            <div key={alert.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg gap-2">
              <div className="flex-1">
                <p className="font-medium text-gray-900">{alert.productTitle}</p>
                <p className="text-sm text-gray-600">{alert.category}</p>
              </div>
              <div className="text-left sm:text-right">
                <p className="text-sm text-red-600">Current: {alert.currentStock}</p>
                <p className="text-sm text-gray-500">Min: {alert.minStock}</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LowStockAlert;
