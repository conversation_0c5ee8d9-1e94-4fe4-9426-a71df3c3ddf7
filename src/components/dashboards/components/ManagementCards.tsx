
import { TrendingUp } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../ui/card';

interface ManagementCard {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  stats: string;
  action?: () => void;
  page?: string;
}

interface ManagementCardsProps {
  cards: ManagementCard[];
  onNavigate?: (page: string) => void;
}

const ManagementCards = ({ cards, onNavigate }: ManagementCardsProps) => {
  const handleCardClick = (card: ManagementCard) => {
    if (card.page && onNavigate) {
      onNavigate(card.page);
    } else if (card.action) {
      card.action();
    }
  };
  return (
    <div>
      <h2 className="text-2xl font-bold text-gray-900 mb-6">System Management</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {cards.map((card, index) => (
          <Card
            key={index}
            className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-2 hover:border-gray-300 hover:scale-105"
            onClick={() => handleCardClick(card)}
          >
            <CardHeader>
              <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${card.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                <card.icon className="h-6 w-6 text-white" />
              </div>
              <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-gray-700 transition-colors">
                {card.title}
              </CardTitle>
              <CardDescription className="text-gray-600">
                {card.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">{card.stats}</span>
                <TrendingUp className="h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ManagementCards;
