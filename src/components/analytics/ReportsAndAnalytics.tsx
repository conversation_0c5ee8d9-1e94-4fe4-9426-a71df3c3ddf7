
import { useState, useEffect } from 'react';
import { Bar<PERSON>hart3, <PERSON><PERSON><PERSON>, Users, TrendingUp, ShoppingCart, Package, DollarSign, Target } from 'lucide-react';
import { SalesReport, InventoryAnalytics, CustomerAnalytics, KPIMetrics, ReportPeriod } from '../../types/analytics';
import { getSalesReport, getInventoryAnalytics, getCustomerAnalytics, getKPIMetrics } from '../../services/analyticsService';
import KPICard from './KPICard';
import SalesChart from './SalesChart';
import InventoryChart from './InventoryChart';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';

const ReportsAndAnalytics = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [reportPeriod, setReportPeriod] = useState<ReportPeriod>('weekly');
  const [salesData, setSalesData] = useState<SalesReport | null>(null);
  const [inventoryData, setInventoryData] = useState<InventoryAnalytics | null>(null);
  const [customerData, setCustomerData] = useState<CustomerAnalytics | null>(null);
  const [kpiData, setKpiData] = useState<KPIMetrics | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAnalyticsData();
  }, [reportPeriod]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      const [sales, inventory, customers, kpis] = await Promise.all([
        getSalesReport(reportPeriod),
        getInventoryAnalytics(),
        getCustomerAnalytics(),
        getKPIMetrics()
      ]);
      setSalesData(sales);
      setInventoryData(inventory);
      setCustomerData(customers);
      setKpiData(kpis);
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold text-gray-900">Reports & Analytics</h2>
        <div className="flex items-center space-x-4">
          <select
            value={reportPeriod}
            onChange={(e) => setReportPeriod(e.target.value as ReportPeriod)}
            className="border border-gray-300 rounded-lg px-4 py-2"
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
          </select>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-white rounded-lg p-1 shadow-sm border border-gray-200">
        {[
          { id: 'overview', label: 'Overview', icon: BarChart3 },
          { id: 'sales', label: 'Sales', icon: DollarSign },
          { id: 'inventory', label: 'Inventory', icon: Package },
          { id: 'customers', label: 'Customers', icon: Users }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-5 w-5" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* KPI Cards */}
      {kpiData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          <KPICard
            title="Revenue"
            value={`${kpiData.revenue.current.toLocaleString()} Dh`}
            growth={kpiData.revenue.growth}
            icon={DollarSign}
            color="bg-green-500"
          />
          <KPICard
            title="Orders"
            value={kpiData.orders.current.toString()}
            growth={kpiData.orders.growth}
            icon={ShoppingCart}
            color="bg-blue-500"
          />
          <KPICard
            title="Customers"
            value={kpiData.customers.current.toString()}
            growth={kpiData.customers.growth}
            icon={Users}
            color="bg-purple-500"
          />
          <KPICard
            title="Avg Order Value"
            value={`${kpiData.averageOrderValue.current.toFixed(2)} Dh`}
            growth={kpiData.averageOrderValue.growth}
            icon={Target}
            color="bg-orange-500"
          />
          <KPICard
            title="Conversion Rate"
            value={`${kpiData.conversionRate.current}%`}
            growth={kpiData.conversionRate.growth}
            icon={TrendingUp}
            color="bg-teal-500"
          />
          <KPICard
            title="Inventory Turnover"
            value={kpiData.inventoryTurnover.current.toString()}
            growth={kpiData.inventoryTurnover.growth}
            icon={Package}
            color="bg-indigo-500"
          />
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'overview' && salesData && inventoryData && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Sales Trend</h3>
            <SalesChart data={salesData.dailySales} type="line" />
          </div>
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Category Performance</h3>
            <InventoryChart 
              categoryData={inventoryData.categoryPerformance} 
              stockMovements={inventoryData.stockMovements}
              type="pie" 
            />
          </div>
        </div>
      )}

      {activeTab === 'sales' && salesData && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Sales Performance</h3>
            <SalesChart data={salesData.dailySales} type="bar" />
          </div>
          
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Top Selling Products</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Units Sold</TableHead>
                  <TableHead>Revenue</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {salesData.topProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.sales}</TableCell>
                    <TableCell>{product.revenue.toFixed(2)} Dh</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {activeTab === 'inventory' && inventoryData && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Stock Movements</h3>
            <InventoryChart 
              categoryData={inventoryData.categoryPerformance} 
              stockMovements={inventoryData.stockMovements}
              type="bar" 
            />
          </div>
          
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Product Performance</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Units Sold</TableHead>
                  <TableHead>Turnover Rate</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {inventoryData.topSellingProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.unitsSold}</TableCell>
                    <TableCell>{product.turnoverRate.toFixed(1)}x</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {activeTab === 'customers' && customerData && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Customer Segments</h3>
              <div className="space-y-4">
                {customerData.customerSegments.map((segment) => (
                  <div key={segment.segment} className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-900">{segment.segment}</p>
                      <p className="text-sm text-gray-600">{segment.count} customers</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-teal-600">{segment.averageSpend.toFixed(2)} Dh</p>
                      <p className="text-sm text-gray-500">avg spend</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Top Customers</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Orders</TableHead>
                    <TableHead>Total Spent</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {customerData.topCustomers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell className="font-medium">{customer.name}</TableCell>
                      <TableCell>{customer.orderCount}</TableCell>
                      <TableCell>{customer.totalSpent.toFixed(2)} Dh</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportsAndAnalytics;
