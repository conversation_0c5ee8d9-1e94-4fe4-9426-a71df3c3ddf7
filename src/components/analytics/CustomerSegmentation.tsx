
import { useState, useEffect } from 'react';
import { Users, TrendingUp, DollarSign, ShoppingCart } from 'lucide-react';
import { CustomerSegment, CustomerLifetimeValue } from '../../types/advancedAnalytics';
import { getCustomerSegments, getCustomerLifetimeValue } from '../../services/advancedAnalyticsService';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '../ui/chart';
import { PieChart, Pie, Cell, ResponsiveContainer } from 'recharts';

const CustomerSegmentation = () => {
  const [segments, setSegments] = useState<CustomerSegment[]>([]);
  const [customerLTV, setCustomerLTV] = useState<CustomerLifetimeValue[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [segmentsData, ltvData] = await Promise.all([
        getCustomerSegments(),
        getCustomerLifetimeValue()
      ]);
      setSegments(segmentsData);
      setCustomerLTV(ltvData);
    } catch (error) {
      console.error('Error loading customer segmentation data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const chartConfig = {
    customers: {
      label: "Customers",
    },
  };

  const chartData = segments.map(segment => ({
    name: segment.name,
    value: segment.customerCount,
    color: segment.color
  }));

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Customer Segmentation & Lifetime Value</h2>
      </div>

      {/* Segment Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {segments.map((segment) => (
          <div key={segment.id} className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-lg" style={{ backgroundColor: `${segment.color}20` }}>
                <Users className="h-6 w-6" style={{ color: segment.color }} />
              </div>
              <span className="text-2xl font-bold text-gray-900">{segment.customerCount}</span>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">{segment.name}</h3>
            <p className="text-sm text-gray-600 mb-3">{segment.description}</p>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Revenue:</span>
                <span className="font-medium">{segment.totalRevenue.toLocaleString()} Dh</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Avg Order:</span>
                <span className="font-medium">{segment.averageOrderValue.toLocaleString()} Dh</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Customer Distribution Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Customer Distribution</h3>
          <ChartContainer config={chartConfig} className="h-[300px]">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                outerRadius={100}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <ChartTooltip content={<ChartTooltipContent />} />
            </PieChart>
          </ChartContainer>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <h3 className="text-xl font-bold text-gray-900 mb-4">Segment Performance</h3>
          <div className="space-y-4">
            {segments.map((segment) => (
              <div key={segment.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-4 h-4 rounded-full" style={{ backgroundColor: segment.color }}></div>
                  <span className="font-medium text-gray-900">{segment.name}</span>
                </div>
                <div className="text-right">
                  <p className="font-bold text-teal-600">{segment.totalRevenue.toLocaleString()} Dh</p>
                  <p className="text-sm text-gray-500">{segment.customerCount} customers</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Customer Lifetime Value Table */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
        <h3 className="text-xl font-bold text-gray-900 mb-4">Customer Lifetime Value Analysis</h3>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Customer</TableHead>
              <TableHead>Segment</TableHead>
              <TableHead>Total Revenue</TableHead>
              <TableHead>Orders</TableHead>
              <TableHead>Avg Order Value</TableHead>
              <TableHead>Predicted LTV</TableHead>
              <TableHead>Churn Risk</TableHead>
              <TableHead>Profit Margin</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {customerLTV.map((customer) => (
              <TableRow key={customer.customerId}>
                <TableCell className="font-medium">{customer.customerName}</TableCell>
                <TableCell>{customer.segment}</TableCell>
                <TableCell>{customer.totalRevenue.toFixed(2)} Dh</TableCell>
                <TableCell>{customer.totalOrders}</TableCell>
                <TableCell>{customer.averageOrderValue.toFixed(2)} Dh</TableCell>
                <TableCell className="font-semibold text-green-600">
                  {customer.predictedLifetimeValue.toFixed(2)} Dh
                </TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    customer.churnRisk === 'low' 
                      ? 'bg-green-100 text-green-800' 
                      : customer.churnRisk === 'medium'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {customer.churnRisk.toUpperCase()}
                  </span>
                </TableCell>
                <TableCell>{customer.profitMargin.toFixed(1)}%</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default CustomerSegmentation;
