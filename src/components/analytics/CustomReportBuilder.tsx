import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Download, 
  Calendar, 
  Filter, 
  BarChart3, 
  <PERSON><PERSON>hart, 
  <PERSON><PERSON><PERSON>,
  Play,
  Save,
  Settings
} from 'lucide-react';
import { AdvancedReportingService, ReportData } from '../../services/reportingService';

interface CustomReportBuilderProps {
  onNavigate?: (page: string) => void;
}

const CustomReportBuilder: React.FC<CustomReportBuilderProps> = ({ onNavigate }) => {
  const [reportType, setReportType] = useState<'sales' | 'inventory' | 'customers'>('sales');
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(false);
  const [parameters, setParameters] = useState({
    dateFrom: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    dateTo: new Date().toISOString().split('T')[0],
    branchId: '',
    customerId: '',
    categoryId: '',
    userType: 'all' as 'all' | 'client' | 'reseller',
    lowStockOnly: false,
    includeInactive: false,
    minTotalSpent: 0
  });

  const reportTypes = [
    { 
      id: 'sales' as const, 
      name: 'Sales Report', 
      description: 'Analyze sales performance, revenue trends, and order metrics',
      icon: BarChart3,
      color: 'blue'
    },
    { 
      id: 'inventory' as const, 
      name: 'Inventory Report', 
      description: 'Track stock levels, product performance, and inventory turnover',
      icon: PieChart,
      color: 'green'
    },
    { 
      id: 'customers' as const, 
      name: 'Customer Report', 
      description: 'Customer analytics, segmentation, and behavior insights',
      icon: LineChart,
      color: 'purple'
    }
  ];

  const generateReport = async () => {
    setLoading(true);
    try {
      let data: ReportData;
      
      switch (reportType) {
        case 'sales':
          data = await AdvancedReportingService.generateSalesReport({
            dateFrom: parameters.dateFrom,
            dateTo: parameters.dateTo,
            branchId: parameters.branchId || undefined,
            customerId: parameters.customerId || undefined
          });
          break;
        case 'inventory':
          data = await AdvancedReportingService.generateInventoryReport({
            categoryId: parameters.categoryId || undefined,
            lowStockOnly: parameters.lowStockOnly,
            includeInactive: parameters.includeInactive
          });
          break;
        case 'customers':
          data = await AdvancedReportingService.generateCustomerReport({
            userType: parameters.userType,
            registrationDateFrom: parameters.dateFrom,
            registrationDateTo: parameters.dateTo,
            minTotalSpent: parameters.minTotalSpent || undefined
          });
          break;
        default:
          throw new Error('Invalid report type');
      }
      
      setReportData(data);
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async (format: 'csv' | 'json') => {
    if (!reportData) return;
    
    try {
      const exportedData = await AdvancedReportingService.exportReport(reportData, format);
      
      const blob = new Blob([exportedData], { 
        type: format === 'csv' ? 'text/csv' : 'application/json' 
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${reportType}-report-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  const renderParameterForm = () => {
    const selectedReportType = reportTypes.find(rt => rt.id === reportType);
    
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className={`h-10 w-10 bg-${selectedReportType?.color}-100 rounded-lg flex items-center justify-center`}>
            {selectedReportType?.icon && <selectedReportType.icon className={`h-5 w-5 text-${selectedReportType.color}-600`} />}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{selectedReportType?.name}</h3>
            <p className="text-sm text-gray-600">{selectedReportType?.description}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Common Parameters */}
          {(reportType === 'sales' || reportType === 'customers') && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                <input
                  type="date"
                  value={parameters.dateFrom}
                  onChange={(e) => setParameters(prev => ({ ...prev, dateFrom: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                <input
                  type="date"
                  value={parameters.dateTo}
                  onChange={(e) => setParameters(prev => ({ ...prev, dateTo: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </>
          )}

          {/* Sales Report Parameters */}
          {reportType === 'sales' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Branch (Optional)</label>
                <input
                  type="text"
                  placeholder="Branch ID"
                  value={parameters.branchId}
                  onChange={(e) => setParameters(prev => ({ ...prev, branchId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Customer (Optional)</label>
                <input
                  type="text"
                  placeholder="Customer ID"
                  value={parameters.customerId}
                  onChange={(e) => setParameters(prev => ({ ...prev, customerId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </>
          )}

          {/* Inventory Report Parameters */}
          {reportType === 'inventory' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category (Optional)</label>
                <input
                  type="text"
                  placeholder="Category ID"
                  value={parameters.categoryId}
                  onChange={(e) => setParameters(prev => ({ ...prev, categoryId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={parameters.lowStockOnly}
                    onChange={(e) => setParameters(prev => ({ ...prev, lowStockOnly: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Low Stock Only</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={parameters.includeInactive}
                    onChange={(e) => setParameters(prev => ({ ...prev, includeInactive: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Include Inactive</span>
                </label>
              </div>
            </>
          )}

          {/* Customer Report Parameters */}
          {reportType === 'customers' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">User Type</label>
                <select
                  value={parameters.userType}
                  onChange={(e) => setParameters(prev => ({ ...prev, userType: e.target.value as 'all' | 'client' | 'reseller' }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Customers</option>
                  <option value="client">Clients Only</option>
                  <option value="reseller">Resellers Only</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Min Total Spent (MAD)</label>
                <input
                  type="number"
                  min="0"
                  value={parameters.minTotalSpent}
                  onChange={(e) => setParameters(prev => ({ ...prev, minTotalSpent: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </>
          )}
        </div>

        <div className="mt-6 flex justify-end">
          <button
            onClick={generateReport}
            disabled={loading}
            className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <Play className="h-4 w-4" />
            <span>{loading ? 'Generating...' : 'Generate Report'}</span>
          </button>
        </div>
      </div>
    );
  };

  const renderReportResults = () => {
    if (!reportData) return null;

    return (
      <div className="space-y-6">
        {/* Report Summary */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Report Summary</h3>
              <p className="text-sm text-gray-600">Generated on {new Date(reportData.generatedAt).toLocaleString()}</p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => exportReport('csv')}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                <Download className="h-4 w-4" />
                <span>CSV</span>
              </button>
              <button
                onClick={() => exportReport('json')}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Download className="h-4 w-4" />
                <span>JSON</span>
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{reportData.summary.totalRecords}</div>
              <div className="text-sm text-gray-600">Total Records</div>
            </div>
            {reportData.summary.totalValue && (
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{reportData.summary.totalValue.toFixed(2)} MAD</div>
                <div className="text-sm text-gray-600">Total Value</div>
              </div>
            )}
            {reportData.summary.averageValue && (
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{reportData.summary.averageValue.toFixed(2)} MAD</div>
                <div className="text-sm text-gray-600">Average Value</div>
              </div>
            )}
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{Object.keys(reportData.summary.keyMetrics).length}</div>
              <div className="text-sm text-gray-600">Key Metrics</div>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="mt-6">
            <h4 className="text-md font-semibold text-gray-900 mb-3">Key Metrics</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(reportData.summary.keyMetrics).map(([key, value]) => (
                <div key={key} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm font-medium text-gray-700">{key}</span>
                  <span className="text-sm font-bold text-gray-900">{value}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Data Table Preview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Data Preview</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {reportData.data.length > 0 && Object.keys(reportData.data[0]).slice(0, 6).map((key) => (
                    <th key={key} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {key.replace(/_/g, ' ')}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.data.slice(0, 10).map((row, index) => (
                  <tr key={index}>
                    {Object.values(row).slice(0, 6).map((value, cellIndex) => (
                      <td key={cellIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {reportData.data.length > 10 && (
            <div className="mt-4 text-center text-sm text-gray-600">
              Showing 10 of {reportData.data.length} records. Export for full data.
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Custom Report Builder</h1>
        <p className="text-gray-600">Create and export custom reports with advanced filtering</p>
      </div>

      {/* Report Type Selection */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {reportTypes.map((type) => (
          <button
            key={type.id}
            onClick={() => setReportType(type.id)}
            className={`p-6 rounded-xl border-2 transition-all duration-200 text-left ${
              reportType === type.id
                ? `border-${type.color}-500 bg-${type.color}-50`
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
          >
            <div className={`h-12 w-12 bg-${type.color}-100 rounded-lg flex items-center justify-center mb-4`}>
              <type.icon className={`h-6 w-6 text-${type.color}-600`} />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">{type.name}</h3>
            <p className="text-sm text-gray-600">{type.description}</p>
          </button>
        ))}
      </div>

      {/* Parameter Form */}
      {renderParameterForm()}

      {/* Report Results */}
      {renderReportResults()}
    </div>
  );
};

export default CustomReportBuilder;
