
import { useState, useEffect } from 'react';
import { TrendingUp, Package, MapPin, <PERSON><PERSON>hart as PieChartIcon } from 'lucide-react';
import { 
  ProfitMarginAnalysis as ProfitAnalysis, 
  RegionProfitability, 
  CategoryProfitability 
} from '../../types/advancedAnalytics';
import { 
  getProfitMarginAnalysis, 
  getRegionalProfitability, 
  getCategoryProfitability 
} from '../../services/advancedAnalyticsService';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '../ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

const ProfitMarginAnalysis = () => {
  const [profitData, setProfitData] = useState<ProfitAnalysis[]>([]);
  const [regionalData, setRegionalData] = useState<RegionProfitability[]>([]);
  const [categoryData, setCategoryData] = useState<CategoryProfitability[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('products');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [profit, regional, category] = await Promise.all([
        getProfitMarginAnalysis(),
        getRegionalProfitability(),
        getCategoryProfitability()
      ]);
      setProfitData(profit);
      setRegionalData(regional);
      setCategoryData(category);
    } catch (error) {
      console.error('Error loading profit margin data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const chartConfig = {
    margin: {
      label: "Margin %",
    },
    revenue: {
      label: "Revenue",
    },
  };

  const totalRevenue = profitData.reduce((sum, item) => sum + item.revenue, 0);
  const totalCost = profitData.reduce((sum, item) => sum + item.cost, 0);
  const totalProfit = totalRevenue - totalCost;
  const overallMargin = (totalProfit / totalRevenue) * 100;

  const regionColors = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444'];
  const categoryColors = ['#8B5CF6', '#06B6D4', '#10B981'];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Profit Margin Analysis</h2>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900">{totalRevenue.toLocaleString()} Dh</p>
            </div>
            <div className="bg-green-500 p-3 rounded-lg">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">Gross Profit: {totalProfit.toLocaleString()} Dh</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Overall Margin</p>
              <p className="text-2xl font-bold text-gray-900">{overallMargin.toFixed(1)}%</p>
            </div>
            <div className="bg-blue-500 p-3 rounded-lg">
              <PieChartIcon className="h-6 w-6 text-white" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">Cost: {totalCost.toLocaleString()} Dh</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Products Analyzed</p>
              <p className="text-2xl font-bold text-gray-900">{profitData.length}</p>
            </div>
            <div className="bg-purple-500 p-3 rounded-lg">
              <Package className="h-6 w-6 text-white" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">Across {regionalData.length} regions</span>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-white rounded-lg p-1 shadow-sm border border-gray-200">
        {[
          { id: 'products', label: 'Products', icon: Package },
          { id: 'regions', label: 'Regions', icon: MapPin },
          { id: 'categories', label: 'Categories', icon: PieChartIcon }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-md transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-gradient-to-r from-teal-600 to-teal-700 text-white shadow-md'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <tab.icon className="h-5 w-5" />
            <span className="font-medium">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'products' && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Product Profit Margins</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Region</TableHead>
                  <TableHead>Revenue</TableHead>
                  <TableHead>Cost</TableHead>
                  <TableHead>Gross Profit</TableHead>
                  <TableHead>Margin %</TableHead>
                  <TableHead>Units Sold</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {profitData.map((product) => (
                  <TableRow key={product.productId}>
                    <TableCell className="font-medium">{product.productName}</TableCell>
                    <TableCell>{product.category}</TableCell>
                    <TableCell>{product.region}</TableCell>
                    <TableCell>{product.revenue.toFixed(2)} Dh</TableCell>
                    <TableCell>{product.cost.toFixed(2)} Dh</TableCell>
                    <TableCell className="text-green-600 font-semibold">
                      {product.grossProfit.toFixed(2)} Dh
                    </TableCell>
                    <TableCell>
                      <span className={`font-semibold ${
                        product.grossMargin >= 35 ? 'text-green-600' : 
                        product.grossMargin >= 25 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {product.grossMargin.toFixed(1)}%
                      </span>
                    </TableCell>
                    <TableCell>{product.unitsSold}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {activeTab === 'regions' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Regional Revenue</h3>
              <ChartContainer config={chartConfig} className="h-[300px]">
                <BarChart data={regionalData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="region" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Bar dataKey="totalRevenue" fill="#8B5CF6" />
                </BarChart>
              </ChartContainer>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Regional Profit Distribution</h3>
              <ChartContainer config={chartConfig} className="h-[300px]">
                <PieChart>
                  <Pie
                    data={regionalData.map((region, index) => ({
                      name: region.region,
                      value: region.grossProfit,
                      color: regionColors[index % regionColors.length]
                    }))}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {regionalData.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={regionColors[index % regionColors.length]} />
                    ))}
                  </Pie>
                  <ChartTooltip content={<ChartTooltipContent />} />
                </PieChart>
              </ChartContainer>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Regional Performance</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Region</TableHead>
                  <TableHead>Revenue</TableHead>
                  <TableHead>Cost</TableHead>
                  <TableHead>Gross Profit</TableHead>
                  <TableHead>Margin %</TableHead>
                  <TableHead>Customers</TableHead>
                  <TableHead>Orders</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {regionalData.map((region) => (
                  <TableRow key={region.region}>
                    <TableCell className="font-medium">{region.region}</TableCell>
                    <TableCell>{region.totalRevenue.toLocaleString()} Dh</TableCell>
                    <TableCell>{region.totalCost.toLocaleString()} Dh</TableCell>
                    <TableCell className="text-green-600 font-semibold">
                      {region.grossProfit.toLocaleString()} Dh
                    </TableCell>
                    <TableCell>
                      <span className={`font-semibold ${
                        region.grossMargin >= 35 ? 'text-green-600' : 
                        region.grossMargin >= 25 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {region.grossMargin.toFixed(1)}%
                      </span>
                    </TableCell>
                    <TableCell>{region.customerCount}</TableCell>
                    <TableCell>{region.orderCount}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {activeTab === 'categories' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Category Revenue</h3>
              <ChartContainer config={chartConfig} className="h-[300px]">
                <BarChart data={categoryData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="category" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Bar dataKey="totalRevenue" fill="#06B6D4" />
                </BarChart>
              </ChartContainer>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Category Profit Distribution</h3>
              <ChartContainer config={chartConfig} className="h-[300px]">
                <PieChart>
                  <Pie
                    data={categoryData.map((category, index) => ({
                      name: category.category,
                      value: category.grossProfit,
                      color: categoryColors[index % categoryColors.length]
                    }))}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {categoryData.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={categoryColors[index % categoryColors.length]} />
                    ))}
                  </Pie>
                  <ChartTooltip content={<ChartTooltipContent />} />
                </PieChart>
              </ChartContainer>
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Category Performance</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category</TableHead>
                  <TableHead>Revenue</TableHead>
                  <TableHead>Cost</TableHead>
                  <TableHead>Gross Profit</TableHead>
                  <TableHead>Margin %</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead>Units Sold</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categoryData.map((category) => (
                  <TableRow key={category.category}>
                    <TableCell className="font-medium">{category.category}</TableCell>
                    <TableCell>{category.totalRevenue.toLocaleString()} Dh</TableCell>
                    <TableCell>{category.totalCost.toLocaleString()} Dh</TableCell>
                    <TableCell className="text-green-600 font-semibold">
                      {category.grossProfit.toLocaleString()} Dh
                    </TableCell>
                    <TableCell>
                      <span className={`font-semibold ${
                        category.grossMargin >= 35 ? 'text-green-600' : 
                        category.grossMargin >= 25 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {category.grossMargin.toFixed(1)}%
                      </span>
                    </TableCell>
                    <TableCell>{category.productCount}</TableCell>
                    <TableCell>{category.unitsSold}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfitMarginAnalysis;
