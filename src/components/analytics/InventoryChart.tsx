
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '../ui/chart';

interface InventoryChartProps {
  categoryData: Array<{
    category: string;
    revenue: number;
    unitsSold: number;
  }>;
  stockMovements: Array<{
    date: string;
    inbound: number;
    outbound: number;
  }>;
  type: 'pie' | 'bar';
}

const COLORS = ['#0ea5e9', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

const InventoryChart = ({ categoryData, stockMovements, type }: InventoryChartProps) => {
  const chartConfig = {
    revenue: {
      label: "Revenue (Dh)",
      color: "hsl(var(--chart-1))",
    },
    inbound: {
      label: "Inbound",
      color: "hsl(var(--chart-2))",
    },
    outbound: {
      label: "Outbound",
      color: "hsl(var(--chart-3))",
    },
  };

  const formattedMovements = stockMovements.map(item => ({
    ...item,
    date: new Date(item.date).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
  }));

  if (type === 'pie') {
    return (
      <ChartContainer config={chartConfig} className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={categoryData}
              cx="50%"
              cy="50%"
              outerRadius={100}
              fill="#8884d8"
              dataKey="revenue"
              label={({ category, percent }) => `${category} ${(percent * 100).toFixed(0)}%`}
            >
              {categoryData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <ChartTooltip content={<ChartTooltipContent />} />
          </PieChart>
        </ResponsiveContainer>
      </ChartContainer>
    );
  }

  return (
    <ChartContainer config={chartConfig} className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={formattedMovements}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="inbound" fill="var(--color-inbound)" radius={4} />
          <Bar dataKey="outbound" fill="var(--color-outbound)" radius={4} />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
};

export default InventoryChart;
