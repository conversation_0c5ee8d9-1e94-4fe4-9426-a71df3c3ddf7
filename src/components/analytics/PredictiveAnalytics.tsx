
import { useState, useEffect } from 'react';
import { TrendingUp, Package, AlertTriangle, Download, Calendar, Filter } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Badge } from '../ui/badge';
import { getDemandForecast, getProfitabilityAnalysis, generatePredictiveReport } from '../../services/predictiveAnalyticsService';
import { DemandForecast, ProfitabilityAnalysis } from '../../types/predictiveAnalytics';
import { useToast } from '../../hooks/use-toast';

const PredictiveAnalytics = () => {
  const [demandForecasts, setDemandForecasts] = useState<DemandForecast[]>([]);
  const [profitabilityData, setProfitabilityData] = useState<ProfitabilityAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [timeframe, setTimeframe] = useState<'week' | 'month' | 'quarter'>('month');
  const [reportGenerating, setReportGenerating] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadAnalyticsData();
  }, [timeframe]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      const [forecasts, profitability] = await Promise.all([
        getDemandForecast(timeframe),
        getProfitabilityAnalysis()
      ]);
      setDemandForecasts(forecasts);
      setProfitabilityData(profitability);
    } catch (error) {
      console.error('Error loading analytics data:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateReport = async (type: 'demand' | 'profitability' | 'inventory', format: 'pdf' | 'excel' | 'csv') => {
    setReportGenerating(`${type}-${format}`);
    try {
      const report = await generatePredictiveReport(type, format);
      toast({
        title: "Report Generated",
        description: `Your ${type} report is ready for download`,
      });
      console.log('Report ready:', report);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate report",
        variant: "destructive"
      });
    } finally {
      setReportGenerating(null);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getTrendColor = (direction: string) => {
    if (direction === 'increasing') return 'text-green-600';
    if (direction === 'decreasing') return 'text-red-600';
    return 'text-gray-600';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Predictive Analytics</h2>
          <p className="text-gray-600">AI-powered demand forecasting and profitability analysis</p>
        </div>
        <div className="flex items-center gap-3">
          <Select value={timeframe} onValueChange={(value: 'week' | 'month' | 'quarter') => setTimeframe(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Week</SelectItem>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="quarter">Quarter</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={loadAnalyticsData} variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Demand Forecasting */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Demand Forecasting
          </CardTitle>
          <CardDescription>
            AI predictions for product demand based on historical data and trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {demandForecasts.map((forecast) => (
              <div key={forecast.productId} className="border rounded-lg p-4 space-y-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-semibold">{forecast.productName}</h4>
                    <p className="text-sm text-gray-600">{forecast.category}</p>
                  </div>
                  <Badge className={getConfidenceColor(forecast.confidence)}>
                    {Math.round(forecast.confidence * 100)}% confidence
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-xs text-gray-500">Current Stock</p>
                    <p className="text-lg font-semibold">{forecast.currentStock}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Predicted Demand ({timeframe})</p>
                    <p className="text-lg font-semibold">
                      {timeframe === 'week' ? forecast.predictedDemand.nextWeek :
                       timeframe === 'month' ? forecast.predictedDemand.nextMonth :
                       forecast.predictedDemand.nextQuarter}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Trend:</span>
                    <span className={`font-medium ${getTrendColor(forecast.trends.direction)}`}>
                      {forecast.trends.direction} ({forecast.trends.changeRate.toFixed(1)}%)
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Seasonality:</span>
                    <span className="font-medium">{forecast.seasonality.pattern}</span>
                  </div>
                </div>

                {forecast.currentStock < (timeframe === 'week' ? forecast.predictedDemand.nextWeek : 
                                         timeframe === 'month' ? forecast.predictedDemand.nextMonth :
                                         forecast.predictedDemand.nextQuarter) && (
                  <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <span className="text-sm text-red-800">Stock may be insufficient for predicted demand</span>
                  </div>
                )}

                <div className="space-y-1">
                  <p className="text-xs font-medium text-gray-700">Recommendations:</p>
                  {forecast.recommendations.slice(0, 2).map((rec, idx) => (
                    <p key={idx} className="text-xs text-gray-600">• {rec}</p>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Profitability Analysis */}
      {profitabilityData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Profitability Analysis
            </CardTitle>
            <CardDescription>
              Comprehensive profit analysis by product, branch, and category
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-600">Total Revenue</p>
                <p className="text-2xl font-bold text-blue-900">{profitabilityData.totalRevenue.toLocaleString()} Dh</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-sm text-green-600">Gross Profit</p>
                <p className="text-2xl font-bold text-green-900">{profitabilityData.grossProfit.toLocaleString()} Dh</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <p className="text-sm text-purple-600">Profit Margin</p>
                <p className="text-2xl font-bold text-purple-900">{profitabilityData.profitMargin.toFixed(1)}%</p>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <p className="text-sm text-orange-600">Total Cost</p>
                <p className="text-2xl font-bold text-orange-900">{profitabilityData.totalCost.toLocaleString()} Dh</p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Top Products */}
              <div>
                <h4 className="font-semibold mb-3">Top Products by Profit</h4>
                <div className="space-y-2">
                  {profitabilityData.byProduct.slice(0, 5).map((product) => (
                    <div key={product.productId} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <div>
                        <p className="text-sm font-medium">{product.productName}</p>
                        <p className="text-xs text-gray-600">{product.unitsSold} units sold</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold">{product.profit.toLocaleString()} Dh</p>
                        <p className="text-xs text-gray-600">{product.margin.toFixed(1)}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Branch Performance */}
              <div>
                <h4 className="font-semibold mb-3">Branch Performance</h4>
                <div className="space-y-2">
                  {profitabilityData.byBranch.map((branch) => (
                    <div key={branch.branchId} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <div>
                        <p className="text-sm font-medium">{branch.branchName}</p>
                        <p className="text-xs text-gray-600">Revenue: {branch.revenue.toLocaleString()} Dh</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold">{branch.profit.toLocaleString()} Dh</p>
                        <p className="text-xs text-gray-600">{branch.margin.toFixed(1)}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Category Analysis */}
              <div>
                <h4 className="font-semibold mb-3">Category Performance</h4>
                <div className="space-y-2">
                  {profitabilityData.byCategory.map((category) => (
                    <div key={category.category} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                      <div>
                        <p className="text-sm font-medium">{category.category}</p>
                        <p className="text-xs text-gray-600">{category.unitsSold} units</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold">{category.profit.toLocaleString()} Dh</p>
                        <p className="text-xs text-gray-600">{category.margin.toFixed(1)}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Custom Report Builder
          </CardTitle>
          <CardDescription>
            Generate detailed reports with export options
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {['demand', 'profitability', 'inventory'].map((reportType) => (
              <div key={reportType} className="border rounded-lg p-4">
                <h4 className="font-semibold mb-2 capitalize">{reportType} Report</h4>
                <p className="text-sm text-gray-600 mb-4">
                  {reportType === 'demand' && 'Detailed demand forecasting and trend analysis'}
                  {reportType === 'profitability' && 'Complete profitability breakdown and insights'}
                  {reportType === 'inventory' && 'Comprehensive inventory analysis and recommendations'}
                </p>
                <div className="flex gap-2">
                  {['pdf', 'excel', 'csv'].map((format) => (
                    <Button
                      key={format}
                      size="sm"
                      variant="outline"
                      onClick={() => handleGenerateReport(reportType as any, format as any)}
                      disabled={reportGenerating === `${reportType}-${format}`}
                    >
                      {reportGenerating === `${reportType}-${format}` ? 'Generating...' : format.toUpperCase()}
                    </Button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PredictiveAnalytics;
