/**
 * Simple component to test if AuthContext is working
 * This helps verify the React hooks dispatcher issue is fixed
 */

import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const AuthTest: React.FC = () => {
  const { user, isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="p-4 bg-blue-100 border border-blue-300 rounded">
        <p className="text-blue-800">🔄 Loading authentication...</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-green-100 border border-green-300 rounded">
      <h3 className="text-green-800 font-semibold mb-2">✅ AuthContext Working!</h3>
      <div className="text-sm text-green-700">
        <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
        <p><strong>User:</strong> {user ? user.fullName : 'None'}</p>
        <p><strong>Email:</strong> {user ? user.email : 'None'}</p>
        <p><strong>User Type:</strong> {user ? user.userType : 'None'}</p>
      </div>
    </div>
  );
};

export default AuthTest;
