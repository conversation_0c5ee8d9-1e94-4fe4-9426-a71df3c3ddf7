
import React, { useState } from 'react';
import { Filter, X, Calendar, DollarSign, User, Package } from 'lucide-react';
import { OrderFilters } from '../../types/orderTracking';

interface AdvancedOrderFiltersProps {
  filters: OrderFilters;
  onFiltersChange: (filters: OrderFilters) => void;
  onClearFilters: () => void;
}

const AdvancedOrderFilters = ({ filters, onFiltersChange, onClearFilters }: AdvancedOrderFiltersProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const statusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'confirmed', label: 'Confirmed' },
    { value: 'processing', label: 'Processing' },
    { value: 'shipped', label: 'Shipped' },
    { value: 'delivered', label: 'Delivered' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  const paymentStatusOptions = [
    { value: 'pending', label: 'Pending' },
    { value: 'paid', label: 'Paid' },
    { value: 'failed', label: 'Failed' },
    { value: 'refunded', label: 'Refunded' }
  ];

  const updateFilters = (updates: Partial<OrderFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key as keyof OrderFilters];
    return Array.isArray(value) ? value.length > 0 : !!value;
  });

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
          >
            <Filter className="h-4 w-4" />
            <span className="text-sm font-medium">Advanced Filters</span>
            {hasActiveFilters && (
              <span className="bg-teal-100 text-teal-800 text-xs px-2 py-1 rounded-full">
                Active
              </span>
            )}
          </button>
          
          {hasActiveFilters && (
            <button
              onClick={onClearFilters}
              className="text-sm text-gray-500 hover:text-gray-700 flex items-center"
            >
              <X className="h-3 w-3 mr-1" />
              Clear All
            </button>
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Search Term */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search Orders
              </label>
              <input
                type="text"
                value={filters.searchTerm || ''}
                onChange={(e) => updateFilters({ searchTerm: e.target.value })}
                placeholder="Order ID, customer name, etc."
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>

            {/* Order Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Order Status
              </label>
              <select
                multiple
                value={filters.status || []}
                onChange={(e) => updateFilters({ 
                  status: Array.from(e.target.selectedOptions, option => option.value)
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                size={3}
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Payment Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Status
              </label>
              <select
                multiple
                value={filters.paymentStatus || []}
                onChange={(e) => updateFilters({ 
                  paymentStatus: Array.from(e.target.selectedOptions, option => option.value)
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                size={3}
              >
                {paymentStatusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Calendar className="h-4 w-4 inline mr-1" />
                Date Range
              </label>
              <div className="space-y-2">
                <input
                  type="date"
                  value={filters.dateRange?.start || ''}
                  onChange={(e) => updateFilters({ 
                    dateRange: { 
                      ...filters.dateRange, 
                      start: e.target.value,
                      end: filters.dateRange?.end || ''
                    }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <input
                  type="date"
                  value={filters.dateRange?.end || ''}
                  onChange={(e) => updateFilters({ 
                    dateRange: { 
                      start: filters.dateRange?.start || '',
                      end: e.target.value
                    }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>

            {/* Amount Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <DollarSign className="h-4 w-4 inline mr-1" />
                Amount Range (Dh)
              </label>
              <div className="space-y-2">
                <input
                  type="number"
                  value={filters.minAmount || ''}
                  onChange={(e) => updateFilters({ minAmount: e.target.value ? Number(e.target.value) : undefined })}
                  placeholder="Min amount"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <input
                  type="number"
                  value={filters.maxAmount || ''}
                  onChange={(e) => updateFilters({ maxAmount: e.target.value ? Number(e.target.value) : undefined })}
                  placeholder="Max amount"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>

            {/* Customer ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <User className="h-4 w-4 inline mr-1" />
                Customer ID
              </label>
              <input
                type="text"
                value={filters.customerId || ''}
                onChange={(e) => updateFilters({ customerId: e.target.value })}
                placeholder="Enter customer ID"
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedOrderFilters;
