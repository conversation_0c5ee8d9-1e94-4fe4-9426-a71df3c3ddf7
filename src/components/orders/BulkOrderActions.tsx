
import React, { useState } from 'react';
import { Package, Download, Upload, Filter, Search } from 'lucide-react';
import { createBulkOperation } from '../../services/orderTrackingService';
import { useAuth } from '../../contexts/AuthContext';

interface BulkOrderActionsProps {
  selectedOrders: string[];
  onClearSelection: () => void;
}

const BulkOrderActions = ({ selectedOrders, onClearSelection }: BulkOrderActionsProps) => {
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleBulkStatusUpdate = async (status: string) => {
    if (!user || selectedOrders.length === 0) return;

    setIsProcessing(true);
    try {
      await createBulkOperation('status_update', selectedOrders, user.id);
      alert(`Bulk status update initiated for ${selectedOrders.length} orders`);
      onClearSelection();
    } catch (error) {
      console.error('Error in bulk operation:', error);
      alert('Error processing bulk operation');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkCancel = async () => {
    if (!user || selectedOrders.length === 0) return;
    
    if (!confirm(`Are you sure you want to cancel ${selectedOrders.length} orders?`)) {
      return;
    }

    setIsProcessing(true);
    try {
      await createBulkOperation('bulk_cancel', selectedOrders, user.id);
      alert(`Bulk cancellation initiated for ${selectedOrders.length} orders`);
      onClearSelection();
    } catch (error) {
      console.error('Error in bulk cancellation:', error);
      alert('Error processing bulk cancellation');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExportOrders = () => {
    // Mock export functionality
    const csvData = selectedOrders.map(id => `Order ID: ${id}`).join('\n');
    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'orders_export.csv';
    a.click();
    URL.revokeObjectURL(url);
  };

  if (selectedOrders.length === 0) {
    return null;
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center text-sm text-gray-600">
            <Package className="h-4 w-4 mr-2" />
            {selectedOrders.length} orders selected
          </div>
          
          <div className="flex items-center space-x-2">
            <select
              onChange={(e) => e.target.value && handleBulkStatusUpdate(e.target.value)}
              className="text-sm border border-gray-300 rounded px-3 py-1"
              disabled={isProcessing}
            >
              <option value="">Update Status</option>
              <option value="confirmed">Confirm Orders</option>
              <option value="processing">Mark as Processing</option>
              <option value="shipped">Mark as Shipped</option>
              <option value="delivered">Mark as Delivered</option>
            </select>

            <button
              onClick={handleBulkCancel}
              disabled={isProcessing}
              className="text-sm bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 disabled:opacity-50"
            >
              Cancel Orders
            </button>

            <button
              onClick={handleExportOrders}
              className="text-sm bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700 flex items-center"
            >
              <Download className="h-3 w-3 mr-1" />
              Export
            </button>
          </div>
        </div>

        <button
          onClick={onClearSelection}
          className="text-sm text-gray-500 hover:text-gray-700"
        >
          Clear Selection
        </button>
      </div>

      {isProcessing && (
        <div className="mt-2 text-sm text-blue-600">
          Processing bulk operation...
        </div>
      )}
    </div>
  );
};

export default BulkOrderActions;
