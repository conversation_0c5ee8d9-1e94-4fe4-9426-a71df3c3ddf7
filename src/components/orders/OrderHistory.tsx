
import { useState, useEffect } from 'react';
import { Package, Clock, CheckCircle, Truck, MapPin, CreditCard } from 'lucide-react';
import { Order } from '../../types/order';
import { getOrders } from '../../services/orderService';
import { getOrderStatusColor, getPaymentStatusColor, formatOrderDate } from '../../utils/orderUtils';
import OrderDetails from './OrderDetails';

interface OrderHistoryProps {
  customerId?: string;
  isAdmin?: boolean;
}

const OrderHistory = ({ customerId, isAdmin = false }: OrderHistoryProps) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadOrders();
  }, [customerId]);

  const loadOrders = async () => {
    try {
      const orderData = await getOrders(customerId);
      setOrders(orderData);
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="text-center py-12">
          <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
          <p className="text-gray-500">
            {customerId ? "You haven't placed any orders yet." : "No orders in the system yet."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-xl font-bold text-gray-900">
            {isAdmin ? 'All Orders' : 'Order History'}
          </h3>
          <p className="text-gray-600 mt-1">{orders.length} orders found</p>
        </div>

        <div className="divide-y divide-gray-200">
          {orders.map(order => (
            <div
              key={order.id}
              className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
              onClick={() => setSelectedOrder(order)}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className="bg-teal-100 p-2 rounded-lg">
                    <Package className="h-5 w-5 text-teal-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{order.id}</h4>
                    {isAdmin && (
                      <p className="text-sm text-gray-600">{order.customerName}</p>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900">{order.total.toFixed(2)} Dh</p>
                  <p className="text-sm text-gray-500">{formatOrderDate(order.createdAt)}</p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getOrderStatusColor(order.status)}`}>
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusColor(order.paymentStatus)}`}>
                    {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <MapPin className="h-4 w-4" />
                  <span>{order.selectedBranch}</span>
                </div>
              </div>

              <div className="mt-3 flex items-center space-x-6 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <Package className="h-4 w-4" />
                  <span>{order.items.length} items</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CreditCard className="h-4 w-4" />
                  <span>{order.paymentMethod}</span>
                </div>
                {order.estimatedDelivery && (
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>Est. {formatOrderDate(order.estimatedDelivery)}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {selectedOrder && (
        <OrderDetails
          order={selectedOrder}
          onClose={() => setSelectedOrder(null)}
          onUpdate={loadOrders}
          isAdmin={isAdmin}
        />
      )}
    </>
  );
};

export default OrderHistory;
