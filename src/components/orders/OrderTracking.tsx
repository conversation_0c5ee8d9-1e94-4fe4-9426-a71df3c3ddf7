
import React, { useState, useEffect } from 'react';
import { Package, MapPin, Clock, User, CheckCircle, AlertCircle } from 'lucide-react';
import { OrderTracking as OrderTrackingType, OrderTrackingStatus } from '../../types/orderTracking';
import { getOrderTracking } from '../../services/orderTrackingService';

interface OrderTrackingProps {
  orderId: string;
}

const OrderTracking = ({ orderId }: OrderTrackingProps) => {
  const [trackingHistory, setTrackingHistory] = useState<OrderTrackingType[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTracking();
  }, [orderId]);

  const loadTracking = async () => {
    try {
      const history = await getOrderTracking(orderId);
      setTrackingHistory(history);
    } catch (error) {
      console.error('Error loading tracking:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: OrderTrackingStatus) => {
    switch (status) {
      case 'order_placed':
      case 'payment_confirmed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'preparing':
      case 'ready_for_pickup':
        return <Package className="h-4 w-4 text-blue-500" />;
      case 'out_for_delivery':
        return <MapPin className="h-4 w-4 text-orange-500" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'cancelled':
      case 'returned':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: OrderTrackingStatus) => {
    switch (status) {
      case 'delivered':
        return 'text-green-600 bg-green-50';
      case 'cancelled':
      case 'returned':
        return 'text-red-600 bg-red-50';
      case 'out_for_delivery':
        return 'text-orange-600 bg-orange-50';
      case 'preparing':
      case 'ready_for_pickup':
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatStatus = (status: OrderTrackingStatus) => {
    return status.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex space-x-4">
                <div className="h-4 w-4 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <Package className="h-5 w-5 mr-2 text-teal-600" />
        Order Tracking
      </h3>

      <div className="space-y-4">
        {trackingHistory.length === 0 ? (
          <p className="text-gray-500 text-center py-4">No tracking information available</p>
        ) : (
          trackingHistory.map((tracking, index) => (
            <div key={tracking.id} className="flex items-start space-x-4">
              <div className="flex-shrink-0 mt-1">
                {getStatusIcon(tracking.status)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(tracking.status)}`}>
                    {formatStatus(tracking.status)}
                  </span>
                  <span className="text-sm text-gray-500">
                    {new Date(tracking.timestamp).toLocaleDateString()} at{' '}
                    {new Date(tracking.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <div className="mt-1 flex items-center text-sm text-gray-600">
                  <MapPin className="h-4 w-4 mr-1" />
                  {tracking.location}
                </div>
                {tracking.notes && (
                  <p className="mt-1 text-sm text-gray-600">{tracking.notes}</p>
                )}
                <div className="mt-1 flex items-center text-xs text-gray-500">
                  <User className="h-3 w-3 mr-1" />
                  Updated by {tracking.updatedBy}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default OrderTracking;
