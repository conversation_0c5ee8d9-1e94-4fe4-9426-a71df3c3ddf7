
import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Activity, Search, Filter, User, Settings, Shield, Eye, Clock, AlertTriangle } from 'lucide-react';

interface AuditLogsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AuditLogsModal = ({ isOpen, onClose }: AuditLogsModalProps) => {
  const [logs] = useState([
    { id: 1, user: 'Ahmed Mansouri', action: 'User Login', resource: 'Authentication', timestamp: '2024-06-15 10:30:00', ip: '***********', severity: 'Low' },
    { id: 2, user: 'Fatima Alami', action: 'Product Updated', resource: 'Inventory', timestamp: '2024-06-15 10:25:00', ip: '***********', severity: 'Medium' },
    { id: 3, user: '<PERSON>', action: 'Failed Login', resource: 'Authentication', timestamp: '2024-06-15 10:20:00', ip: '***********', severity: 'High' },
    { id: 4, user: 'System', action: 'Backup Created', resource: 'System', timestamp: '2024-06-15 10:15:00', ip: 'localhost', severity: 'Low' }
  ]);

  const totalLogs = logs.length;
  const highSeverityLogs = logs.filter(l => l.severity === 'High').length;
  const uniqueUsers = new Set(logs.map(l => l.user)).size;
  const recentLogs = logs.filter(l => {
    const logTime = new Date(l.timestamp);
    const now = new Date();
    const hoursDiff = (now.getTime() - logTime.getTime()) / (1000 * 60 * 60);
    return hoursDiff <= 24;
  }).length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Activity className="h-6 w-6 text-teal-600" />
            Audit Logs
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Header Actions */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">System Activity Logs</h3>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                Search
              </Button>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Total Logs</h4>
                  <p className="text-3xl font-bold">{totalLogs}</p>
                </div>
                <Activity className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-red-100 text-sm font-medium">High Severity</h4>
                  <p className="text-3xl font-bold">{highSeverityLogs}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-green-100 text-sm font-medium">Active Users</h4>
                  <p className="text-3xl font-bold">{uniqueUsers}</p>
                </div>
                <User className="h-8 w-8 text-green-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-purple-100 text-sm font-medium">Last 24h</h4>
                  <p className="text-3xl font-bold">{recentLogs}</p>
                </div>
                <Clock className="h-8 w-8 text-purple-200" />
              </div>
            </div>
          </div>
          
          {/* Audit Logs Table */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">User</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Action</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Resource</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Timestamp</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">IP Address</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Severity</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {logs.map(log => (
                    <tr key={log.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <User className="h-5 w-5 text-teal-600" />
                          </div>
                          <span className="font-medium text-gray-900">{log.user}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Settings className="h-4 w-4 text-blue-600" />
                          </div>
                          <span className="font-medium text-gray-900">{log.action}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-gray-600">{log.resource}</td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-1 text-gray-600">
                          <Clock className="h-4 w-4" />
                          <span className="text-sm">{log.timestamp}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-gray-600 font-mono text-sm">{log.ip}</td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          log.severity === 'High' ? 'bg-red-100 text-red-800' :
                          log.severity === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {log.severity}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuditLogsModal;
