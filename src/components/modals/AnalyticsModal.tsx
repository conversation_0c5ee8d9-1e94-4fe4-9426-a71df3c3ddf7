
import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { BarChart3, TrendingUp, DollarSign, Users, Package, Calendar, Activity, Target } from 'lucide-react';

interface AnalyticsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AnalyticsModal = ({ isOpen, onClose }: AnalyticsModalProps) => {
  const [selectedPeriod, setSelectedPeriod] = useState('30days');

  const analyticsData = [
    { metric: 'Total Revenue', value: '45,230 Dh', change: '+12.5%', trend: 'up' },
    { metric: 'Total Orders', value: '1,234', change: '+8.3%', trend: 'up' },
    { metric: 'Active Users', value: '892', change: '+15.2%', trend: 'up' },
    { metric: 'Product Sales', value: '2,456', change: '-2.1%', trend: 'down' }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <BarChart3 className="h-6 w-6 text-teal-600" />
            Analytics & Reports
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">Business Analytics</h3>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              >
                <option value="7days">Last 7 Days</option>
                <option value="30days">Last 30 Days</option>
                <option value="90days">Last 90 Days</option>
                <option value="1year">Last Year</option>
              </select>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-green-100 text-sm font-medium">Total Revenue</h4>
                  <p className="text-3xl font-bold">45,230 Dh</p>
                  <p className="text-green-200 text-sm mt-1">+12.5% from last month</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Total Orders</h4>
                  <p className="text-3xl font-bold">1,234</p>
                  <p className="text-blue-200 text-sm mt-1">+8.3% from last month</p>
                </div>
                <Package className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-purple-100 text-sm font-medium">Active Users</h4>
                  <p className="text-3xl font-bold">892</p>
                  <p className="text-purple-200 text-sm mt-1">+15.2% from last month</p>
                </div>
                <Users className="h-8 w-8 text-purple-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-orange-100 text-sm font-medium">Conversion Rate</h4>
                  <p className="text-3xl font-bold">3.2%</p>
                  <p className="text-orange-200 text-sm mt-1">+0.5% from last month</p>
                </div>
                <Target className="h-8 w-8 text-orange-200" />
              </div>
            </div>
          </div>
          
          {/* Charts and Data */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900">Sales Trends</h4>
                <TrendingUp className="h-5 w-5 text-teal-600" />
              </div>
              <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Chart visualization would go here</p>
                  <p className="text-sm text-gray-400 mt-1">Sales performance over time</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-900">Top Products</h4>
                <Package className="h-5 w-5 text-teal-600" />
              </div>
              <div className="space-y-3">
                {['Premium Pen Set', 'Notebook Bundle', 'Office Organizer', 'Desk Lamp'].map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 bg-teal-100 rounded-lg flex items-center justify-center">
                        <Package className="h-4 w-4 text-teal-600" />
                      </div>
                      <span className="font-medium text-gray-900">{product}</span>
                    </div>
                    <div className="text-right">
                      <span className="font-bold text-gray-900">{(250 - index * 50)} sold</span>
                      <p className="text-sm text-gray-500">{(4500 - index * 500)} Dh</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-semibold text-gray-900">Performance Metrics</h4>
              <Activity className="h-5 w-5 text-teal-600" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {analyticsData.map((data, index) => (
                <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-center mb-2">
                    {data.metric === 'Total Revenue' && <DollarSign className="h-6 w-6 text-green-600" />}
                    {data.metric === 'Total Orders' && <Package className="h-6 w-6 text-blue-600" />}
                    {data.metric === 'Active Users' && <Users className="h-6 w-6 text-purple-600" />}
                    {data.metric === 'Product Sales' && <BarChart3 className="h-6 w-6 text-orange-600" />}
                  </div>
                  <h5 className="text-sm font-medium text-gray-600 mb-1">{data.metric}</h5>
                  <p className="text-2xl font-bold text-gray-900">{data.value}</p>
                  <div className={`flex items-center justify-center gap-1 mt-2 text-sm ${
                    data.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <TrendingUp className={`h-4 w-4 ${data.trend === 'down' ? 'rotate-180' : ''}`} />
                    {data.change}
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AnalyticsModal;
