
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Building2, Plus, Edit, Trash2, MapPin, CheckCircle, XCircle, Activity } from 'lucide-react';

interface BranchManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const BranchManagementModal = ({ isOpen, onClose }: BranchManagementModalProps) => {
  const [branches] = useState([
    { id: 1, name: 'Casablanca Central', address: '123 Mohammed V Ave, Casablanca', manager: '<PERSON>', status: 'Active', phone: '+212 522-XXX-XXX' },
    { id: 2, name: '<PERSON><PERSON> Branch', address: '456 Hassan II St, Rabat', manager: 'Fatima Alami', status: 'Active', phone: '+212 537-XXX-XXX' },
    { id: 3, name: 'Marrakech Store', address: '789 Jemaa <PERSON>, <PERSON><PERSON><PERSON>', manager: '<PERSON>', status: 'Inactive', phone: '+212 524-XXX-XXX' }
  ]);

  const totalBranches = branches.length;
  const activeBranches = branches.filter(b => b.status === 'Active').length;
  const inactiveBranches = branches.filter(b => b.status === 'Inactive').length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Building2 className="h-6 w-6 text-teal-600" />
            Branch Management
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Header Actions */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">Store Branches</h3>
            <Button className="bg-teal-600 hover:bg-teal-700 text-white flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Branch
            </Button>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Total Branches</h4>
                  <p className="text-3xl font-bold">{totalBranches}</p>
                </div>
                <Building2 className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-green-100 text-sm font-medium">Active Branches</h4>
                  <p className="text-3xl font-bold">{activeBranches}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-red-100 text-sm font-medium">Inactive Branches</h4>
                  <p className="text-3xl font-bold">{inactiveBranches}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-purple-100 text-sm font-medium">Total Managers</h4>
                  <p className="text-3xl font-bold">{totalBranches}</p>
                </div>
                <Activity className="h-8 w-8 text-purple-200" />
              </div>
            </div>
          </div>
          
          {/* Branches Table */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Branch Name</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Address</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Manager</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Phone</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {branches.map(branch => (
                    <tr key={branch.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <Building2 className="h-5 w-5 text-teal-600" />
                          </div>
                          <span className="font-medium text-gray-900">{branch.name}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2 text-gray-600">
                          <MapPin className="h-4 w-4" />
                          <span className="max-w-xs truncate">{branch.address}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-gray-900">{branch.manager}</td>
                      <td className="px-6 py-4 text-gray-600">{branch.phone}</td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          branch.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {branch.status}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BranchManagementModal;
