
import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { BarChart3, Calendar, Download, FileText, TrendingUp, Activity } from 'lucide-react';

interface GenerateReportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const GenerateReportModal = ({ isOpen, onClose }: GenerateReportModalProps) => {
  const [reportConfig, setReportConfig] = useState({
    type: 'sales',
    dateRange: '30days',
    format: 'pdf'
  });

  const handleGenerate = () => {
    console.log('Generating report with config:', reportConfig);
    // Here you would typically generate and download the report
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <BarChart3 className="h-6 w-6 text-teal-600" />
            Generate System Report
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">Report Generation</h3>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Available Reports</h4>
                  <p className="text-3xl font-bold">4</p>
                </div>
                <FileText className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-green-100 text-sm font-medium">Generated Today</h4>
                  <p className="text-3xl font-bold">12</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-purple-100 text-sm font-medium">Data Sources</h4>
                  <p className="text-3xl font-bold">5</p>
                </div>
                <Activity className="h-8 w-8 text-purple-200" />
              </div>
            </div>
          </div>

          {/* Report Configuration */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h4 className="text-lg font-semibold mb-6">Report Configuration</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-2">Report Type</label>
                <select
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  value={reportConfig.type}
                  onChange={(e) => setReportConfig({...reportConfig, type: e.target.value})}
                >
                  <option value="sales">Sales Report</option>
                  <option value="inventory">Inventory Report</option>
                  <option value="users">User Activity Report</option>
                  <option value="financial">Financial Summary</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Date Range</label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <select
                    className="w-full pl-10 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    value={reportConfig.dateRange}
                    onChange={(e) => setReportConfig({...reportConfig, dateRange: e.target.value})}
                  >
                    <option value="7days">Last 7 Days</option>
                    <option value="30days">Last 30 Days</option>
                    <option value="90days">Last 90 Days</option>
                    <option value="1year">Last Year</option>
                    <option value="custom">Custom Range</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Export Format</label>
                <select
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  value={reportConfig.format}
                  onChange={(e) => setReportConfig({...reportConfig, format: e.target.value})}
                >
                  <option value="pdf">PDF Document</option>
                  <option value="excel">Excel Spreadsheet</option>
                  <option value="csv">CSV File</option>
                </select>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Report Preview</h4>
                <p className="text-sm text-gray-600">
                  This will generate a {reportConfig.type} report for the {reportConfig.dateRange.replace(/(\d+)/, '$1 ')} 
                  and export it as a {reportConfig.format.toUpperCase()} file.
                </p>
              </div>
            </div>
            
            <div className="flex gap-3 pt-6 mt-6 border-t">
              <Button type="button" variant="outline" onClick={onClose} className="flex-1">
                Cancel
              </Button>
              <Button onClick={handleGenerate} className="bg-teal-600 hover:bg-teal-700 text-white flex-1 flex items-center gap-2">
                <Download className="h-4 w-4" />
                Generate Report
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GenerateReportModal;
