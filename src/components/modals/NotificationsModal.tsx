
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Bell, AlertTriangle, Info, CheckCircle, X, Activity, Clock } from 'lucide-react';

interface NotificationsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationsModal = ({ isOpen, onClose }: NotificationsModalProps) => {
  const [notifications] = useState([
    { id: 1, type: 'warning', title: 'Low Stock Alert', message: 'Ballpoint Pen stock is running low (5 remaining)', time: '2 minutes ago', read: false },
    { id: 2, type: 'info', title: 'New Order Received', message: 'Order #ORD-2024-004 received from Ahmed Mansouri', time: '15 minutes ago', read: false },
    { id: 3, type: 'success', title: 'Backup Completed', message: 'Daily system backup completed successfully', time: '1 hour ago', read: true },
    { id: 4, type: 'error', title: 'Payment Failed', message: 'Payment processing failed for Order #ORD-2024-003', time: '2 hours ago', read: false }
  ]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'warning': return AlertTriangle;
      case 'error': return X;
      case 'success': return CheckCircle;
      default: return Info;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'warning': return 'yellow';
      case 'error': return 'red';
      case 'success': return 'green';
      default: return 'blue';
    }
  };

  const totalNotifications = notifications.length;
  const unreadNotifications = notifications.filter(n => !n.read).length;
  const criticalNotifications = notifications.filter(n => n.type === 'error' || n.type === 'warning').length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Bell className="h-6 w-6 text-teal-600" />
            Notifications Center
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Header Actions */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">Recent Notifications</h3>
            <Button variant="outline" size="sm">
              Mark All as Read
            </Button>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Total Notifications</h4>
                  <p className="text-3xl font-bold">{totalNotifications}</p>
                </div>
                <Bell className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-orange-100 text-sm font-medium">Unread</h4>
                  <p className="text-3xl font-bold">{unreadNotifications}</p>
                </div>
                <Activity className="h-8 w-8 text-orange-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-red-100 text-sm font-medium">Critical</h4>
                  <p className="text-3xl font-bold">{criticalNotifications}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-200" />
              </div>
            </div>
          </div>
          
          {/* Notifications List */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="space-y-3 p-6 max-h-96 overflow-y-auto">
              {notifications.map(notification => {
                const Icon = getNotificationIcon(notification.type);
                const color = getNotificationColor(notification.type);
                
                return (
                  <div key={notification.id} className={`border rounded-lg p-4 transition-colors ${
                    notification.read ? 'bg-gray-50' : 'bg-white border-l-4 border-l-' + color + '-500'
                  }`}>
                    <div className="flex items-start gap-3">
                      <div className="h-10 w-10 rounded-lg flex items-center justify-center bg-gray-100">
                        <Icon className={`h-5 w-5 ${
                          color === 'yellow' ? 'text-yellow-600' :
                          color === 'red' ? 'text-red-600' :
                          color === 'green' ? 'text-green-600' :
                          'text-blue-600'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <h4 className={`font-medium ${notification.read ? 'text-gray-600' : 'text-gray-900'}`}>
                          {notification.title}
                        </h4>
                        <p className={`text-sm mt-1 ${notification.read ? 'text-gray-500' : 'text-gray-700'}`}>
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-1 mt-2">
                          <Clock className="h-3 w-3 text-gray-400" />
                          <p className="text-xs text-gray-400">{notification.time}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {!notification.read && (
                          <Button variant="ghost" size="sm">
                            Mark Read
                          </Button>
                        )}
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            
            <div className="border-t p-4">
              <Button variant="outline" className="w-full">
                View All Notifications
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NotificationsModal;
