
import { useState } from 'react';
import { Dialog, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Tag, Plus, Edit, Trash2, Package, CheckCircle, XCircle, BarChart3 } from 'lucide-react';

interface CategoryManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CategoryManagementModal = ({ isOpen, onClose }: CategoryManagementModalProps) => {
  const [categories] = useState([
    { id: 1, name: 'Writing Instruments', description: 'Pens, pencils, markers', products: 45, status: 'Active' },
    { id: 2, name: 'Paper & Notebooks', description: 'Notebooks, paper, stationery', products: 32, status: 'Active' },
    { id: 3, name: 'Office Accessories', description: 'Desk organizers, supplies', products: 28, status: 'Active' },
    { id: 4, name: 'Technology', description: 'Calculators, electronics', products: 15, status: 'Inactive' }
  ]);

  const totalCategories = categories.length;
  const activeCategories = categories.filter(c => c.status === 'Active').length;
  const totalProducts = categories.reduce((sum, c) => sum + c.products, 0);
  const inactiveCategories = categories.filter(c => c.status === 'Inactive').length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Tag className="h-6 w-6 text-teal-600" />
            Category Management
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Header Actions */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">Product Categories</h3>
            <Button className="bg-teal-600 hover:bg-teal-700 text-white flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Category
            </Button>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-purple-100 text-sm font-medium">Total Categories</h4>
                  <p className="text-3xl font-bold">{totalCategories}</p>
                </div>
                <Tag className="h-8 w-8 text-purple-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-green-100 text-sm font-medium">Active Categories</h4>
                  <p className="text-3xl font-bold">{activeCategories}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Total Products</h4>
                  <p className="text-3xl font-bold">{totalProducts}</p>
                </div>
                <Package className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-red-100 text-sm font-medium">Inactive</h4>
                  <p className="text-3xl font-bold">{inactiveCategories}</p>
                </div>
                <XCircle className="h-8 w-8 text-red-200" />
              </div>
            </div>
          </div>
          
          {/* Categories Table */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Category Name</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Description</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Products</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {categories.map(category => (
                    <tr key={category.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <Tag className="h-5 w-5 text-teal-600" />
                          </div>
                          <span className="font-medium text-gray-900">{category.name}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-gray-600">{category.description}</td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <BarChart3 className="h-4 w-4 text-blue-600" />
                          </div>
                          <span className="font-medium text-gray-900">{category.products} items</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          category.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {category.status}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CategoryManagementModal;
