
import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { FileText, Plus, Download, Eye, Send, DollarSign, Clock, CheckCircle } from 'lucide-react';

interface InvoiceManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const InvoiceManagementModal = ({ isOpen, onClose }: InvoiceManagementModalProps) => {
  const [invoices] = useState([
    { id: 1, invoiceNumber: 'INV-2024-001', customer: '<PERSON>', amount: 146.75, status: 'Paid', date: '2024-06-15', dueDate: '2024-06-30' },
    { id: 2, invoiceNumber: 'INV-2024-002', customer: 'Fatima El Amrani', amount: 89.50, status: 'Pending', date: '2024-06-14', dueDate: '2024-06-29' },
    { id: 3, invoiceNumber: 'INV-2024-003', customer: '<PERSON>', amount: 234.20, status: 'Overdue', date: '2024-06-10', dueDate: '2024-06-25' }
  ]);

  const totalInvoices = invoices.length;
  const paidAmount = invoices.filter(i => i.status === 'Paid').reduce((sum, i) => sum + i.amount, 0);
  const pendingAmount = invoices.filter(i => i.status !== 'Paid').reduce((sum, i) => sum + i.amount, 0);
  const overdueInvoices = invoices.filter(i => i.status === 'Overdue').length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <FileText className="h-6 w-6 text-teal-600" />
            Invoice Management
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Header Actions */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">Customer Invoices</h3>
            <Button className="bg-teal-600 hover:bg-teal-700 text-white flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Invoice
            </Button>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Total Invoices</h4>
                  <p className="text-3xl font-bold">{totalInvoices}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-green-100 text-sm font-medium">Paid Amount</h4>
                  <p className="text-3xl font-bold">{paidAmount.toFixed(0)} Dh</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-orange-100 text-sm font-medium">Pending Amount</h4>
                  <p className="text-3xl font-bold">{pendingAmount.toFixed(0)} Dh</p>
                </div>
                <Clock className="h-8 w-8 text-orange-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-red-100 text-sm font-medium">Overdue</h4>
                  <p className="text-3xl font-bold">{overdueInvoices}</p>
                </div>
                <DollarSign className="h-8 w-8 text-red-200" />
              </div>
            </div>
          </div>
          
          {/* Invoices Table */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Invoice Number</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Customer</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Amount</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Date</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Due Date</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {invoices.map(invoice => (
                    <tr key={invoice.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <FileText className="h-5 w-5 text-teal-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{invoice.invoiceNumber}</p>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 font-medium text-gray-900">{invoice.customer}</td>
                      <td className="px-6 py-4 font-medium text-gray-900">{invoice.amount} Dh</td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                          invoice.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {invoice.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-gray-600">{invoice.date}</td>
                      <td className="px-6 py-4 text-gray-600">{invoice.dueDate}</td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                            <Send className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InvoiceManagementModal;
