
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Shield, Lock, Eye, AlertTriangle, CheckCircle, Activity, Users, Key } from 'lucide-react';

interface SecurityManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SecurityManagementModal = ({ isOpen, onClose }: SecurityManagementModalProps) => {
  const [securitySettings] = useState([
    { id: 1, setting: 'Two-Factor Authentication', status: 'Enabled', level: 'High', description: 'Required for all admin accounts' },
    { id: 2, setting: 'Password Policy', status: 'Active', level: 'Medium', description: 'Minimum 8 characters, special chars required' },
    { id: 3, setting: 'Session Timeout', status: 'Active', level: 'Medium', description: 'Auto logout after 30 minutes' },
    { id: 4, setting: 'IP Whitelist', status: 'Disabled', level: 'Low', description: 'Restrict access by IP address' }
  ]);

  const securityLogs = [
    { id: 1, event: 'Failed login attempt', user: '<EMAIL>', time: '2 minutes ago', severity: 'High' },
    { id: 2, event: 'Password changed', user: '<EMAIL>', time: '1 hour ago', severity: 'Medium' },
    { id: 3, event: 'New user created', user: '<EMAIL>', time: '3 hours ago', severity: 'Low' }
  ];

  const enabledSettings = securitySettings.filter(s => s.status === 'Enabled' || s.status === 'Active').length;
  const highSecuritySettings = securitySettings.filter(s => s.level === 'High').length;
  const recentEvents = securityLogs.filter(l => l.severity === 'High' || l.severity === 'Medium').length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Shield className="h-6 w-6 text-teal-600" />
            Security Management
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Security Status Alert */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <h4 className="font-semibold text-green-800">System Security Status: Secure</h4>
              <p className="text-sm text-green-700">All critical security measures are active</p>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-green-100 text-sm font-medium">Security Status</h4>
                  <p className="text-3xl font-bold">Secure</p>
                </div>
                <Shield className="h-8 w-8 text-green-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Active Settings</h4>
                  <p className="text-3xl font-bold">{enabledSettings}/4</p>
                </div>
                <Lock className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-red-100 text-sm font-medium">High Security</h4>
                  <p className="text-3xl font-bold">{highSecuritySettings}</p>
                </div>
                <Key className="h-8 w-8 text-red-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-orange-100 text-sm font-medium">Recent Events</h4>
                  <p className="text-3xl font-bold">{recentEvents}</p>
                </div>
                <Activity className="h-8 w-8 text-orange-200" />
              </div>
            </div>
          </div>
          
          {/* Security Settings */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Security Settings</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Security Setting</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Security Level</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Description</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {securitySettings.map(setting => (
                    <tr key={setting.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <Shield className="h-5 w-5 text-teal-600" />
                          </div>
                          <span className="font-medium text-gray-900">{setting.setting}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          setting.status === 'Enabled' || setting.status === 'Active' ? 
                          'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {setting.status}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          setting.level === 'High' ? 'bg-red-100 text-red-800' :
                          setting.level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {setting.level}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-gray-600">{setting.description}</td>
                      <td className="px-6 py-4">
                        <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                          <Lock className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Security Events */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Recent Security Events</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Event</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">User</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Time</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Severity</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {securityLogs.map(log => (
                    <tr key={log.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <Activity className="h-5 w-5 text-teal-600" />
                          </div>
                          <span className="font-medium text-gray-900">{log.event}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-gray-600">{log.user}</td>
                      <td className="px-6 py-4 text-gray-600">{log.time}</td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          log.severity === 'High' ? 'bg-red-100 text-red-800' :
                          log.severity === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {log.severity}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SecurityManagementModal;
