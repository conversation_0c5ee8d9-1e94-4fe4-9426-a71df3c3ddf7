
import { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Monitor, Cpu, HardDrive, Wifi, CheckCircle, AlertCircle, Activity, Zap } from 'lucide-react';

interface SystemHealthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SystemHealthModal = ({ isOpen, onClose }: SystemHealthModalProps) => {
  const [systemMetrics] = useState([
    { id: 1, component: 'CPU Usage', value: '23%', status: 'Healthy', icon: Cpu, color: 'green' },
    { id: 2, component: 'Memory Usage', value: '67%', status: 'Normal', icon: Monitor, color: 'yellow' },
    { id: 3, component: 'Disk Space', value: '45%', status: 'Healthy', icon: HardDrive, color: 'green' },
    { id: 4, component: 'Network', value: '99.9%', status: 'Healthy', icon: Wifi, color: 'green' }
  ]);

  const [serviceStatus] = useState([
    { service: 'Web Server', status: 'Running', uptime: '99.9%', lastCheck: '2 minutes ago' },
    { service: 'Database', status: 'Running', uptime: '99.8%', lastCheck: '1 minute ago' },
    { service: 'Cache Service', status: 'Running', uptime: '99.7%', lastCheck: '30 seconds ago' },
    { service: 'Backup Service', status: 'Running', uptime: '98.5%', lastCheck: '5 minutes ago' }
  ]);

  const healthyServices = serviceStatus.filter(s => s.status === 'Running').length;
  const avgUptime = (serviceStatus.reduce((sum, s) => sum + parseFloat(s.uptime), 0) / serviceStatus.length).toFixed(1);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Monitor className="h-6 w-6 text-teal-600" />
            System Health Monitor
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* System Status Alert */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <h4 className="font-semibold text-green-800">System Status: Healthy</h4>
              <p className="text-sm text-green-700">All systems are operating normally</p>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-green-100 text-sm font-medium">System Status</h4>
                  <p className="text-3xl font-bold">Healthy</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Services Online</h4>
                  <p className="text-3xl font-bold">{healthyServices}/4</p>
                </div>
                <Activity className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-purple-100 text-sm font-medium">Average Uptime</h4>
                  <p className="text-3xl font-bold">{avgUptime}%</p>
                </div>
                <Zap className="h-8 w-8 text-purple-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-orange-100 text-sm font-medium">Monitored Services</h4>
                  <p className="text-3xl font-bold">{serviceStatus.length}</p>
                </div>
                <Monitor className="h-8 w-8 text-orange-200" />
              </div>
            </div>
          </div>
          
          {/* System Metrics */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h3 className="text-lg font-semibold mb-4">System Metrics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {systemMetrics.map(metric => (
                <div key={metric.id} className="bg-gray-50 border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <metric.icon className={`h-6 w-6 ${
                      metric.color === 'green' ? 'text-green-600' : 
                      metric.color === 'yellow' ? 'text-yellow-600' : 'text-red-600'
                    }`} />
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      metric.color === 'green' ? 'bg-green-100 text-green-800' :
                      metric.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {metric.status}
                    </span>
                  </div>
                  <h4 className="font-medium text-gray-900">{metric.component}</h4>
                  <div className="text-2xl font-bold text-gray-900 mt-1">{metric.value}</div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Service Status */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Service Status</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Service</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Uptime</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Last Check</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {serviceStatus.map((service, index) => (
                    <tr key={index} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <Activity className="h-5 w-5 text-teal-600" />
                          </div>
                          <span className="font-medium text-gray-900">{service.service}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className="inline-flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                          <CheckCircle className="h-3 w-3" />
                          {service.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 font-medium text-gray-900">{service.uptime}</td>
                      <td className="px-6 py-4 text-gray-600">{service.lastCheck}</td>
                      <td className="px-6 py-4">
                        <Button variant="outline" size="sm">
                          Restart
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SystemHealthModal;
