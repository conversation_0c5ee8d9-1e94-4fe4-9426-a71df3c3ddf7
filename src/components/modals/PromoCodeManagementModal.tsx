
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Tag, Plus, Edit, Trash2, Calendar, Percent, Eye } from 'lucide-react';
import { PromoCode } from '../../types/promoCode';
import { getPromoCodes, deletePromoCode } from '../../services/promoCodeService';
import PromoCodeModal from '../promoCode/PromoCodeModal';

interface PromoCodeManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const PromoCodeManagementModal = ({ isOpen, onClose }: PromoCodeManagementModalProps) => {
  const [promoCodes, setPromoCodes] = useState<PromoCode[]>([]);
  const [loading, setLoading] = useState(false);
  const [promoModal, setPromoModal] = useState<{
    isOpen: boolean;
    mode: 'create' | 'edit' | 'view';
    promoCode?: PromoCode | null;
  }>({
    isOpen: false,
    mode: 'create',
    promoCode: null
  });

  useEffect(() => {
    if (isOpen) {
      loadPromoCodes();
    }
  }, [isOpen]);

  const loadPromoCodes = async () => {
    setLoading(true);
    try {
      const codes = await getPromoCodes();
      setPromoCodes(codes);
    } catch (error) {
      console.error('Error loading promo codes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this promo code?')) {
      try {
        await deletePromoCode(id);
        await loadPromoCodes();
        console.log('Promo code deleted successfully');
      } catch (error) {
        console.error('Error deleting promo code:', error);
      }
    }
  };

  const openPromoModal = (mode: 'create' | 'edit' | 'view', promoCode?: PromoCode) => {
    setPromoModal({
      isOpen: true,
      mode,
      promoCode: promoCode || null
    });
  };

  const closePromoModal = () => {
    setPromoModal({
      isOpen: false,
      mode: 'create',
      promoCode: null
    });
    loadPromoCodes();
  };

  const getUsagePercentage = (used: number, limit?: number) => {
    if (!limit) return 0;
    return Math.min((used / limit) * 100, 100);
  };

  const isExpired = (validUntil: string) => {
    return new Date() > new Date(validUntil);
  };

  const totalCodes = promoCodes.length;
  const activeCodes = promoCodes.filter(p => p.isActive && !isExpired(p.validUntil)).length;
  const totalUses = promoCodes.reduce((sum, p) => sum + p.usedCount, 0);
  const totalSavings = promoCodes.reduce((sum, p) => {
    if (p.type === 'percentage') {
      return sum + (p.usedCount * (p.maxDiscount || 50));
    }
    return sum + (p.usedCount * p.value);
  }, 0);

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-2xl">
              <Tag className="h-6 w-6 text-teal-600" />
              Promo Code Management
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Header Actions */}
            <div className="flex justify-between items-center">
              <h3 className="text-xl font-semibold text-gray-900">Promotional Codes</h3>
              <Button 
                onClick={() => openPromoModal('create')}
                className="bg-teal-600 hover:bg-teal-700 text-white flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Create Promo Code
              </Button>
            </div>
            
            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-blue-100 text-sm font-medium">Total Codes</h4>
                    <p className="text-3xl font-bold">{totalCodes}</p>
                  </div>
                  <Tag className="h-8 w-8 text-blue-200" />
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-green-100 text-sm font-medium">Active Codes</h4>
                    <p className="text-3xl font-bold">{activeCodes}</p>
                  </div>
                  <div className="h-8 w-8 bg-green-400 rounded-full flex items-center justify-center">
                    <div className="h-3 w-3 bg-white rounded-full"></div>
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-orange-100 text-sm font-medium">Total Uses</h4>
                    <p className="text-3xl font-bold">{totalUses}</p>
                  </div>
                  <div className="h-8 w-8 bg-orange-400 rounded-full flex items-center justify-center text-orange-800 font-bold">
                    #
                  </div>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-purple-100 text-sm font-medium">Total Savings</h4>
                    <p className="text-3xl font-bold">{totalSavings.toFixed(0)} Dh</p>
                  </div>
                  <div className="h-8 w-8 bg-purple-400 rounded-full flex items-center justify-center text-purple-800 font-bold">
                    $
                  </div>
                </div>
              </div>
            </div>
            
            {/* Promo Codes Table */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading promo codes...</p>
                </div>
              ) : promoCodes.length === 0 ? (
                <div className="p-8 text-center">
                  <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No promo codes found. Create your first one!</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Code</th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Type</th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Value</th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Usage</th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Valid Until</th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Status</th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {promoCodes.map(promo => (
                        <tr key={promo.id} className="hover:bg-gray-50 transition-colors">
                          <td className="px-6 py-4">
                            <div className="flex items-center gap-3">
                              <div className="h-10 w-10 bg-teal-100 rounded-lg flex items-center justify-center">
                                <Tag className="h-5 w-5 text-teal-600" />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">{promo.code}</p>
                                <p className="text-sm text-gray-500 truncate max-w-xs">{promo.description}</p>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                              promo.type === 'percentage' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                            }`}>
                              {promo.type === 'percentage' ? <Percent className="h-3 w-3" /> : <span>Dh</span>}
                              {promo.type === 'percentage' ? 'Percentage' : 'Fixed'}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <span className="font-medium text-gray-900">
                              {promo.type === 'percentage' ? `${promo.value}%` : `${promo.value} Dh`}
                            </span>
                            {promo.type === 'percentage' && promo.maxDiscount && (
                              <p className="text-sm text-gray-500">Max: {promo.maxDiscount} Dh</p>
                            )}
                          </td>
                          <td className="px-6 py-4">
                            <div className="space-y-1">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">
                                  {promo.usedCount}{promo.usageLimit ? ` / ${promo.usageLimit}` : ''}
                                </span>
                                {promo.usageLimit && (
                                  <span className="text-gray-500">
                                    {getUsagePercentage(promo.usedCount, promo.usageLimit).toFixed(0)}%
                                  </span>
                                )}
                              </div>
                              {promo.usageLimit && (
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-teal-600 h-2 rounded-full transition-all" 
                                    style={{width: `${getUsagePercentage(promo.usedCount, promo.usageLimit)}%`}}
                                  ></div>
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center gap-1 text-sm text-gray-600">
                              <Calendar className="h-4 w-4" />
                              {new Date(promo.validUntil).toLocaleDateString()}
                            </div>
                            {isExpired(promo.validUntil) && (
                              <p className="text-xs text-red-600 mt-1">Expired</p>
                            )}
                          </td>
                          <td className="px-6 py-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              promo.isActive && !isExpired(promo.validUntil)
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {promo.isActive && !isExpired(promo.validUntil) ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openPromoModal('view', promo)}
                                className="h-8 w-8 p-0"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openPromoModal('edit', promo)}
                                className="h-8 w-8 p-0"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDelete(promo.id)}
                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Promo Code Modal */}
      {promoModal.isOpen && (
        <PromoCodeModal
          mode={promoModal.mode}
          promoCode={promoModal.promoCode}
          onClose={closePromoModal}
        />
      )}
    </>
  );
};

export default PromoCodeManagementModal;
