
import { useState } from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { ShoppingCart, Search, Filter, Eye, Edit, Package, TrendingUp, Clock, CheckCircle } from 'lucide-react';

interface OrderManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const OrderManagementModal = ({ isOpen, onClose }: OrderManagementModalProps) => {
  const [orders] = useState([
    { id: 1, orderNumber: 'ORD-2024-001', customer: '<PERSON>', total: 146.75, status: 'Pending', date: '2024-06-15', items: 3 },
    { id: 2, orderNumber: 'ORD-2024-002', customer: 'Fatima El Amrani', total: 89.50, status: 'Shipped', date: '2024-06-14', items: 2 },
    { id: 3, orderNumber: 'ORD-2024-003', customer: '<PERSON>', total: 234.20, status: 'Delivered', date: '2024-06-13', items: 5 }
  ]);

  const totalOrders = orders.length;
  const pendingOrders = orders.filter(o => o.status === 'Pending').length;
  const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
  const completedOrders = orders.filter(o => o.status === 'Delivered').length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <ShoppingCart className="h-6 w-6 text-teal-600" />
            Order Management
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Header Actions */}
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold text-gray-900">Customer Orders</h3>
            <div className="flex gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filter
              </Button>
              <Button variant="outline" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                Search
              </Button>
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-blue-100 text-sm font-medium">Total Orders</h4>
                  <p className="text-3xl font-bold">{totalOrders}</p>
                </div>
                <ShoppingCart className="h-8 w-8 text-blue-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-orange-100 text-sm font-medium">Pending Orders</h4>
                  <p className="text-3xl font-bold">{pendingOrders}</p>
                </div>
                <Clock className="h-8 w-8 text-orange-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-green-100 text-sm font-medium">Total Revenue</h4>
                  <p className="text-3xl font-bold">{totalRevenue.toFixed(0)} Dh</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-200" />
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-purple-100 text-sm font-medium">Completed</h4>
                  <p className="text-3xl font-bold">{completedOrders}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-purple-200" />
              </div>
            </div>
          </div>
          
          {/* Orders Table */}
          <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Order Number</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Customer</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Items</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Total</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Date</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {orders.map(order => (
                    <tr key={order.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 bg-teal-100 rounded-lg flex items-center justify-center">
                            <ShoppingCart className="h-5 w-5 text-teal-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{order.orderNumber}</p>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 font-medium text-gray-900">{order.customer}</td>
                      <td className="px-6 py-4 text-gray-600">{order.items} items</td>
                      <td className="px-6 py-4 font-medium text-gray-900">{order.total} Dh</td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                          order.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                          order.status === 'Shipped' ? 'bg-blue-100 text-blue-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {order.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-gray-600">{order.date}</td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                            <Package className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OrderManagementModal;
