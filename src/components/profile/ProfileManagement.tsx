
import { useState, useEffect } from 'react';
import { User, Mail, Phone, MapPin, Building, Save, FileText, CreditCard, Lock, Eye, EyeOff, Shield, AlertTriangle } from 'lucide-react';
import { updateUser } from '../../services/userManagementService';
import { MOROCCAN_CITIES } from '../../constants/cities';
import { supabase } from '../../integrations/supabase/client';

interface ProfileManagementProps {
  user: any;
  onUserUpdate: (updatedUser: any) => void;
}

const ProfileManagement = ({ user, onUserUpdate }: ProfileManagementProps) => {
  const [loading, setLoading] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [showPasswordSection, setShowPasswordSection] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [formData, setFormData] = useState({
    // Map from user object (snake_case) to form fields (camelCase)
    fullName: user.full_name || user.fullName || '',
    email: user.email || '',
    phone: user.phone || '',
    city: user.city || '',
    company: user.company || '',
    jobTitle: user.job_title || user.jobTitle || '',
    bio: user.bio || '',
    // Company information
    isCompany: user.is_company || user.isCompany || false,
    companyName: user.company_name || user.companyName || '',
    iceNumber: user.ice_number || user.iceNumber || '',
    companyAddress: user.company_address || user.companyAddress || '',
    companyPhone: user.company_phone || user.companyPhone || '',
    companyCity: user.company_city || user.companyCity || '',
    companyEmail: user.company_email || user.companyEmail || '',
    taxId: user.tax_id || user.taxId || '',
    legalForm: user.legal_form || user.legalForm || ''
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Role-based visibility check
  const isAdminOrManager = user.userType === 'admin' || user.userType === 'manager';
  const shouldHideAccountType = isAdminOrManager;

  // Update form data when user prop changes (for data persistence)
  useEffect(() => {
    console.log('ProfileManagement: User prop changed, updating form data:', user);
    setFormData({
      // Map from user object (snake_case) to form fields (camelCase)
      fullName: user.full_name || user.fullName || '',
      email: user.email || '',
      phone: user.phone || '',
      city: user.city || '',
      company: user.company || '',
      jobTitle: user.job_title || user.jobTitle || '',
      bio: user.bio || '',
      // Company information
      isCompany: user.is_company || user.isCompany || false,
      companyName: user.company_name || user.companyName || '',
      iceNumber: user.ice_number || user.iceNumber || '',
      companyAddress: user.company_address || user.companyAddress || '',
      companyPhone: user.company_phone || user.companyPhone || '',
      companyCity: user.company_city || user.companyCity || '',
      companyEmail: user.company_email || user.companyEmail || '',
      taxId: user.tax_id || user.taxId || '',
      legalForm: user.legal_form || user.legalForm || ''
    });
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      console.log('ProfileManagement: Updating profile with form data:', formData);

      // Map form data to database field names (camelCase to snake_case)
      const mappedUserData = {
        full_name: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        city: formData.city,
        company: formData.company,
        job_title: formData.jobTitle,
        bio: formData.bio,
        is_company: formData.isCompany,
        company_name: formData.companyName,
        ice_number: formData.iceNumber,
        company_address: formData.companyAddress,
        company_phone: formData.companyPhone,
        company_city: formData.companyCity,
        company_email: formData.companyEmail,
        tax_id: formData.taxId,
        legal_form: formData.legalForm
      };

      console.log('ProfileManagement: Mapped data for database update:', mappedUserData);

      // Update user through the userManagementService to ensure real-time sync
      const updatedUser = await updateUser(user.id, mappedUserData, user.id);

      console.log('ProfileManagement: Update result:', updatedUser);

      if (updatedUser) {
        // Update the local form data to reflect the saved changes
        setFormData({
          fullName: updatedUser.full_name || '',
          email: updatedUser.email || '',
          phone: updatedUser.phone || '',
          city: updatedUser.city || '',
          company: updatedUser.company || '',
          jobTitle: updatedUser.job_title || '',
          bio: updatedUser.bio || '',
          isCompany: updatedUser.is_company || false,
          companyName: updatedUser.company_name || '',
          iceNumber: updatedUser.ice_number || '',
          companyAddress: updatedUser.company_address || '',
          companyPhone: updatedUser.company_phone || '',
          companyCity: updatedUser.company_city || '',
          companyEmail: updatedUser.company_email || '',
          taxId: updatedUser.tax_id || '',
          legalForm: updatedUser.legal_form || ''
        });

        // Notify parent component of the update
        onUserUpdate(updatedUser);

        console.log('ProfileManagement: Profile updated successfully');
        alert('Profile updated successfully!');
      } else {
        throw new Error('Update operation returned null or undefined');
      }
    } catch (error) {
      console.error('ProfileManagement: Error updating profile:', error);
      alert(`Error updating profile: ${error instanceof Error ? error.message : 'Please try again.'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordData(prev => ({ ...prev, [field]: value }));
  };

  const validatePassword = (password: string) => {
    const requirements = {
      minLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumbers: /\d/.test(password),
      hasSpecialChars: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    return {
      isValid: Object.values(requirements).every(Boolean),
      requirements
    };
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match');
      return;
    }

    const validation = validatePassword(passwordData.newPassword);
    if (!validation.isValid) {
      alert('Password does not meet security requirements');
      return;
    }

    setPasswordLoading(true);
    try {
      console.log('Changing password for user:', user.id);

      // Update password using Supabase Auth
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (error) {
        throw error;
      }

      alert('Password changed successfully!');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setShowPasswordSection(false);
    } catch (error) {
      console.error('Error changing password:', error);
      alert('Error changing password. Please try again.');
    } finally {
      setPasswordLoading(false);
    }
  };

  const moroccanCities = MOROCCAN_CITIES;

  const legalForms = [
    'SARL', 'SA', 'SAS', 'SNC', 'SCS', 'Auto-entrepreneur', 'Entreprise individuelle'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Profile Settings</h3>
        <p className="text-gray-600 dark:text-gray-300">Manage your personal and company information</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Account Type Selection - Hidden for Admin/Manager roles */}
        {!shouldHideAccountType && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Building className="h-5 w-5 text-teal-600 mr-2" />
              Account Type
            </h4>
            <div className="flex space-x-6">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="accountType"
                  checked={!formData.isCompany}
                  onChange={() => handleInputChange('isCompany', false)}
                  className="mr-2 text-teal-600 focus:ring-teal-500"
                />
                <span className="text-gray-700 dark:text-gray-300">Individual</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="accountType"
                  checked={formData.isCompany}
                  onChange={() => handleInputChange('isCompany', true)}
                  className="mr-2 text-teal-600 focus:ring-teal-500"
                />
                <span className="text-gray-700 dark:text-gray-300">Company</span>
              </label>
            </div>
          </div>
        )}

        {/* Security Notice for Admin/Manager */}
        {shouldHideAccountType && (
          <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
            <div className="flex items-center">
              <Shield className="h-5 w-5 text-amber-600 mr-2" />
              <div>
                <h4 className="text-sm font-semibold text-amber-800">Account Type Restriction</h4>
                <p className="text-sm text-amber-700">
                  As an {user.userType}, you cannot modify your account type for security reasons.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Personal Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <User className="h-5 w-5 text-teal-600 mr-2" />
            {formData.isCompany ? 'Contact Person Information' : 'Personal Information'}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Full Name
              </label>
              <input
                type="text"
                value={formData.fullName}
                onChange={(e) => handleInputChange('fullName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Job Title
              </label>
              <input
                type="text"
                value={formData.jobTitle}
                onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Bio
            </label>
            <textarea
              rows={3}
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Brief description about yourself..."
            />
          </div>
        </div>

        {/* Company Information (only shown if isCompany is true) */}
        {formData.isCompany && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
              <Building className="h-5 w-5 text-teal-600 mr-2" />
              Company Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company Name *
                </label>
                <input
                  type="text"
                  value={formData.companyName}
                  onChange={(e) => handleInputChange('companyName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required={formData.isCompany}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  ICE Number
                </label>
                <input
                  type="text"
                  value={formData.iceNumber}
                  onChange={(e) => handleInputChange('iceNumber', e.target.value)}
                  placeholder="000000000000000"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tax ID
                </label>
                <input
                  type="text"
                  value={formData.taxId}
                  onChange={(e) => handleInputChange('taxId', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Legal Form
                </label>
                <select
                  value={formData.legalForm}
                  onChange={(e) => handleInputChange('legalForm', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select legal form</option>
                  {legalForms.map(form => (
                    <option key={form} value={form}>{form}</option>
                  ))}
                </select>
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company Address
                </label>
                <textarea
                  rows={2}
                  value={formData.companyAddress}
                  onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Company address..."
                />
              </div>
            </div>
          </div>
        )}

        {/* Contact Information */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Mail className="h-5 w-5 text-teal-600 mr-2" />
            Contact Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Phone
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+212 6 XX XX XX XX"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                City
              </label>
              <select
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select city</option>
                {moroccanCities.map(city => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>
            </div>
            {!formData.isCompany && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Company (Optional)
                </label>
                <input
                  type="text"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
              </div>
            )}
            {formData.isCompany && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company Phone
                  </label>
                  <input
                    type="tel"
                    value={formData.companyPhone}
                    onChange={(e) => handleInputChange('companyPhone', e.target.value)}
                    placeholder="+212 5 XX XX XX XX"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company Email
                  </label>
                  <input
                    type="email"
                    value={formData.companyEmail}
                    onChange={(e) => handleInputChange('companyEmail', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company City
                  </label>
                  <select
                    value={formData.companyCity}
                    onChange={(e) => handleInputChange('companyCity', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Select company city</option>
                    {moroccanCities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))}
                  </select>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="bg-teal-600 text-white py-2 px-6 rounded-lg hover:bg-teal-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4" />
            <span>{loading ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </form>

      {/* Security Information Section */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <Lock className="h-5 w-5 text-teal-600 mr-2" />
            Security Settings
          </h4>
          <button
            onClick={() => setShowPasswordSection(!showPasswordSection)}
            className="text-teal-600 hover:text-teal-700 text-sm font-medium"
          >
            {showPasswordSection ? 'Hide' : 'Change Password'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center mb-2">
              <Shield className="h-4 w-4 text-green-600 mr-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Account Security</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Your account is secured with encrypted password protection.
            </p>
          </div>
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center mb-2">
              <User className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Role: {user.userType}</span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Your account has {user.userType} level permissions.
            </p>
          </div>
        </div>

        {/* Password Change Form */}
        {showPasswordSection && (
          <form onSubmit={handlePasswordSubmit} className="space-y-4 border-t border-gray-200 dark:border-gray-600 pt-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <AlertTriangle className="h-4 w-4 text-blue-600 mr-2" />
                <span className="text-sm font-semibold text-blue-800 dark:text-blue-200">Password Requirements</span>
              </div>
              <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                <li>• At least 8 characters long</li>
                <li>• Contains uppercase and lowercase letters</li>
                <li>• Contains at least one number</li>
                <li>• Contains at least one special character (!@#$%^&*)</li>
              </ul>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Current Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Current Password
                </label>
                <div className="relative">
                  <input
                    type={showCurrentPassword ? 'text' : 'password'}
                    value={passwordData.currentPassword}
                    onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                    required
                    className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Enter current password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              {/* New Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  New Password
                </label>
                <div className="relative">
                  <input
                    type={showNewPassword ? 'text' : 'password'}
                    value={passwordData.newPassword}
                    onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                    required
                    className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Enter new password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {/* Password Strength Indicator */}
                {passwordData.newPassword && (
                  <div className="mt-2">
                    {(() => {
                      const validation = validatePassword(passwordData.newPassword);
                      return (
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <div className={`h-2 w-full rounded-full ${
                              validation.isValid ? 'bg-green-500' :
                              Object.values(validation.requirements).filter(Boolean).length >= 3 ? 'bg-amber-500' : 'bg-red-500'
                            }`}></div>
                            <span className={`text-xs ${
                              validation.isValid ? 'text-green-600' :
                              Object.values(validation.requirements).filter(Boolean).length >= 3 ? 'text-amber-600' : 'text-red-600'
                            }`}>
                              {validation.isValid ? 'Strong' :
                               Object.values(validation.requirements).filter(Boolean).length >= 3 ? 'Medium' : 'Weak'}
                            </span>
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                )}
              </div>

              {/* Confirm Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Confirm Password
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={passwordData.confirmPassword}
                    onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                    required
                    className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Confirm new password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {/* Password Match Indicator */}
                {passwordData.confirmPassword && (
                  <div className="mt-1">
                    {passwordData.newPassword === passwordData.confirmPassword ? (
                      <span className="text-xs text-green-600 flex items-center">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                        Passwords match
                      </span>
                    ) : (
                      <span className="text-xs text-red-600 flex items-center">
                        <span className="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
                        Passwords do not match
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Password Change Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => {
                  setShowPasswordSection(false);
                  setPasswordData({
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: ''
                  });
                }}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={passwordLoading || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                className="bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Lock className="h-4 w-4" />
                <span>{passwordLoading ? 'Changing Password...' : 'Change Password'}</span>
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ProfileManagement;
