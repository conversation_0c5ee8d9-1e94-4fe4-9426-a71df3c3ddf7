import { ReactNode } from 'react';
import PageNavigation from '../navigation/PageNavigation';

interface PageLayoutProps {
  children: ReactNode;
  currentPage: string;
  onNavigate: (page: string) => void;
  breadcrumbs?: { label: string; page: string }[];
  title: string;
  subtitle?: string;
  actions?: ReactNode;
  className?: string;
}

const PageLayout = ({ 
  children, 
  currentPage, 
  onNavigate, 
  breadcrumbs, 
  title, 
  subtitle, 
  actions,
  className = ''
}: PageLayoutProps) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <PageNavigation
        currentPage={currentPage}
        onNavigate={onNavigate}
        breadcrumbs={breadcrumbs}
        title={title}
        subtitle={subtitle}
      />
      
      <div className={`p-6 ${className}`}>
        {/* Page Actions */}
        {actions && (
          <div className="mb-6 flex justify-end">
            {actions}
          </div>
        )}
        
        {/* Page Content */}
        <div className="space-y-6">
          {children}
        </div>
      </div>
    </div>
  );
};

export default PageLayout;
