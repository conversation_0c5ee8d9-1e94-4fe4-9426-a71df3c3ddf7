/**
 * Branch-Specific Product Management Test Suite
 * Tests the integration between Product Management and Branch Management
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { supabase } from '../integrations/supabase/client';
import { createProduct, updateProduct, getProducts } from '../services/inventoryService';
import { getBranches } from '../services/branchService';
import { 
  getBranchInventory, 
  updateBranchInventory, 
  createBranchInventory,
  syncProductStockAcrossBranches 
} from '../services/branchInventoryService';

describe('Branch-Specific Product Management', () => {
  let testBranchId: string;
  let testProductId: string;

  beforeEach(async () => {
    // Create a test branch
    const { data: branch, error: branchError } = await supabase
      .from('branches')
      .insert({
        name: 'Test Branch',
        code: 'TEST-001',
        address: {
          street: '123 Test St',
          city: 'Test City',
          state: 'Test State',
          postal_code: '12345',
          country: 'Morocco'
        },
        contact: {
          phone: '+212-123-456-789',
          email: '<EMAIL>',
          manager: 'Test Manager'
        },
        is_active: true
      })
      .select()
      .single();

    if (branchError) throw branchError;
    testBranchId = branch.id;
  });

  afterEach(async () => {
    // Clean up test data
    if (testProductId) {
      await supabase.from('branch_inventory').delete().eq('product_id', testProductId);
      await supabase.from('products').delete().eq('id', testProductId);
    }
    if (testBranchId) {
      await supabase.from('branches').delete().eq('id', testBranchId);
    }
  });

  describe('Product Creation with Branch Assignment', () => {
    it('should create a product and assign it to a branch', async () => {
      // Create product with branch assignment
      const productData = {
        title: 'Test Product',
        description: 'A test product for branch management',
        sku: 'TEST-SKU-001',
        category: 'Test Category',
        brand: 'Test Brand',
        price: 29.99,
        resellerPrice: 24.99,
        stock: 100,
        minStock: 10,
        isActive: true,
        isNew: false,
        featuredImage: '/test-image.jpg',
        thumbnailImages: ['/test-thumb1.jpg', '/test-thumb2.jpg'],
        branchId: testBranchId
      };

      const result = await createProduct(productData);
      expect(result).toBeTruthy();
      testProductId = result!.id;

      // Verify branch inventory was created
      const branchInventory = await getBranchInventory(testBranchId);
      const productInventory = branchInventory.find(item => item.productId === testProductId);
      
      expect(productInventory).toBeTruthy();
      expect(productInventory!.stock).toBe(100);
      expect(productInventory!.minStock).toBe(10);
    });

    it('should create product without branch assignment', async () => {
      const productData = {
        title: 'Test Product No Branch',
        description: 'A test product without branch assignment',
        sku: 'TEST-SKU-002',
        category: 'Test Category',
        brand: 'Test Brand',
        price: 19.99,
        stock: 50,
        minStock: 5,
        isActive: true,
        isNew: false
      };

      const result = await createProduct(productData);
      expect(result).toBeTruthy();
      testProductId = result!.id;

      // Verify no branch inventory was created
      const branchInventory = await getBranchInventory(testBranchId);
      const productInventory = branchInventory.find(item => item.productId === testProductId);
      
      expect(productInventory).toBeFalsy();
    });
  });

  describe('Product Updates with Branch Synchronization', () => {
    beforeEach(async () => {
      // Create a test product first
      const productData = {
        title: 'Test Product for Update',
        sku: 'TEST-SKU-UPDATE',
        category: 'Test Category',
        price: 39.99,
        stock: 75,
        minStock: 15,
        branchId: testBranchId
      };

      const result = await createProduct(productData);
      testProductId = result!.id;
    });

    it('should update product and sync with branch inventory', async () => {
      const updates = {
        stock: 150,
        minStock: 20,
        branchId: testBranchId
      };

      const result = await updateProduct(testProductId, updates);
      expect(result).toBeTruthy();

      // Verify branch inventory was updated
      const branchInventory = await getBranchInventory(testBranchId);
      const productInventory = branchInventory.find(item => item.productId === testProductId);
      
      expect(productInventory).toBeTruthy();
      expect(productInventory!.stock).toBe(150);
      expect(productInventory!.minStock).toBe(20);
    });

    it('should sync stock across all branches when product is updated', async () => {
      // Create another branch
      const { data: branch2, error } = await supabase
        .from('branches')
        .insert({
          name: 'Test Branch 2',
          code: 'TEST-002',
          address: { street: '456 Test Ave', city: 'Test City 2', postal_code: '67890', country: 'Morocco' },
          contact: { phone: '+212-987-654-321', email: '<EMAIL>', manager: 'Test Manager 2' },
          is_active: true
        })
        .select()
        .single();

      if (error) throw error;
      const testBranchId2 = branch2.id;

      // Create inventory for the second branch
      await createBranchInventory(testBranchId2, testProductId, 50, 10);

      // Update product stock
      const success = await syncProductStockAcrossBranches(testProductId, 200, 25);
      expect(success).toBe(true);

      // Verify both branches have updated stock
      const branch1Inventory = await getBranchInventory(testBranchId);
      const branch2Inventory = await getBranchInventory(testBranchId2);

      const product1 = branch1Inventory.find(item => item.productId === testProductId);
      const product2 = branch2Inventory.find(item => item.productId === testProductId);

      expect(product1!.stock).toBe(200);
      expect(product1!.minStock).toBe(25);
      expect(product2!.stock).toBe(200);
      expect(product2!.minStock).toBe(25);

      // Clean up
      await supabase.from('branches').delete().eq('id', testBranchId2);
    });
  });

  describe('Product Listing with Branch Information', () => {
    beforeEach(async () => {
      // Create test products with branch assignments
      const product1Data = {
        title: 'Branch Product 1',
        sku: 'BRANCH-001',
        category: 'Test Category',
        price: 15.99,
        stock: 30,
        minStock: 5,
        branchId: testBranchId
      };

      const product2Data = {
        title: 'Branch Product 2',
        sku: 'BRANCH-002',
        category: 'Test Category',
        price: 25.99,
        stock: 45,
        minStock: 8
        // No branch assignment
      };

      const result1 = await createProduct(product1Data);
      const result2 = await createProduct(product2Data);
      
      testProductId = result1!.id; // Store one for cleanup
    });

    it('should fetch products with branch information', async () => {
      const products = await getProducts();
      
      // Find our test products
      const branchProduct = products.find(p => p.sku === 'BRANCH-001');
      const noBranchProduct = products.find(p => p.sku === 'BRANCH-002');

      expect(branchProduct).toBeTruthy();
      expect(noBranchProduct).toBeTruthy();

      // Check branch information
      expect((branchProduct as any).branchName).toBe('Test Branch');
      expect((branchProduct as any).branchCode).toBe('TEST-001');
      expect((branchProduct as any).branchStock).toBe(30);

      // Product without branch should not have branch info
      expect((noBranchProduct as any).branchName).toBeFalsy();
    });

    it('should filter products by branch', async () => {
      const products = await getProducts({ branchId: testBranchId });
      
      // Should only return products assigned to this branch
      const branchProducts = products.filter(p => (p as any).branchId === testBranchId);
      expect(branchProducts.length).toBeGreaterThan(0);

      // All returned products should have branch information
      branchProducts.forEach(product => {
        expect((product as any).branchName).toBe('Test Branch');
        expect((product as any).branchCode).toBe('TEST-001');
      });
    });
  });

  describe('Branch Inventory Management', () => {
    beforeEach(async () => {
      const productData = {
        title: 'Inventory Test Product',
        sku: 'INV-TEST-001',
        category: 'Test Category',
        price: 49.99,
        stock: 80,
        minStock: 12,
        branchId: testBranchId
      };

      const result = await createProduct(productData);
      testProductId = result!.id;
    });

    it('should get branch inventory with product details', async () => {
      const inventory = await getBranchInventory(testBranchId);
      const productInventory = inventory.find(item => item.productId === testProductId);

      expect(productInventory).toBeTruthy();
      expect(productInventory!.productTitle).toBe('Inventory Test Product');
      expect(productInventory!.productSku).toBe('INV-TEST-001');
      expect(productInventory!.branchName).toBe('Test Branch');
      expect(productInventory!.branchCode).toBe('TEST-001');
      expect(productInventory!.stock).toBe(80);
      expect(productInventory!.minStock).toBe(12);
    });

    it('should update branch inventory', async () => {
      const success = await updateBranchInventory({
        branchId: testBranchId,
        productId: testProductId,
        stock: 120,
        minStock: 18
      });

      expect(success).toBe(true);

      // Verify update
      const inventory = await getBranchInventory(testBranchId);
      const productInventory = inventory.find(item => item.productId === testProductId);

      expect(productInventory!.stock).toBe(120);
      expect(productInventory!.minStock).toBe(18);
    });
  });

  describe('Real-time Synchronization', () => {
    it('should emit real-time events when branch inventory is updated', async () => {
      // This test would require setting up event listeners
      // For now, we'll just verify the service methods work correctly
      const productData = {
        title: 'Real-time Test Product',
        sku: 'RT-TEST-001',
        category: 'Test Category',
        price: 35.99,
        stock: 60,
        minStock: 10,
        branchId: testBranchId
      };

      const result = await createProduct(productData);
      testProductId = result!.id;

      // Update inventory
      const success = await updateBranchInventory({
        branchId: testBranchId,
        productId: testProductId,
        stock: 90,
        minStock: 15
      });

      expect(success).toBe(true);
      
      // In a real application, this would trigger real-time events
      // that would update the UI immediately across all connected clients
    });
  });
});
