
export interface OrderTracking {
  id: string;
  orderId: string;
  status: OrderTrackingStatus;
  location: string;
  timestamp: string;
  notes?: string;
  updatedBy: string;
}

export type OrderTrackingStatus = 
  | 'order_placed'
  | 'payment_confirmed'
  | 'preparing'
  | 'ready_for_pickup'
  | 'out_for_delivery'
  | 'delivered'
  | 'cancelled'
  | 'returned';

export interface OrderTemplate {
  id: string;
  name: string;
  customerId: string;
  items: {
    productId: string;
    quantity: number;
  }[];
  isActive: boolean;
  createdAt: string;
  lastUsed?: string;
}

export interface BulkOrderOperation {
  id: string;
  type: 'status_update' | 'bulk_create' | 'bulk_cancel';
  orderIds: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdBy: string;
  createdAt: string;
  completedAt?: string;
  results?: {
    successful: string[];
    failed: { orderId: string; error: string }[];
  };
}

export interface OrderFilters {
  status?: string[];
  paymentStatus?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  customerId?: string;
  branchId?: string;
  minAmount?: number;
  maxAmount?: number;
  searchTerm?: string;
}
