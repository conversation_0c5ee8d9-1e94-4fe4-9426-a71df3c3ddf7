
export interface AutomationRule {
  id: string;
  name: string;
  type: 'stock_reorder' | 'email_notification' | 'approval_workflow' | 'scheduled_report';
  isActive: boolean;
  conditions: AutomationCondition[];
  actions: AutomationAction[];
  schedule?: ScheduleConfig;
  createdAt: string;
  updatedAt: string;
}

export interface AutomationCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}

export interface AutomationAction {
  type: 'reorder_stock' | 'send_email' | 'require_approval' | 'generate_report';
  parameters: Record<string, any>;
}

export interface ScheduleConfig {
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string; // HH:MM format
  dayOfWeek?: number; // 0-6 for weekly
  dayOfMonth?: number; // 1-31 for monthly
}

export interface StockReorderRule {
  id: string;
  productId: string;
  minStockLevel: number;
  reorderQuantity: number;
  supplierId?: string;
  isActive: boolean;
  lastTriggered?: string;
}

export interface ApprovalWorkflow {
  id: string;
  name: string;
  type: 'order' | 'transfer' | 'discount';
  threshold: number;
  approvers: string[];
  requiredApprovals: number;
  isActive: boolean;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'push';
  subject: string;
  body: string;
  variables: string[];
}
