
export interface Product {
  id: string;
  title: string;
  description: string;
  category: string;
  brand: string;
  price: number;
  resellerPrice: number;
  image: string; // Main/featured image
  featuredImage: string; // Primary display image
  thumbnailImages: string[]; // Additional product images (up to 3)
  secondaryImages?: string[]; // Legacy support - will be migrated to thumbnailImages
  rating: number;
  stock: number;
  minStock: number;
  isActive: boolean;
  isNew: boolean;
  sku: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  parentId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface StockMovement {
  id: string;
  productId: string;
  type: 'in' | 'out' | 'adjustment';
  quantity: number;
  reason: string;
  reference?: string; // Order ID, Purchase Order, etc.
  createdBy: string;
  createdAt: string;
}

export interface LowStockAlert {
  id: string;
  productId: string;
  productTitle: string;
  currentStock: number;
  minStock: number;
  category: string;
  isResolved: boolean;
  createdAt: string;
}
