
export interface SystemConfig {
  id: string;
  category: 'general' | 'security' | 'notifications' | 'integrations' | 'performance';
  key: string;
  value: any;
  description: string;
  dataType: 'string' | 'number' | 'boolean' | 'json' | 'array';
  isEditable: boolean;
  requiresRestart: boolean;
  updatedAt: string;
  updatedBy: string;
}

export interface AuditLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  entity: string;
  entityId: string;
  changes: AuditChange[];
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface AuditChange {
  field: string;
  oldValue: any;
  newValue: any;
}

export interface BackupJob {
  id: string;
  type: 'full' | 'incremental' | 'differential';
  status: 'scheduled' | 'running' | 'completed' | 'failed';
  fileName: string;
  fileSize?: number;
  duration?: number;
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
  schedule?: BackupSchedule;
}

export interface BackupSchedule {
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string;
  dayOfWeek?: number;
  dayOfMonth?: number;
  retention: number; // days
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  lastChecked: string;
  metrics: SystemMetric[];
  alerts: SystemAlert[];
}

export interface SystemMetric {
  name: string;
  value: number;
  unit: string;
  threshold: number;
  status: 'normal' | 'warning' | 'critical';
}

export interface SystemAlert {
  id: string;
  type: 'performance' | 'security' | 'error' | 'warning';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  isResolved: boolean;
  resolvedAt?: string;
}
