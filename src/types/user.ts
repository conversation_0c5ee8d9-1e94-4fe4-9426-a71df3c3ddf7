
export type UserType = 'admin' | 'manager' | 'client' | 'reseller' | 'delivery_person';
export type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended';

export interface User {
  id: string;
  email: string;
  fullName: string;
  userType: UserType;
  phone?: string;
  city?: string;
  isActive: boolean;
  status: UserStatus;
  createdAt: string;
  lastLogin?: string;
  // Company information fields
  isCompany?: boolean;
  companyName?: string;
  iceNumber?: string; // Morocco ICE number
  companyAddress?: string;
  companyPhone?: string;
  companyCity?: string;
  companyEmail?: string;
  taxId?: string;
  legalForm?: string; // SARL, SA, etc.
}

export interface UserProfile {
  userId: string;
  company?: string;
  jobTitle?: string;
  bio?: string;
  avatar?: string;
  // Company profile information
  isCompany?: boolean;
  companyName?: string;
  iceNumber?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyCity?: string;
  companyEmail?: string;
  taxId?: string;
  legalForm?: string;
  preferences: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
      orderUpdates: boolean;
      promotions: boolean;
      stockAlerts: boolean;
    };
    orderDefaults: {
      deliveryPreference: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  id: string;
  userId: string;
  type: 'shipping' | 'billing' | 'both';
  label: string;
  fullName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethod {
  id: string;
  userId: string;
  type: 'bank_transfer' | 'check' | 'cash_on_delivery';
  label: string;
  isDefault: boolean;
  details: {
    // Bank transfer details
    bankName?: string;
    accountNumber?: string;
    // Check details
    checkDetails?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface UserActivity {
  id: string;
  userId: string;
  action: string;
  details: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
}

export interface UserSession {
  id: string;
  userId: string;
  token: string;
  expiresAt: string;
  isActive: boolean;
  createdAt: string;
}
