
export interface SalesReport {
  period: string;
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  topProducts: {
    id: string;
    name: string;
    sales: number;
    revenue: number;
  }[];
  dailySales: {
    date: string;
    sales: number;
    orders: number;
  }[];
}

export interface InventoryAnalytics {
  totalProducts: number;
  lowStockItems: number;
  outOfStockItems: number;
  topSellingProducts: {
    id: string;
    name: string;
    unitsSold: number;
    turnoverRate: number;
  }[];
  categoryPerformance: {
    category: string;
    revenue: number;
    unitsSold: number;
    averagePrice: number;
  }[];
  stockMovements: {
    date: string;
    inbound: number;
    outbound: number;
  }[];
}

export interface CustomerAnalytics {
  totalCustomers: number;
  newCustomers: number;
  repeatCustomers: number;
  customerLifetimeValue: number;
  topCustomers: {
    id: string;
    name: string;
    totalSpent: number;
    orderCount: number;
  }[];
  customerSegments: {
    segment: string;
    count: number;
    averageSpend: number;
  }[];
}

export interface KPIMetrics {
  revenue: {
    current: number;
    previous: number;
    growth: number;
  };
  orders: {
    current: number;
    previous: number;
    growth: number;
  };
  customers: {
    current: number;
    previous: number;
    growth: number;
  };
  averageOrderValue: {
    current: number;
    previous: number;
    growth: number;
  };
  conversionRate: {
    current: number;
    previous: number;
    growth: number;
  };
  inventoryTurnover: {
    current: number;
    previous: number;
    growth: number;
  };
}

export type ReportPeriod = 'daily' | 'weekly' | 'monthly' | 'yearly';
export type ReportType = 'sales' | 'inventory' | 'customers' | 'performance';
