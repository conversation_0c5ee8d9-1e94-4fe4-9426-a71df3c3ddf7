
export interface APIEndpoint {
  id: string;
  name: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description: string;
  authentication: 'api_key' | 'bearer_token' | 'basic_auth' | 'none';
  parameters: APIParameter[];
  responses: APIResponse[];
  isActive: boolean;
  rateLimit?: number;
  createdAt: string;
  updatedAt: string;
}

export interface APIParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  example?: any;
}

export interface APIResponse {
  statusCode: number;
  description: string;
  schema?: Record<string, any>;
}

export interface Webhook {
  id: string;
  name: string;
  url: string;
  events: WebhookEvent[];
  secret?: string;
  isActive: boolean;
  retryAttempts: number;
  timeout: number;
  headers?: Record<string, string>;
  lastTriggered?: string;
  successCount: number;
  failureCount: number;
  createdAt: string;
}

export interface WebhookEvent {
  type: string;
  description: string;
  samplePayload: Record<string, any>;
}

export interface ImportExportJob {
  id: string;
  type: 'import' | 'export';
  entity: 'products' | 'orders' | 'customers' | 'inventory';
  format: 'csv' | 'excel' | 'json' | 'xml';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  fileName: string;
  fileSize?: number;
  recordsProcessed: number;
  totalRecords: number;
  errors: string[];
  createdAt: string;
  completedAt?: string;
}

export interface ERPIntegration {
  id: string;
  name: string;
  type: 'sap' | 'oracle' | 'microsoft_dynamics' | 'netsuite' | 'custom';
  connectionString: string;
  isConnected: boolean;
  lastSync?: string;
  syncFrequency: 'real_time' | 'hourly' | 'daily' | 'weekly';
  mappings: ERPMapping[];
  settings: Record<string, any>;
}

export interface ERPMapping {
  localField: string;
  erpField: string;
  transformation?: string;
  isRequired: boolean;
}
