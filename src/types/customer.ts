
export interface Wishlist {
  id: string;
  customerId: string;
  productId: number;
  addedAt: string;
}

export interface ProductReview {
  id: string;
  productId: number;
  customerId: string;
  customerName: string;
  rating: number; // 1-5 stars
  title?: string;
  comment?: string;
  verified: boolean; // purchased product
  helpful: number; // helpful votes
  createdAt: string;
  updatedAt: string;
}

export interface CustomerBehavior {
  customerId: string;
  viewedProducts: number[];
  purchaseHistory: {
    productId: number;
    quantity: number;
    purchasedAt: string;
  }[];
  searchHistory: string[];
  categoryPreferences: Record<string, number>; // category -> frequency
}
