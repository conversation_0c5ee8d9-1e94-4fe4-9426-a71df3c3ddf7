// Management types for categories and branches

export interface Category {
  id: string;
  name: string;
  description: string;
  parentId?: string;
  level: number;
  isActive: boolean;
  sortOrder: number;
  icon?: string;
  color?: string;
  productCount: number;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface Branch {
  id: string;
  name: string;
  code: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  contact: {
    phone: string;
    email: string;
    manager: string;
  };
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  isActive: boolean;
  isMainBranch: boolean;
  operatingHours: {
    [key: string]: {
      open: string;
      close: string;
      isClosed: boolean;
    };
  };
  services: string[];
  capacity: {
    storage: number;
    staff: number;
  };
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface ManagementStats {
  totalProducts: number;
  totalCategories: number;
  totalBranches: number;
  activeBranches: number;
  recentChanges: {
    products: number;
    categories: number;
    branches: number;
  };
}
