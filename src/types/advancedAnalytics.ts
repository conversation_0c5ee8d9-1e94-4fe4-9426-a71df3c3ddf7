
export interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  customerCount: number;
  totalRevenue: number;
  averageOrderValue: number;
  criteria: {
    minSpend?: number;
    maxSpend?: number;
    orderFrequency?: string;
    lastOrderDays?: number;
  };
  color: string;
}

export interface CustomerLifetimeValue {
  customerId: string;
  customerName: string;
  segment: string;
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  firstOrderDate: string;
  lastOrderDate: string;
  predictedLifetimeValue: number;
  churnRisk: 'low' | 'medium' | 'high';
  acquisitionCost: number;
  profitMargin: number;
}

export interface ProfitMarginAnalysis {
  productId: string;
  productName: string;
  category: string;
  region: string;
  revenue: number;
  cost: number;
  grossProfit: number;
  grossMargin: number;
  unitsSold: number;
  averageSellingPrice: number;
  averageCost: number;
}

export interface RegionProfitability {
  region: string;
  totalRevenue: number;
  totalCost: number;
  grossProfit: number;
  grossMargin: number;
  customerCount: number;
  orderCount: number;
}

export interface CategoryProfitability {
  category: string;
  totalRevenue: number;
  totalCost: number;
  grossProfit: number;
  grossMargin: number;
  productCount: number;
  unitsSold: number;
}
