
export interface PromoCode {
  id: string;
  code: string;
  description: string;
  type: 'percentage' | 'fixed';
  value: number; // percentage (0-100) or fixed amount
  minOrderAmount?: number;
  maxDiscount?: number; // for percentage discounts
  usageLimit?: number;
  usedCount: number;
  isActive: boolean;
  validFrom: string;
  validUntil: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface PromoCodeUsage {
  id: string;
  promoCodeId: string;
  orderId: string;
  customerId: string;
  discountAmount: number;
  usedAt: string;
}
