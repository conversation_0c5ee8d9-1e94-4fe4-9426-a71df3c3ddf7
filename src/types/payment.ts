
export interface Payment {
  id: string;
  orderId: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  processedAt?: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

export type PaymentMethod = 'cash' | 'bank_transfer' | 'check';
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded' | 'cancelled';

export interface PaymentTransaction {
  id: string;
  paymentId: string;
  type: 'charge' | 'refund' | 'partial_refund';
  amount: number;
  status: PaymentStatus;
  gateway: string;
  gatewayTransactionId?: string;
  createdAt: string;
  processedAt?: string;
  failureReason?: string;
}

export interface CreditAccount {
  id: string;
  customerId: string;
  balance: number;
  creditLimit: number;
  availableCredit: number;
  currency: string;
  status: 'active' | 'suspended' | 'closed';
  createdAt: string;
  updatedAt: string;
}

export interface CreditTransaction {
  id: string;
  creditAccountId: string;
  type: 'credit' | 'debit' | 'adjustment';
  amount: number;
  description: string;
  referenceId?: string;
  referenceType?: 'order' | 'payment' | 'adjustment';
  createdAt: string;
  createdBy: string;
}
