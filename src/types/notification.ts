
export interface Notification {
  id: string;
  userId: string;
  type: 'order' | 'stock' | 'delivery' | 'system' | 'promotion';
  title: string;
  message: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
  metadata?: {
    orderId?: string;
    productId?: string;
    stockLevel?: number;
    userEmail?: string;
  };
  createdAt: string;
  readAt?: string;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  orderUpdates: boolean;
  stockAlerts: boolean;
  promotions: boolean;
  deliveryUpdates: boolean;
}
