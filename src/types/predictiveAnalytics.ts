
export interface DemandForecast {
  productId: string;
  productName: string;
  category: string;
  currentStock: number;
  predictedDemand: {
    nextWeek: number;
    nextMonth: number;
    nextQuarter: number;
  };
  confidence: number; // 0-1
  seasonality: SeasonalityPattern;
  trends: TrendAnalysis;
  recommendations: string[];
}

export interface SeasonalityPattern {
  pattern: 'seasonal' | 'cyclical' | 'irregular' | 'none';
  peakMonths: number[];
  lowMonths: number[];
  seasonalityStrength: number; // 0-1
}

export interface TrendAnalysis {
  direction: 'increasing' | 'decreasing' | 'stable';
  strength: number; // 0-1
  trendDuration: number; // months
  changeRate: number; // percentage
}

export interface ProfitabilityAnalysis {
  period: string;
  totalRevenue: number;
  totalCost: number;
  grossProfit: number;
  profitMargin: number;
  byProduct: ProductProfitability[];
  byBranch: BranchProfitability[];
  byCategory: CategoryProfitability[];
}

export interface ProductProfitability {
  productId: string;
  productName: string;
  revenue: number;
  cost: number;
  profit: number;
  margin: number;
  unitsSold: number;
  averageSellingPrice: number;
}

export interface BranchProfitability {
  branchId: string;
  branchName: string;
  revenue: number;
  cost: number;
  profit: number;
  margin: number;
  operatingExpenses: number;
}

export interface CategoryProfitability {
  category: string;
  revenue: number;
  cost: number;
  profit: number;
  margin: number;
  unitsSold: number;
  marketShare: number;
}
