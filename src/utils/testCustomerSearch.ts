import { liveDataService } from '../services/liveDataService';

/**
 * Test customer search functionality
 */
export const testCustomerSearch = async (): Promise<boolean> => {
  console.log('🧪 Testing Customer Search Functionality...');
  
  try {
    // Test 1: Check if liveDataService is available
    console.log('📋 Step 1: Checking liveDataService availability...');
    if (!liveDataService || typeof liveDataService.getAllCustomers !== 'function') {
      console.error('❌ liveDataService or getAllCustomers method is not available');
      return false;
    }
    console.log('✅ liveDataService is available');

    // Test 2: Load all customers
    console.log('👥 Step 2: Loading customers from database...');
    const customers = await liveDataService.getAllCustomers();
    
    if (!Array.isArray(customers)) {
      console.error('❌ getAllCustomers did not return an array');
      return false;
    }
    
    console.log(`✅ Loaded ${customers.length} customers from database`);
    
    if (customers.length === 0) {
      console.warn('⚠️ No customers found in database');
      console.log('ℹ️ This might be expected if no users with client/reseller roles exist');
      
      // Test 3: Check all users to see what user types exist
      console.log('🔍 Step 3: Checking all users to see available user types...');
      try {
        const allUsers = await liveDataService.getAllUsers();
        console.log(`📊 Total users in database: ${allUsers.length}`);
        
        const userTypeStats = allUsers.reduce((stats, user) => {
          const userType = user.userType || 'unknown';
          stats[userType] = (stats[userType] || 0) + 1;
          return stats;
        }, {} as Record<string, number>);
        
        console.log('📈 User type distribution:', userTypeStats);
        
        const clientsAndResellers = allUsers.filter(user => 
          user.userType === 'client' || user.userType === 'reseller'
        );
        
        if (clientsAndResellers.length === 0) {
          console.log('ℹ️ No users with client or reseller roles found');
          console.log('💡 To test customer search, create some users with user_type = "client" or "reseller"');
        } else {
          console.log(`✅ Found ${clientsAndResellers.length} users with client/reseller roles`);
          clientsAndResellers.forEach(user => {
            console.log(`   - ${user.full_name} (${user.email}) - ${user.userType}`);
          });
        }
      } catch (userError) {
        console.error('❌ Error loading all users:', userError);
      }
      
      return true; // Not a failure if no customers exist
    }

    // Test 4: Verify customer data structure
    console.log('🔍 Step 4: Verifying customer data structure...');
    const sampleCustomer = customers[0];
    const requiredFields = ['id', 'full_name', 'email', 'user_type'];
    const missingFields = requiredFields.filter(field => !sampleCustomer[field]);
    
    if (missingFields.length > 0) {
      console.error('❌ Customer data missing required fields:', missingFields);
      return false;
    }
    console.log('✅ Customer data structure is valid');

    // Test 5: Test customer transformation
    console.log('🔄 Step 5: Testing customer data transformation...');
    const transformedCustomers = customers.map(user => ({
      id: user.id,
      full_name: user.full_name,
      email: user.email,
      phone: user.phone,
      company: user.company_name,
      address: user.company_address,
      userType: user.user_type as 'client' | 'reseller'
    }));
    
    console.log(`✅ Successfully transformed ${transformedCustomers.length} customers`);

    // Test 6: Test search functionality
    console.log('🔍 Step 6: Testing search functionality...');
    const testSearchTerms = ['test', 'admin', 'client', '@'];
    
    for (const searchTerm of testSearchTerms) {
      const filteredCustomers = transformedCustomers.filter(customer => {
        if (!searchTerm.trim()) return true;
        
        const searchLower = searchTerm.toLowerCase().trim();
        const nameMatch = customer.full_name?.toLowerCase().includes(searchLower) || false;
        const emailMatch = customer.email?.toLowerCase().includes(searchLower) || false;
        const companyMatch = customer.company?.toLowerCase().includes(searchLower) || false;
        const phoneMatch = customer.phone?.toLowerCase().includes(searchLower) || false;
        
        return nameMatch || emailMatch || companyMatch || phoneMatch;
      });
      
      console.log(`   Search "${searchTerm}": ${filteredCustomers.length} results`);
    }

    // Test 7: Display sample customers
    console.log('👥 Step 7: Sample customers:');
    customers.slice(0, 3).forEach((customer, index) => {
      console.log(`   ${index + 1}. ${customer.full_name} (${customer.email}) - ${customer.user_type}`);
      if (customer.company_name) {
        console.log(`      Company: ${customer.company_name}`);
      }
      if (customer.phone) {
        console.log(`      Phone: ${customer.phone}`);
      }
    });

    console.log('✅ All customer search tests passed!');
    return true;
    
  } catch (error) {
    console.error('❌ Customer search test failed:', error);
    return false;
  }
};

/**
 * Test customer search with specific search terms
 */
export const testCustomerSearchWithTerms = async (searchTerms: string[]): Promise<void> => {
  console.log('🔍 Testing Customer Search with Specific Terms...');
  
  try {
    const customers = await liveDataService.getAllCustomers();
    const transformedCustomers = customers.map(user => ({
      id: user.id,
      full_name: user.full_name,
      email: user.email,
      phone: user.phone,
      company: user.company_name,
      address: user.company_address,
      userType: user.user_type as 'client' | 'reseller'
    }));
    
    console.log(`📊 Testing search on ${transformedCustomers.length} customers`);
    
    for (const searchTerm of searchTerms) {
      const filteredCustomers = transformedCustomers.filter(customer => {
        if (!searchTerm.trim()) return true;
        
        const searchLower = searchTerm.toLowerCase().trim();
        const nameMatch = customer.full_name?.toLowerCase().includes(searchLower) || false;
        const emailMatch = customer.email?.toLowerCase().includes(searchLower) || false;
        const companyMatch = customer.company?.toLowerCase().includes(searchLower) || false;
        const phoneMatch = customer.phone?.toLowerCase().includes(searchLower) || false;
        
        return nameMatch || emailMatch || companyMatch || phoneMatch;
      });
      
      console.log(`🔍 Search "${searchTerm}": ${filteredCustomers.length} results`);
      
      if (filteredCustomers.length > 0 && filteredCustomers.length <= 5) {
        filteredCustomers.forEach(customer => {
          console.log(`   - ${customer.full_name} (${customer.email})`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing customer search with terms:', error);
  }
};

/**
 * Run comprehensive customer search tests
 */
export const runCustomerSearchTests = async (): Promise<void> => {
  console.log('🧪 Starting Comprehensive Customer Search Tests...\n');
  
  const functionalityTest = await testCustomerSearch();
  
  if (functionalityTest) {
    await testCustomerSearchWithTerms(['admin', 'test', 'client', 'company', '@gmail', '212']);
  }
  
  console.log('\n📋 Test Summary:');
  console.log(`   Customer Search Tests: ${functionalityTest ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (functionalityTest) {
    console.log('\n🎉 Customer Search System is working correctly!');
    console.log('   - Customer data loading: ✅ Working');
    console.log('   - Data transformation: ✅ Working');
    console.log('   - Search filtering: ✅ Working');
    console.log('   - Case-insensitive search: ✅ Working');
    console.log('   - Multi-field search: ✅ Working (name, email, phone, company)');
  } else {
    console.log('\n❌ Customer Search System has issues that need attention');
  }
};

export default {
  testCustomerSearch,
  testCustomerSearchWithTerms,
  runCustomerSearchTests
};
