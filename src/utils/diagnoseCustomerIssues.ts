import { supabase } from '../integrations/supabase/client';

/**
 * Diagnose customer search issues by checking database directly
 */
export const diagnoseCustomerIssues = async (): Promise<void> => {
  console.log('🔍 Diagnosing Customer Search Issues...\n');
  
  try {
    // Test 1: Check total users in database
    console.log('📊 Step 1: Checking total users in database...');
    const { data: allUsers, error: allUsersError, count: totalUsers } = await supabase
      .from('users')
      .select('*', { count: 'exact' });
    
    if (allUsersError) {
      console.error('❌ Error fetching all users:', allUsersError);
      return;
    }
    
    console.log(`✅ Total users in database: ${totalUsers}`);
    
    if (!allUsers || allUsers.length === 0) {
      console.log('⚠️ No users found in database at all');
      return;
    }

    // Test 2: Check user type distribution
    console.log('\n📈 Step 2: Analyzing user type distribution...');
    const userTypeStats = allUsers.reduce((stats, user) => {
      const userType = user.user_type || 'null';
      stats[userType] = (stats[userType] || 0) + 1;
      return stats;
    }, {} as Record<string, number>);
    
    console.log('User type distribution:');
    Object.entries(userTypeStats).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} users`);
    });

    // Test 3: Check active status distribution
    console.log('\n🔄 Step 3: Checking active status distribution...');
    const activeStats = allUsers.reduce((stats, user) => {
      const status = user.is_active ? 'active' : 'inactive';
      stats[status] = (stats[status] || 0) + 1;
      return stats;
    }, {} as Record<string, number>);
    
    console.log('Active status distribution:');
    Object.entries(activeStats).forEach(([status, count]) => {
      console.log(`   ${status}: ${count} users`);
    });

    // Test 4: Check specifically for client and reseller users
    console.log('\n👥 Step 4: Checking for client and reseller users...');
    const { data: customers, error: customersError } = await supabase
      .from('users')
      .select('*')
      .in('user_type', ['client', 'reseller'])
      .eq('is_active', true);
    
    if (customersError) {
      console.error('❌ Error fetching customers:', customersError);
      return;
    }
    
    console.log(`✅ Found ${customers?.length || 0} active client/reseller users`);
    
    if (customers && customers.length > 0) {
      console.log('Sample customers:');
      customers.slice(0, 5).forEach((customer, index) => {
        console.log(`   ${index + 1}. ${customer.full_name} (${customer.email}) - ${customer.user_type}`);
      });
    }

    // Test 5: Check for users with different case variations
    console.log('\n🔤 Step 5: Checking for case variations in user_type...');
    const caseVariations = ['Client', 'CLIENT', 'Reseller', 'RESELLER', 'client', 'reseller'];
    
    for (const variation of caseVariations) {
      const { data: variationUsers, error } = await supabase
        .from('users')
        .select('id, full_name, user_type')
        .eq('user_type', variation)
        .limit(5);
      
      if (!error && variationUsers && variationUsers.length > 0) {
        console.log(`   Found ${variationUsers.length} users with user_type = "${variation}"`);
      }
    }

    // Test 6: Check for users with null or empty user_type but might be customers
    console.log('\n❓ Step 6: Checking users with null/empty user_type...');
    const { data: nullTypeUsers, error: nullError } = await supabase
      .from('users')
      .select('id, full_name, email, user_type')
      .is('user_type', null)
      .limit(10);
    
    if (!nullError && nullTypeUsers && nullTypeUsers.length > 0) {
      console.log(`   Found ${nullTypeUsers.length} users with null user_type`);
      nullTypeUsers.forEach(user => {
        console.log(`   - ${user.full_name} (${user.email}) - user_type: ${user.user_type}`);
      });
    }

    // Test 7: Test the exact query used by getAllCustomers
    console.log('\n🎯 Step 7: Testing exact getAllCustomers query...');
    const { data: exactQueryResult, error: exactError } = await supabase
      .from('users')
      .select(`
        *,
        customer_profiles (
          id,
          discount_rate,
          credit_limit,
          total_orders,
          total_spent,
          last_order_date,
          loyalty_points,
          status
        )
      `)
      .in('user_type', ['client', 'reseller'])
      .eq('is_active', true)
      .order('full_name');
    
    if (exactError) {
      console.error('❌ Error with exact getAllCustomers query:', exactError);
    } else {
      console.log(`✅ Exact query returned ${exactQueryResult?.length || 0} customers`);
      
      if (exactQueryResult && exactQueryResult.length > 0) {
        console.log('Sample results from exact query:');
        exactQueryResult.slice(0, 3).forEach((customer, index) => {
          console.log(`   ${index + 1}. ${customer.full_name} (${customer.email}) - ${customer.user_type}`);
          if (customer.customer_profiles) {
            console.log(`      Has customer profile: Yes`);
          }
        });
      }
    }

    // Test 8: Recommendations
    console.log('\n💡 Step 8: Recommendations...');
    
    if (customers && customers.length === 0) {
      console.log('🔧 To fix the customer search issue:');
      console.log('   1. Create users with user_type = "client" or "reseller"');
      console.log('   2. Ensure users have is_active = true');
      console.log('   3. Make sure users have full_name and email filled');
      console.log('   4. Consider updating existing users to have the correct user_type');
      
      // Show how to create a test customer
      console.log('\n📝 Example SQL to create a test customer:');
      console.log(`
INSERT INTO users (
  id, 
  full_name, 
  email, 
  user_type, 
  is_active, 
  created_at, 
  updated_at
) VALUES (
  gen_random_uuid(),
  'Test Customer',
  '<EMAIL>',
  'client',
  true,
  now(),
  now()
);`);
    } else {
      console.log('✅ Customer data looks good!');
      console.log('   - Users with client/reseller roles exist');
      console.log('   - Users are active');
      console.log('   - The getAllCustomers query works correctly');
    }

  } catch (error) {
    console.error('❌ Error during diagnosis:', error);
  }
};

/**
 * Quick test to verify customer search is working
 */
export const quickCustomerTest = async (): Promise<boolean> => {
  try {
    console.log('⚡ Quick Customer Test...');
    
    const { data: customers, error } = await supabase
      .from('users')
      .select('id, full_name, email, user_type, is_active')
      .in('user_type', ['client', 'reseller'])
      .eq('is_active', true)
      .limit(5);
    
    if (error) {
      console.error('❌ Quick test failed:', error);
      return false;
    }
    
    console.log(`✅ Quick test found ${customers?.length || 0} customers`);
    return (customers?.length || 0) > 0;
    
  } catch (error) {
    console.error('❌ Quick test error:', error);
    return false;
  }
};

export default {
  diagnoseCustomerIssues,
  quickCustomerTest
};
