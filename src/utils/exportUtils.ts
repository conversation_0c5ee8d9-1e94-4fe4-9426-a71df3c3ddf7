/**
 * Universal Data Export Utilities for YalaOffice
 * Supports Excel (.xlsx) and CSV export functionality
 * Handles real-time data from Supabase with proper formatting
 */

import * as XLSX from 'xlsx';

export interface ExportOptions {
  filename?: string;
  sheetName?: string;
  includeTimestamp?: boolean;
  dateFormat?: string;
}

/**
 * Export data to Excel (.xlsx) format
 */
export const exportToExcel = (
  data: any[],
  options: ExportOptions = {}
): void => {
  const {
    filename = 'export',
    sheetName = 'Sheet1',
    includeTimestamp = true,
    dateFormat = 'YYYY-MM-DD_HH-mm-ss'
  } = options;

  try {
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    // Generate filename with timestamp if requested
    const timestamp = includeTimestamp 
      ? `_${new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)}`
      : '';
    
    const finalFilename = `${filename}${timestamp}.xlsx`;

    // Write and download file
    XLSX.writeFile(workbook, finalFilename);
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    throw new Error('Failed to export data to Excel format');
  }
};

/**
 * Export data to CSV format
 */
export const exportToCSV = (
  data: any[],
  options: ExportOptions = {}
): void => {
  const {
    filename = 'export',
    includeTimestamp = true
  } = options;

  try {
    if (!data || data.length === 0) {
      throw new Error('No data to export');
    }

    // Get headers from first object
    const headers = Object.keys(data[0]);
    
    // Create CSV content
    const csvContent = [
      // Header row
      headers.join(','),
      // Data rows
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          // Handle values that contain commas, quotes, or newlines
          if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value ?? '';
        }).join(',')
      )
    ].join('\n');

    // Generate filename with timestamp if requested
    const timestamp = includeTimestamp 
      ? `_${new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)}`
      : '';
    
    const finalFilename = `${filename}${timestamp}.csv`;

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', finalFilename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  } catch (error) {
    console.error('Error exporting to CSV:', error);
    throw new Error('Failed to export data to CSV format');
  }
};

/**
 * Export users data with proper formatting
 */
export const exportUsers = (users: any[], format: 'excel' | 'csv' = 'excel'): void => {
  const formattedUsers = users.map(user => ({
    'User ID': user.id,
    'Full Name': user.full_name || user.fullName,
    'Email': user.email,
    'Phone': user.phone,
    'City': user.city,
    'User Type': user.user_type || user.userType,
    'Status': user.is_active ? 'Active' : 'Inactive',
    'Created Date': user.created_at ? new Date(user.created_at).toLocaleDateString() : '',
    'Company Address': user.company_address || user.address || ''
  }));

  const options: ExportOptions = {
    filename: 'users_export',
    sheetName: 'Users'
  };

  if (format === 'excel') {
    exportToExcel(formattedUsers, options);
  } else {
    exportToCSV(formattedUsers, options);
  }
};

/**
 * Export products data with proper formatting
 */
export const exportProducts = (products: any[], format: 'excel' | 'csv' = 'excel'): void => {
  const formattedProducts = products.map(product => ({
    'Product ID': product.id,
    'Name': product.name,
    'Description': product.description,
    'Category': product.category,
    'Price': product.price,
    'Stock Quantity': product.stock_quantity || product.stockQuantity,
    'SKU': product.sku,
    'Status': product.is_active ? 'Active' : 'Inactive',
    'Created Date': product.created_at ? new Date(product.created_at).toLocaleDateString() : '',
    'Updated Date': product.updated_at ? new Date(product.updated_at).toLocaleDateString() : ''
  }));

  const options: ExportOptions = {
    filename: 'products_export',
    sheetName: 'Products'
  };

  if (format === 'excel') {
    exportToExcel(formattedProducts, options);
  } else {
    exportToCSV(formattedProducts, options);
  }
};

/**
 * Export orders data with proper formatting
 */
export const exportOrders = (orders: any[], format: 'excel' | 'csv' = 'excel'): void => {
  const formattedOrders = orders.map(order => ({
    'Order ID': order.id,
    'Customer Name': order.customer_name || order.customerName,
    'Customer Email': order.customer_email || order.customerEmail,
    'Total Amount': order.total_amount || order.totalAmount,
    'Status': order.status,
    'Payment Method': order.payment_method || order.paymentMethod,
    'Order Date': order.created_at ? new Date(order.created_at).toLocaleDateString() : '',
    'Delivery Address': order.delivery_address || order.deliveryAddress,
    'Notes': order.notes || ''
  }));

  const options: ExportOptions = {
    filename: 'orders_export',
    sheetName: 'Orders'
  };

  if (format === 'excel') {
    exportToExcel(formattedOrders, options);
  } else {
    exportToCSV(formattedOrders, options);
  }
};

/**
 * Export branches data with proper formatting
 */
export const exportBranches = (branches: any[], format: 'excel' | 'csv' = 'excel'): void => {
  const formattedBranches = branches.map(branch => ({
    'Branch ID': branch.id,
    'Name': branch.name,
    'Address': branch.address,
    'City': branch.city,
    'Phone': branch.phone,
    'Email': branch.email,
    'Manager': branch.manager_name || branch.managerName,
    'Status': branch.is_active ? 'Active' : 'Inactive',
    'Created Date': branch.created_at ? new Date(branch.created_at).toLocaleDateString() : ''
  }));

  const options: ExportOptions = {
    filename: 'branches_export',
    sheetName: 'Branches'
  };

  if (format === 'excel') {
    exportToExcel(formattedBranches, options);
  } else {
    exportToCSV(formattedBranches, options);
  }
};

/**
 * Format data for export by cleaning and standardizing values
 */
export const formatDataForExport = (data: any[]): any[] => {
  return data.map(item => {
    const formatted: any = {};
    
    Object.keys(item).forEach(key => {
      let value = item[key];
      
      // Handle different data types
      if (value === null || value === undefined) {
        formatted[key] = '';
      } else if (typeof value === 'boolean') {
        formatted[key] = value ? 'Yes' : 'No';
      } else if (value instanceof Date) {
        formatted[key] = value.toISOString().split('T')[0]; // YYYY-MM-DD format
      } else if (typeof value === 'object') {
        formatted[key] = JSON.stringify(value);
      } else {
        formatted[key] = value;
      }
    });
    
    return formatted;
  });
};
