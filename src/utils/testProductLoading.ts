import { liveDataService } from '../services/liveDataService';

/**
 * Test product loading functionality for Create Order Modal
 */
export const testProductLoading = async (): Promise<boolean> => {
  console.log('🧪 Testing Product Loading for Create Order Modal...');
  
  try {
    // Test 1: Check if liveDataService is available
    console.log('📋 Step 1: Checking liveDataService availability...');
    if (!liveDataService || typeof liveDataService.getAllProducts !== 'function') {
      console.error('❌ liveDataService or getAllProducts method is not available');
      return false;
    }
    console.log('✅ liveDataService is available');

    // Test 2: Load all products
    console.log('📦 Step 2: Loading products from database...');
    const allProducts = await liveDataService.getAllProducts();
    
    if (!Array.isArray(allProducts)) {
      console.error('❌ getAllProducts did not return an array');
      return false;
    }
    
    console.log(`✅ Loaded ${allProducts.length} products from database`);
    
    if (allProducts.length === 0) {
      console.warn('⚠️ No products found in database');
      console.log('ℹ️ This might be expected if no products exist in the database');
      return true; // Not a failure if no products exist
    }

    // Test 3: Verify product data structure
    console.log('🔍 Step 3: Verifying product data structure...');
    const sampleProduct = allProducts[0];
    const requiredFields = ['id', 'title', 'price', 'stock'];
    const missingFields = requiredFields.filter(field => sampleProduct[field] === undefined);
    
    if (missingFields.length > 0) {
      console.error('❌ Product data missing required fields:', missingFields);
      console.log('Sample product structure:', sampleProduct);
      return false;
    }
    console.log('✅ Product data structure is valid');

    // Test 4: Check product field mapping
    console.log('🔄 Step 4: Checking product field mapping...');
    console.log('Sample product fields:');
    console.log('   - id:', sampleProduct.id);
    console.log('   - title:', sampleProduct.title);
    console.log('   - price:', sampleProduct.price);
    console.log('   - stock:', sampleProduct.stock);
    console.log('   - isActive:', sampleProduct.isActive);
    console.log('   - image:', sampleProduct.image);
    console.log('   - sku:', sampleProduct.sku);
    console.log('   - brand:', sampleProduct.brand);

    // Test 5: Test filtering logic (active products with stock)
    console.log('🔍 Step 5: Testing product filtering logic...');
    const activeProducts = allProducts.filter(product => {
      const isActive = product.isActive !== false; // Default to true if undefined
      const hasStock = (product.stock || 0) > 0;
      return isActive && hasStock;
    });
    
    console.log(`✅ Found ${activeProducts.length} active products with stock > 0`);
    
    if (activeProducts.length === 0) {
      console.warn('⚠️ No active products with stock found');
      
      // Analyze why products are filtered out
      const inactiveProducts = allProducts.filter(p => p.isActive === false);
      const noStockProducts = allProducts.filter(p => (p.stock || 0) <= 0);
      
      console.log('📊 Product analysis:');
      console.log(`   - Total products: ${allProducts.length}`);
      console.log(`   - Inactive products: ${inactiveProducts.length}`);
      console.log(`   - Products with no stock: ${noStockProducts.length}`);
      console.log(`   - Active products with stock: ${activeProducts.length}`);
      
      if (inactiveProducts.length > 0) {
        console.log('💡 Some products are marked as inactive (isActive = false)');
      }
      if (noStockProducts.length > 0) {
        console.log('💡 Some products have stock = 0');
      }
    }

    // Test 6: Test search functionality
    console.log('🔍 Step 6: Testing product search functionality...');
    const testSearchTerms = ['test', 'product', 'phone', 'laptop'];
    
    for (const searchTerm of testSearchTerms) {
      const filteredProducts = activeProducts.filter(product => {
        if (!searchTerm.trim()) return true;
        
        const searchLower = searchTerm.toLowerCase().trim();
        const titleMatch = product.title?.toLowerCase().includes(searchLower) || false;
        const skuMatch = product.sku?.toLowerCase().includes(searchLower) || false;
        const brandMatch = product.brand?.toLowerCase().includes(searchLower) || false;
        const descriptionMatch = product.description?.toLowerCase().includes(searchLower) || false;
        
        return titleMatch || skuMatch || brandMatch || descriptionMatch;
      });
      
      console.log(`   Search "${searchTerm}": ${filteredProducts.length} results`);
    }

    // Test 7: Display sample products
    console.log('📦 Step 7: Sample products:');
    activeProducts.slice(0, 5).forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.title} - ${product.price} Dh (Stock: ${product.stock})`);
      if (product.sku) {
        console.log(`      SKU: ${product.sku}`);
      }
      if (product.brand) {
        console.log(`      Brand: ${product.brand}`);
      }
    });

    // Test 8: Test price formatting
    console.log('💰 Step 8: Testing price formatting...');
    activeProducts.slice(0, 3).forEach(product => {
      const formattedPrice = product.price?.toFixed(2);
      console.log(`   ${product.title}: ${formattedPrice} Dh`);
    });

    console.log('✅ All product loading tests passed!');
    return true;
    
  } catch (error) {
    console.error('❌ Product loading test failed:', error);
    return false;
  }
};

/**
 * Test product search with specific search terms
 */
export const testProductSearchWithTerms = async (searchTerms: string[]): Promise<void> => {
  console.log('🔍 Testing Product Search with Specific Terms...');
  
  try {
    const allProducts = await liveDataService.getAllProducts();
    const activeProducts = allProducts.filter(product => {
      const isActive = product.isActive !== false;
      const hasStock = (product.stock || 0) > 0;
      return isActive && hasStock;
    });
    
    console.log(`📊 Testing search on ${activeProducts.length} active products`);
    
    for (const searchTerm of searchTerms) {
      const filteredProducts = activeProducts.filter(product => {
        if (!searchTerm.trim()) return true;
        
        const searchLower = searchTerm.toLowerCase().trim();
        const titleMatch = product.title?.toLowerCase().includes(searchLower) || false;
        const skuMatch = product.sku?.toLowerCase().includes(searchLower) || false;
        const brandMatch = product.brand?.toLowerCase().includes(searchLower) || false;
        const descriptionMatch = product.description?.toLowerCase().includes(searchLower) || false;
        
        return titleMatch || skuMatch || brandMatch || descriptionMatch;
      });
      
      console.log(`🔍 Search "${searchTerm}": ${filteredProducts.length} results`);
      
      if (filteredProducts.length > 0 && filteredProducts.length <= 5) {
        filteredProducts.forEach(product => {
          console.log(`   - ${product.title} (${product.price} Dh, Stock: ${product.stock})`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing product search with terms:', error);
  }
};

/**
 * Quick test to verify products are loading
 */
export const quickProductTest = async (): Promise<boolean> => {
  try {
    console.log('⚡ Quick Product Test...');
    
    const products = await liveDataService.getAllProducts();
    const activeProducts = products.filter(product => {
      const isActive = product.isActive !== false;
      const hasStock = (product.stock || 0) > 0;
      return isActive && hasStock;
    });
    
    console.log(`✅ Quick test found ${activeProducts.length} active products with stock`);
    return activeProducts.length > 0;
    
  } catch (error) {
    console.error('❌ Quick product test error:', error);
    return false;
  }
};

/**
 * Run comprehensive product loading tests
 */
export const runProductLoadingTests = async (): Promise<void> => {
  console.log('🧪 Starting Comprehensive Product Loading Tests...\n');
  
  const functionalityTest = await testProductLoading();
  
  if (functionalityTest) {
    await testProductSearchWithTerms(['phone', 'laptop', 'book', 'test', 'casio']);
  }
  
  console.log('\n📋 Test Summary:');
  console.log(`   Product Loading Tests: ${functionalityTest ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (functionalityTest) {
    console.log('\n🎉 Product Loading System is working correctly!');
    console.log('   - Product data loading: ✅ Working');
    console.log('   - Data filtering: ✅ Working');
    console.log('   - Search functionality: ✅ Working');
    console.log('   - Field mapping: ✅ Working');
    console.log('   - Price formatting: ✅ Working');
  } else {
    console.log('\n❌ Product Loading System has issues that need attention');
  }
};

export default {
  testProductLoading,
  testProductSearchWithTerms,
  quickProductTest,
  runProductLoadingTests
};
