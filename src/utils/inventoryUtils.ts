
export const generateId = (prefix: string): string => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 5).toUpperCase();
  return `${prefix}-${timestamp}-${random}`;
};

export const formatPrice = (price: number): string => {
  return `${price.toFixed(2)} Dh`;
};

export const getStockStatusColor = (currentStock: number, minStock: number): string => {
  if (currentStock === 0) return 'bg-red-100 text-red-800';
  if (currentStock <= minStock) return 'bg-yellow-100 text-yellow-800';
  return 'bg-green-100 text-green-800';
};

export const getStockStatusText = (currentStock: number, minStock: number): string => {
  if (currentStock === 0) return 'Out of Stock';
  if (currentStock <= minStock) return 'Low Stock';
  return 'In Stock';
};

export const calculateStockValue = (products: Array<{ price: number; stock: number }>): number => {
  return products.reduce((total, product) => total + (product.price * product.stock), 0);
};
