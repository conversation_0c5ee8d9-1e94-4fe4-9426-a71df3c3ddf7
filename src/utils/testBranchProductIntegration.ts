/**
 * Test utility to verify branch-product integration
 * This can be run in the browser console to test the functionality
 */

import { createProduct, updateProduct } from '../services/inventoryService';
import { getBranches } from '../services/branchService';
import { getBranchInventory, getProductPrimaryBranch } from '../services/branchInventoryService';

export const testBranchProductIntegration = async () => {
  console.log('🧪 Testing Branch-Product Integration...');
  
  try {
    // Step 1: Get available branches
    console.log('📋 Step 1: Getting available branches...');
    const branches = await getBranches(true); // Only active branches
    
    if (branches.length === 0) {
      console.error('❌ No active branches found. Please create a branch first.');
      return false;
    }
    
    const testBranch = branches[0];
    console.log('✅ Found test branch:', testBranch.name, '(ID:', testBranch.id, ')');
    
    // Step 2: Create a test product with branch assignment
    console.log('📋 Step 2: Creating test product with branch assignment...');
    const testProductData = {
      title: 'Test Product - Branch Integration',
      description: 'A test product to verify branch-product integration',
      sku: `TEST-BRANCH-${Date.now()}`,
      category: 'Test Category',
      brand: 'Test Brand',
      price: 29.99,
      resellerPrice: 24.99,
      stock: 100,
      minStock: 10,
      isActive: true,
      isNew: false,
      featuredImage: '/test-image.jpg',
      thumbnailImages: ['/test-thumb1.jpg'],
      rating: 0,
      weight: 0.5,
      dimensions: { length: 10, width: 5, height: 2 },
      tags: ['test', 'integration'],
      branchId: testBranch.id
    };
    
    const createdProduct = await createProduct(testProductData);
    
    if (!createdProduct) {
      console.error('❌ Failed to create test product');
      return false;
    }
    
    console.log('✅ Created test product:', createdProduct.title, '(ID:', createdProduct.id, ')');
    
    // Step 3: Verify branch inventory was created
    console.log('📋 Step 3: Verifying branch inventory creation...');
    const branchInventory = await getBranchInventory(testBranch.id);
    const productInventory = branchInventory.find(item => item.productId === createdProduct.id);
    
    if (!productInventory) {
      console.error('❌ Branch inventory not created for product');
      return false;
    }
    
    console.log('✅ Branch inventory created:', {
      productId: productInventory.productId,
      stock: productInventory.stock,
      minStock: productInventory.minStock,
      branchName: productInventory.branchName
    });
    
    // Step 4: Test getting product's primary branch
    console.log('📋 Step 4: Testing product primary branch lookup...');
    const primaryBranchId = await getProductPrimaryBranch(createdProduct.id);
    
    if (primaryBranchId !== testBranch.id) {
      console.error('❌ Primary branch lookup failed. Expected:', testBranch.id, 'Got:', primaryBranchId);
      return false;
    }
    
    console.log('✅ Primary branch lookup successful:', primaryBranchId);
    
    // Step 5: Test product update with branch sync
    console.log('📋 Step 5: Testing product update with branch sync...');
    const updatedProduct = await updateProduct(createdProduct.id, {
      stock: 150,
      minStock: 20,
      branchId: testBranch.id
    });
    
    if (!updatedProduct) {
      console.error('❌ Failed to update product');
      return false;
    }
    
    console.log('✅ Product updated successfully');
    
    // Step 6: Verify branch inventory was updated
    console.log('📋 Step 6: Verifying branch inventory update...');
    const updatedBranchInventory = await getBranchInventory(testBranch.id);
    const updatedProductInventory = updatedBranchInventory.find(item => item.productId === createdProduct.id);
    
    if (!updatedProductInventory || updatedProductInventory.stock !== 150 || updatedProductInventory.minStock !== 20) {
      console.error('❌ Branch inventory not updated correctly. Expected stock: 150, minStock: 20. Got:', {
        stock: updatedProductInventory?.stock,
        minStock: updatedProductInventory?.minStock
      });
      return false;
    }
    
    console.log('✅ Branch inventory updated correctly:', {
      stock: updatedProductInventory.stock,
      minStock: updatedProductInventory.minStock
    });
    
    // Step 7: Cleanup (optional - comment out if you want to keep test data)
    console.log('📋 Step 7: Cleaning up test data...');
    // Note: In a real scenario, you might want to keep the test data or clean it up
    // For now, we'll just log that the test completed successfully
    
    console.log('🎉 All tests passed! Branch-Product integration is working correctly.');
    console.log('📊 Test Summary:');
    console.log('  ✅ Branch retrieval');
    console.log('  ✅ Product creation with branch assignment');
    console.log('  ✅ Branch inventory creation');
    console.log('  ✅ Primary branch lookup');
    console.log('  ✅ Product update with branch sync');
    console.log('  ✅ Branch inventory synchronization');
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
};

// Export for browser console testing
(window as any).testBranchProductIntegration = testBranchProductIntegration;

export default testBranchProductIntegration;
