/**
 * Currency formatting utilities for YalaOffice
 * All monetary values should be displayed in Moroccan Dirham (Dh)
 */

/**
 * Format a number as Moroccan Dirham currency
 * @param amount - The amount to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted currency string with "Dh" suffix
 */
export const formatCurrency = (amount: number | string, decimals: number = 2): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) {
    return '0.00 Dh';
  }
  
  return `${numAmount.toFixed(decimals)} Dh`;
};

/**
 * Format currency without decimal places for whole numbers
 * @param amount - The amount to format
 * @returns Formatted currency string with "Dh" suffix
 */
export const formatCurrencyWhole = (amount: number | string): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) {
    return '0 Dh';
  }
  
  // If it's a whole number, don't show decimals
  if (numAmount % 1 === 0) {
    return `${numAmount.toFixed(0)} Dh`;
  }
  
  return `${numAmount.toFixed(2)} Dh`;
};

/**
 * Format currency with thousands separator
 * @param amount - The amount to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted currency string with thousands separator and "Dh" suffix
 */
export const formatCurrencyWithSeparator = (amount: number | string, decimals: number = 2): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) {
    return '0.00 Dh';
  }
  
  return `${numAmount.toLocaleString('fr-MA', { 
    minimumFractionDigits: decimals, 
    maximumFractionDigits: decimals 
  })} Dh`;
};

/**
 * Parse a currency string to number
 * @param currencyString - String like "123.45 Dh" or "123.45"
 * @returns Parsed number or 0 if invalid
 */
export const parseCurrency = (currencyString: string): number => {
  if (!currencyString) return 0;
  
  // Remove "Dh", "$", and any whitespace, then parse
  const cleanString = currencyString.replace(/[Dh$\s]/g, '');
  const parsed = parseFloat(cleanString);
  
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Validate if a string is a valid currency amount
 * @param value - String to validate
 * @returns True if valid currency format
 */
export const isValidCurrency = (value: string): boolean => {
  if (!value) return false;
  
  // Remove currency symbols and whitespace
  const cleanValue = value.replace(/[Dh$\s]/g, '');
  
  // Check if it's a valid number
  const parsed = parseFloat(cleanValue);
  return !isNaN(parsed) && parsed >= 0;
};

/**
 * Convert USD to MAD (Mock conversion - in real app, use live exchange rates)
 * @param usdAmount - Amount in USD
 * @returns Amount in MAD
 */
export const convertUsdToMad = (usdAmount: number): number => {
  // Mock exchange rate - in production, fetch from API
  const exchangeRate = 10.2; // 1 USD = ~10.2 MAD (approximate)
  return usdAmount * exchangeRate;
};

/**
 * Format price for display in product listings
 * @param price - Product price
 * @param resellerPrice - Optional reseller price
 * @returns Formatted price string
 */
export const formatProductPrice = (price: number, resellerPrice?: number): string => {
  if (resellerPrice && resellerPrice !== price) {
    return `${formatCurrency(price)} (Reseller: ${formatCurrency(resellerPrice)})`;
  }
  return formatCurrency(price);
};

/**
 * Format order total with tax information
 * @param subtotal - Order subtotal
 * @param tax - Tax amount
 * @param total - Total amount
 * @returns Formatted order total breakdown
 */
export const formatOrderTotal = (subtotal: number, tax: number = 0, total?: number): {
  subtotal: string;
  tax: string;
  total: string;
} => {
  const calculatedTotal = total || (subtotal + tax);
  
  return {
    subtotal: formatCurrency(subtotal),
    tax: formatCurrency(tax),
    total: formatCurrency(calculatedTotal)
  };
};

/**
 * Currency constants for the application
 */
export const CURRENCY = {
  SYMBOL: 'Dh',
  CODE: 'MAD',
  NAME: 'Moroccan Dirham',
  LOCALE: 'fr-MA'
} as const;

/**
 * Default currency formatting options
 */
export const DEFAULT_CURRENCY_OPTIONS = {
  decimals: 2,
  showSymbol: true,
  useThousandsSeparator: false
} as const;

export default {
  formatCurrency,
  formatCurrencyWhole,
  formatCurrencyWithSeparator,
  parseCurrency,
  isValidCurrency,
  convertUsdToMad,
  formatProductPrice,
  formatOrderTotal,
  CURRENCY,
  DEFAULT_CURRENCY_OPTIONS
};
