#!/bin/bash

# YalaOffice Production Build Script
# This script builds the application for production deployment

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_DIR/dist"
LOG_FILE="$PROJECT_DIR/build.log"
BUILD_ID=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC} $message" | tee -a "$LOG_FILE" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "INFO" "Checking prerequisites..."
    
    # Check Node.js version
    if ! command -v node &> /dev/null; then
        error_exit "Node.js is not installed"
    fi
    
    local node_version=$(node --version | sed 's/v//')
    local required_version="18.0.0"
    
    if ! printf '%s\n%s\n' "$required_version" "$node_version" | sort -V -C; then
        error_exit "Node.js version $node_version is too old. Required: $required_version+"
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        error_exit "npm is not installed"
    fi
    
    # Check if package.json exists
    if [[ ! -f "$PROJECT_DIR/package.json" ]]; then
        error_exit "package.json not found in project directory"
    fi
    
    # Check environment file
    if [[ ! -f "$PROJECT_DIR/.env.production" ]]; then
        log "WARN" ".env.production not found, using default environment"
    fi
    
    log "SUCCESS" "Prerequisites check passed"
}

# Clean previous builds
clean_build() {
    log "INFO" "Cleaning previous builds..."
    
    if [[ -d "$BUILD_DIR" ]]; then
        rm -rf "$BUILD_DIR"
        log "INFO" "Removed existing build directory"
    fi
    
    # Clean npm cache
    npm cache clean --force > /dev/null 2>&1 || true
    
    # Clean node_modules if requested
    if [[ "${CLEAN_MODULES:-false}" == "true" ]]; then
        if [[ -d "$PROJECT_DIR/node_modules" ]]; then
            rm -rf "$PROJECT_DIR/node_modules"
            log "INFO" "Removed node_modules directory"
        fi
    fi
    
    log "SUCCESS" "Build cleanup completed"
}

# Install dependencies
install_dependencies() {
    log "INFO" "Installing dependencies..."
    
    cd "$PROJECT_DIR"
    
    # Use npm ci for production builds
    npm ci --only=production --silent || error_exit "Failed to install dependencies"
    
    log "SUCCESS" "Dependencies installed successfully"
}

# Run security audit
security_audit() {
    log "INFO" "Running security audit..."
    
    cd "$PROJECT_DIR"
    
    # Run npm audit
    if ! npm audit --audit-level high; then
        if [[ "${IGNORE_AUDIT_ERRORS:-false}" == "true" ]]; then
            log "WARN" "Security audit found issues but continuing due to IGNORE_AUDIT_ERRORS=true"
        else
            error_exit "Security audit failed. Set IGNORE_AUDIT_ERRORS=true to ignore."
        fi
    fi
    
    log "SUCCESS" "Security audit passed"
}

# Run tests
run_tests() {
    if [[ "${SKIP_TESTS:-false}" == "true" ]]; then
        log "INFO" "Skipping tests (SKIP_TESTS=true)"
        return
    fi
    
    log "INFO" "Running tests..."
    
    cd "$PROJECT_DIR"
    
    # Run unit tests
    if npm run test:ci > /dev/null 2>&1; then
        log "SUCCESS" "Unit tests passed"
    else
        if [[ "${IGNORE_TEST_FAILURES:-false}" == "true" ]]; then
            log "WARN" "Tests failed but continuing due to IGNORE_TEST_FAILURES=true"
        else
            error_exit "Tests failed. Set IGNORE_TEST_FAILURES=true to ignore."
        fi
    fi
}

# Build application
build_application() {
    log "INFO" "Building application for production..."
    
    cd "$PROJECT_DIR"
    
    # Set production environment
    export NODE_ENV=production
    
    # Load production environment variables
    if [[ -f ".env.production" ]]; then
        export $(grep -v '^#' .env.production | xargs)
    fi
    
    # Set build metadata
    export VITE_BUILD_ID="$BUILD_ID"
    export VITE_BUILD_TIMESTAMP="$(date -Iseconds)"
    export VITE_GIT_COMMIT="$(git rev-parse HEAD 2>/dev/null || echo 'unknown')"
    export VITE_GIT_BRANCH="$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')"
    
    # Run build
    npm run build || error_exit "Build failed"
    
    # Verify build output
    if [[ ! -d "$BUILD_DIR" ]]; then
        error_exit "Build directory not created"
    fi
    
    if [[ ! -f "$BUILD_DIR/index.html" ]]; then
        error_exit "index.html not found in build output"
    fi
    
    log "SUCCESS" "Application built successfully"
}

# Optimize build
optimize_build() {
    log "INFO" "Optimizing build..."
    
    cd "$BUILD_DIR"
    
    # Compress static assets
    if command -v gzip &> /dev/null; then
        find . -type f \( -name "*.js" -o -name "*.css" -o -name "*.html" -o -name "*.json" \) -exec gzip -k {} \;
        log "INFO" "Gzip compression applied to static assets"
    fi
    
    # Generate file checksums
    find . -type f -exec sha256sum {} \; > checksums.txt
    log "INFO" "Generated file checksums"
    
    # Create build manifest
    cat > build-manifest.json << EOF
{
    "buildId": "$BUILD_ID",
    "timestamp": "$(date -Iseconds)",
    "gitCommit": "$(git -C "$PROJECT_DIR" rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "gitBranch": "$(git -C "$PROJECT_DIR" rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')",
    "nodeVersion": "$(node --version)",
    "npmVersion": "$(npm --version)",
    "environment": "production",
    "buildSize": "$(du -sh . | cut -f1)"
}
EOF
    
    log "SUCCESS" "Build optimization completed"
}

# Generate documentation
generate_docs() {
    if [[ "${SKIP_DOCS:-false}" == "true" ]]; then
        log "INFO" "Skipping documentation generation (SKIP_DOCS=true)"
        return
    fi
    
    log "INFO" "Generating documentation..."
    
    cd "$PROJECT_DIR"
    
    # Generate API documentation if available
    if [[ -f "package.json" ]] && grep -q "docs:generate" package.json; then
        npm run docs:generate > /dev/null 2>&1 || log "WARN" "Failed to generate API documentation"
    fi
    
    # Copy documentation to build
    if [[ -d "docs" ]]; then
        cp -r docs "$BUILD_DIR/" || log "WARN" "Failed to copy documentation"
    fi
    
    log "SUCCESS" "Documentation generation completed"
}

# Create deployment package
create_package() {
    log "INFO" "Creating deployment package..."
    
    cd "$PROJECT_DIR"
    
    # Create package directory
    local package_dir="yalaoffice-$BUILD_ID"
    mkdir -p "$package_dir"
    
    # Copy build files
    cp -r "$BUILD_DIR"/* "$package_dir/"
    
    # Copy deployment files
    cp docker-compose.prod.yml "$package_dir/" 2>/dev/null || true
    cp Dockerfile.prod "$package_dir/" 2>/dev/null || true
    cp nginx.prod.conf "$package_dir/" 2>/dev/null || true
    cp -r scripts "$package_dir/" 2>/dev/null || true
    cp -r monitoring "$package_dir/" 2>/dev/null || true
    
    # Create deployment instructions
    cat > "$package_dir/DEPLOYMENT.md" << EOF
# YalaOffice Deployment Package

**Build ID**: $BUILD_ID
**Build Date**: $(date -Iseconds)
**Git Commit**: $(git rev-parse HEAD 2>/dev/null || echo 'unknown')

## Quick Deployment

1. Extract this package to your server
2. Copy .env.production and configure environment variables
3. Run: \`docker-compose -f docker-compose.prod.yml up -d\`
4. Verify: \`curl -f http://localhost/health\`

## Files Included

- Application build files
- Docker configuration
- Nginx configuration
- Deployment scripts
- Monitoring configuration

For detailed instructions, see docs/PRODUCTION_DEPLOYMENT.md
EOF
    
    # Create tarball
    tar czf "yalaoffice-$BUILD_ID.tar.gz" "$package_dir"
    rm -rf "$package_dir"
    
    local package_size=$(du -sh "yalaoffice-$BUILD_ID.tar.gz" | cut -f1)
    log "SUCCESS" "Deployment package created: yalaoffice-$BUILD_ID.tar.gz ($package_size)"
}

# Validate build
validate_build() {
    log "INFO" "Validating build..."
    
    cd "$BUILD_DIR"
    
    # Check for required files
    local required_files=("index.html" "manifest.json" "sw.js")
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error_exit "Required file missing: $file"
        fi
    done
    
    # Check HTML validity (basic)
    if command -v tidy &> /dev/null; then
        tidy -q -e index.html || log "WARN" "HTML validation warnings found"
    fi
    
    # Check JavaScript syntax (basic)
    local js_files=$(find . -name "*.js" -not -path "./node_modules/*")
    for js_file in $js_files; do
        if command -v node &> /dev/null; then
            node -c "$js_file" || error_exit "JavaScript syntax error in $js_file"
        fi
    done
    
    # Check build size
    local build_size=$(du -sb . | cut -f1)
    local max_size=$((100 * 1024 * 1024)) # 100MB
    
    if [[ $build_size -gt $max_size ]]; then
        log "WARN" "Build size is large: $(du -sh . | cut -f1)"
    fi
    
    log "SUCCESS" "Build validation completed"
}

# Generate build report
generate_report() {
    log "INFO" "Generating build report..."
    
    local report_file="$PROJECT_DIR/build-report-$BUILD_ID.txt"
    
    cat > "$report_file" << EOF
YalaOffice Production Build Report
==================================

Build ID: $BUILD_ID
Build Date: $(date)
Git Commit: $(git -C "$PROJECT_DIR" rev-parse HEAD 2>/dev/null || echo 'unknown')
Git Branch: $(git -C "$PROJECT_DIR" rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')

Environment:
- Node.js: $(node --version)
- npm: $(npm --version)
- OS: $(uname -a)

Build Statistics:
- Build Size: $(du -sh "$BUILD_DIR" | cut -f1)
- File Count: $(find "$BUILD_DIR" -type f | wc -l)
- Build Time: $(($(date +%s) - ${BUILD_START_TIME:-$(date +%s)})) seconds

Files Generated:
$(find "$BUILD_DIR" -type f | head -20)
$([ $(find "$BUILD_DIR" -type f | wc -l) -gt 20 ] && echo "... and $(($(find "$BUILD_DIR" -type f | wc -l) - 20)) more files")

Build Log: $LOG_FILE
EOF
    
    log "SUCCESS" "Build report generated: $report_file"
}

# Main build function
main() {
    local BUILD_START_TIME=$(date +%s)
    
    log "INFO" "Starting YalaOffice production build (ID: $BUILD_ID)"
    
    # Run build steps
    check_prerequisites
    clean_build
    install_dependencies
    security_audit
    run_tests
    build_application
    optimize_build
    generate_docs
    validate_build
    create_package
    generate_report
    
    local build_time=$(($(date +%s) - BUILD_START_TIME))
    log "SUCCESS" "Production build completed successfully in ${build_time}s"
    
    # Print summary
    echo
    echo "========================================="
    echo "Build Summary:"
    echo "- Build ID: $BUILD_ID"
    echo "- Build Size: $(du -sh "$BUILD_DIR" | cut -f1)"
    echo "- Package: yalaoffice-$BUILD_ID.tar.gz"
    echo "- Build Time: ${build_time}s"
    echo "========================================="
}

# Run main function
main "$@"
