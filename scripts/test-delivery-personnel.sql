-- Test script to check delivery personnel in the database
-- Run this in Supabase SQL Editor to debug delivery personnel loading issue

-- =============================================
-- CHECK USERS TABLE STRUCTURE
-- =============================================

-- Check the structure of the users table
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;

-- =============================================
-- CHECK ALL USER TYPES IN DATABASE
-- =============================================

-- See what user types exist in the database
SELECT user_type, COUNT(*) as count, 
       COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
FROM users 
GROUP BY user_type 
ORDER BY user_type;

-- =============================================
-- CHECK FOR DELIVERY PERSONNEL
-- =============================================

-- Look for delivery personnel with different possible values
SELECT 'delivery_person' as search_term, COUNT(*) as count
FROM users 
WHERE user_type = 'delivery_person' AND is_active = true

UNION ALL

SELECT 'delivery' as search_term, COUNT(*) as count
FROM users 
WHERE user_type = 'delivery' AND is_active = true

UNION ALL

SELECT 'delivery (role field)' as search_term, COUNT(*) as count
FROM users 
WHERE role = 'delivery' AND is_active = true;

-- =============================================
-- SHOW ACTUAL DELIVERY PERSONNEL DATA
-- =============================================

-- Show all users that might be delivery personnel
SELECT id, email, full_name, user_type, is_active, created_at
FROM users 
WHERE (user_type ILIKE '%delivery%' OR role ILIKE '%delivery%')
ORDER BY created_at DESC;

-- =============================================
-- CHECK IF ROLE FIELD EXISTS
-- =============================================

-- Check if there's a role field in the users table
SELECT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'role'
) as role_field_exists;

-- =============================================
-- SAMPLE DATA FOR DEBUGGING
-- =============================================

-- Show a sample of all users to understand the data structure
SELECT id, email, full_name, user_type, is_active, 
       CASE WHEN is_company THEN company_name ELSE NULL END as company_name,
       created_at
FROM users 
ORDER BY created_at DESC 
LIMIT 10;

-- =============================================
-- CREATE TEST DELIVERY PERSONNEL (IF NEEDED)
-- =============================================

-- Uncomment the following lines to create test delivery personnel
-- Only run this if no delivery personnel exist in the database

/*
INSERT INTO users (email, full_name, user_type, phone, city, is_active)
VALUES 
    ('<EMAIL>', 'Ahmed Delivery', 'delivery_person', '+212600000001', 'Casablanca', true),
    ('<EMAIL>', 'Fatima Transport', 'delivery_person', '+212600000002', 'Rabat', true),
    ('<EMAIL>', 'Mohamed Livraison', 'delivery_person', '+212600000003', 'Marrakech', true)
ON CONFLICT (email) DO NOTHING;
*/

-- =============================================
-- VERIFY INSERTION (IF ABOVE WAS RUN)
-- =============================================

-- Check if the test delivery personnel were created
SELECT COUNT(*) as delivery_personnel_count
FROM users 
WHERE user_type = 'delivery_person' AND is_active = true;

-- Show all delivery personnel
SELECT id, email, full_name, user_type, phone, city, is_active, created_at
FROM users 
WHERE user_type = 'delivery_person'
ORDER BY created_at DESC;
