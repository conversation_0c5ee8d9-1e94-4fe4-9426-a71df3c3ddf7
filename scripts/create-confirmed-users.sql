-- S<PERSON> Script to Create Confirmed Test Users
-- Run this in your Supabase SQL Editor

-- IMPORTANT: This approach directly modifies auth.users table
-- Alternative: Simply disable email confirmation in Authentication > Settings

-- Method 1: Update existing users to confirmed status (RECOMMENDED)
UPDATE auth.users
SET email_confirmed_at = now()
WHERE email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
);

-- Check if users were updated
SELECT
  email,
  email_confirmed_at,
  raw_user_meta_data->>'full_name' as full_name,
  raw_user_meta_data->>'user_type' as user_type
FROM auth.users
WHERE email IN (
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
)
ORDER BY email;

