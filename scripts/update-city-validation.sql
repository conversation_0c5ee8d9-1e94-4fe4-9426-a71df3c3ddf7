-- =============================================
-- YalaOffice City List Update Migration
-- =============================================
-- This script updates the city validation to support the comprehensive
-- list of Moroccan cities while maintaining backward compatibility
-- with existing user records.
--
-- Date: 2025-01-16
-- Purpose: System-wide city list standardization
-- =============================================

-- Create a function to validate Moroccan cities
CREATE OR REPLACE FUNCTION is_valid_moroccan_city(city_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN city_name = ANY(ARRAY[
        'Ad Dakhla',
        'Ad Darwa',
        'Agadir',
        'Aguelmous',
        '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>',
        '<PERSON>',
        '<PERSON><PERSON>',
        '<PERSON><PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON> Ho<PERSON>ïma',
        'Al <PERSON>',
        'Al ''Attawia',
        'Arfoud',
        'Assilah',
        'Azemmour',
        'Azrou',
        'Aziylal',
        'Barrechid',
        'Ben Guerir',
        'Beni Mellal',
        'Beni Yakhlef',
        'Berkane',
        'Biougra',
        'Bir Jdid',
        'Bou Arfa',
        'Boujad',
        'Bouknadel',
        'Bouskoura',
        'Béni Mellal',
        'Casablanca',
        'Chefchaouen',
        'Chichaoua',
        'Demnat',
        'El Aïoun',
        'El Hajeb',
        'El Jadid',
        'El Kelaa des Srarhna',
        'Errachidia',
        'Fnidq',
        'Fès',
        'Guelmim',
        'Guercif',
        'Iheddadene',
        'Imzouren',
        'Inezgane',
        'Jerada',
        'Kenitra',
        'Khénifra',
        'Khouribga',
        'Kouribga',
        'Ksar El Kebir',
        'Laâyoune',
        'Larache',
        'M''diq',
        'Marrakech',
        'Martil',
        'Mechraa Bel Ksiri',
        'Mehdya',
        'Meknès',
        'Midalt',
        'Missour',
        'Mohammedia',
        'Moulay Abdallah',
        'Moulay Bousselham',
        'Mrirt',
        'My Drarga',
        'Nador',
        'Oued Zem',
        'Oujda-Angad',
        'Oulad Barhil',
        'Oulad Tayeb',
        'Oulad Teïma',
        'Oulad Yaïch',
        'Ouezzane',
        'Qasbat Tadla',
        'Rabat',
        'Safi',
        'Sale',
        'Sefrou',
        'Settat',
        'Sidi Qacem',
        'Sidi Slimane',
        'Sidi Smai''il',
        'Sidi Yahia El Gharb',
        'Sidi Yahya Zaer',
        'Skhirate',
        'Souk et Tnine Jorf el Mellah',
        'Souq Sebt Oulad Nemma',
        'Tahla',
        'Tameslouht',
        'Tangier',
        'Taourirt',
        'Taza',
        'Temara',
        'Temsia',
        'Tifariti',
        'Tit Mellil',
        'Tiznit',
        'Tetouan',
        'Youssoufia',
        'Zagora',
        'Zawyat ech Cheïkh',
        'Zaïo',
        'Zeghanghane'
    ]);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add comment to the function
COMMENT ON FUNCTION is_valid_moroccan_city(TEXT) IS 
'Validates if a city name is in the approved list of Moroccan cities for YalaOffice application';

-- Create a view to show city usage statistics
CREATE OR REPLACE VIEW city_usage_stats AS
SELECT 
    city,
    COUNT(*) as user_count,
    is_valid_moroccan_city(city) as is_valid_city,
    CASE 
        WHEN is_valid_moroccan_city(city) THEN 'Valid'
        ELSE 'Legacy/Invalid'
    END as status
FROM users 
WHERE city IS NOT NULL AND city != ''
GROUP BY city
ORDER BY user_count DESC, city;

-- Add comment to the view
COMMENT ON VIEW city_usage_stats IS 
'Shows usage statistics for cities in the users table with validation status';

-- Create a function to suggest city corrections for legacy data
CREATE OR REPLACE FUNCTION suggest_city_correction(input_city TEXT)
RETURNS TEXT AS $$
DECLARE
    suggestions TEXT[];
BEGIN
    -- Direct mapping for common variations
    CASE LOWER(input_city)
        WHEN 'fez' THEN RETURN 'Fès';
        WHEN 'meknes' THEN RETURN 'Meknès';
        WHEN 'tetouan' THEN RETURN 'Tetouan';
        WHEN 'oujda' THEN RETURN 'Oujda-Angad';
        WHEN 'beni mellal' THEN RETURN 'Beni Mellal';
        WHEN 'el jadida' THEN RETURN 'El Jadid';
        WHEN 'khouribga' THEN RETURN 'Khouribga';
        WHEN 'al hoceima' THEN RETURN 'Al Hoceïma';
        WHEN 'laayoune' THEN RETURN 'Laâyoune';
        WHEN 'dakhla' THEN RETURN 'Ad Dakhla';
        ELSE 
            -- If no direct mapping found, return the original
            RETURN input_city;
    END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add comment to the function
COMMENT ON FUNCTION suggest_city_correction(TEXT) IS 
'Suggests corrections for common city name variations to match the standard list';

-- =============================================
-- REPORTING QUERIES
-- =============================================

-- Query to check current city distribution
-- SELECT * FROM city_usage_stats ORDER BY user_count DESC;

-- Query to find users with invalid cities
-- SELECT id, full_name, email, city, suggest_city_correction(city) as suggested_city
-- FROM users 
-- WHERE city IS NOT NULL 
--   AND city != '' 
--   AND NOT is_valid_moroccan_city(city)
-- ORDER BY city;

-- Query to get total users by city validation status
-- SELECT 
--     CASE 
--         WHEN is_valid_moroccan_city(city) THEN 'Valid Cities'
--         ELSE 'Legacy/Invalid Cities'
--     END as city_status,
--     COUNT(*) as user_count
-- FROM users 
-- WHERE city IS NOT NULL AND city != ''
-- GROUP BY city_status;

-- =============================================
-- MAINTENANCE NOTES
-- =============================================
-- 
-- 1. This migration maintains backward compatibility
-- 2. Existing user records with old city names will continue to work
-- 3. New registrations will be validated against the comprehensive list
-- 4. Use the city_usage_stats view to monitor city data quality
-- 5. Use suggest_city_correction() function to help standardize legacy data
-- 
-- To apply city corrections to existing data (run with caution):
-- UPDATE users SET city = suggest_city_correction(city) 
-- WHERE NOT is_valid_moroccan_city(city) AND city IS NOT NULL;
-- =============================================
