-- Fix Order Items RLS Policies
-- Run this in Supabase SQL Editor to fix order items creation

-- =============================================
-- DROP EXISTING ORDER ITEMS POLICIES
-- =============================================

DROP POLICY IF EXISTS order_items_policy ON order_items;
DROP POLICY IF EXISTS order_items_select_policy ON order_items;
DROP POLICY IF EXISTS order_items_insert_policy ON order_items;
DROP POLICY IF EXISTS order_items_update_policy ON order_items;
DROP POLICY IF EXISTS order_items_delete_policy ON order_items;

-- =============================================
-- CREATE PERMISSIVE ORDER ITEMS POLICIES
-- =============================================

-- Order items SELECT policy - allow authenticated users
CREATE POLICY order_items_select_policy ON order_items
    FOR SELECT USING (
        auth.uid() IS NOT NULL
    );

-- Order items INSERT policy - allow authenticated users
CREATE POLICY order_items_insert_policy ON order_items
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

-- Order items UPDATE policy - allow authenticated users
CREATE POLICY order_items_update_policy ON order_items
    FOR UPDATE USING (
        auth.uid() IS NOT NULL
    );

-- Order items DELETE policy - allow authenticated users
CREATE POLICY order_items_delete_policy ON order_items
    FOR DELETE USING (
        auth.uid() IS NOT NULL
    );

-- =============================================
-- ENABLE RLS AND GRANT PERMISSIONS
-- =============================================

-- Ensure RLS is enabled on order_items table
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON order_items TO authenticated;

-- =============================================
-- VERIFY TABLE STRUCTURE
-- =============================================

-- Check order_items table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'order_items'
ORDER BY ordinal_position;

-- =============================================
-- VERIFY POLICIES
-- =============================================

-- Check if policies are created correctly
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'order_items';
