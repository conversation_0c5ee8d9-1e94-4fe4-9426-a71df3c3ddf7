-- SQL Script to Update "Tétouan" to "Tetouan" in Database
-- This script updates all occurrences of "Tétouan" (with accent) to "Tetouan" (without accent)
-- Run this in your Supabase SQL Editor

-- =============================================
-- UPDATE USERS TABLE
-- =============================================

-- Update city field in users table
UPDATE users 
SET city = 'Tetouan' 
WHERE city = 'Tétouan';

-- Update company_city field in users table
UPDATE users 
SET company_city = 'Tetouan' 
WHERE company_city = 'Tétouan';

-- =============================================
-- UPDATE BRANCHES TABLE
-- =============================================

-- Update city in branch addresses (JSON field)
UPDATE branches 
SET address = jsonb_set(address, '{city}', '"Tetouan"')
WHERE address->>'city' = 'Tétouan';

-- Update branch names that might contain Tétouan
UPDATE branches
SET name = REPLACE(name, 'Tétouan', 'Tetouan')
WHERE name LIKE '%Tétouan%';

-- Update contact information (JSON field)
UPDATE branches
SET contact = jsonb_set(contact, '{address}',
    CASE
        WHEN contact->>'address' LIKE '%Tétouan%'
        THEN to_jsonb(REPLACE(contact->>'address', 'Tétouan', 'Tetouan'))
        ELSE contact->'address'
    END
)
WHERE contact->>'address' LIKE '%Tétouan%';

-- =============================================
-- UPDATE CUSTOMER_PROFILES TABLE
-- =============================================

-- Update notes field in customer_profiles table if it contains Tétouan
UPDATE customer_profiles
SET notes = REPLACE(notes, 'Tétouan', 'Tetouan')
WHERE notes LIKE '%Tétouan%';

-- =============================================
-- UPDATE ORDERS TABLE
-- =============================================

-- Update delivery addresses (JSON field)
UPDATE orders
SET delivery_address = jsonb_set(delivery_address, '{city}', '"Tetouan"')
WHERE delivery_address->>'city' = 'Tétouan';

-- Update billing addresses (JSON field)
UPDATE orders
SET billing_address = jsonb_set(billing_address, '{city}', '"Tetouan"')
WHERE billing_address->>'city' = 'Tétouan';

-- =============================================
-- UPDATE SYSTEM_CONFIGS TABLE
-- =============================================

-- Update company address in system configs
UPDATE system_configs
SET value = to_jsonb(REPLACE(value::text, 'Tétouan', 'Tetouan'))
WHERE key = 'company_address' AND value::text LIKE '%Tétouan%';

-- Update company name in system configs
UPDATE system_configs
SET value = to_jsonb(REPLACE(value::text, 'Tétouan', 'Tetouan'))
WHERE key = 'company_name' AND value::text LIKE '%Tétouan%';

-- =============================================
-- UPDATE SUPPLIERS TABLE
-- =============================================

-- Update supplier addresses (JSON field)
UPDATE suppliers
SET address = jsonb_set(address, '{city}', '"Tetouan"')
WHERE address->>'city' = 'Tétouan';

-- Update supplier names that might contain Tétouan
UPDATE suppliers
SET name = REPLACE(name, 'Tétouan', 'Tetouan')
WHERE name LIKE '%Tétouan%';

-- =============================================
-- UPDATE ORDER_TRACKING TABLE
-- =============================================

-- Update location field in order_tracking table
UPDATE order_tracking
SET location = REPLACE(location, 'Tétouan', 'Tetouan')
WHERE location LIKE '%Tétouan%';

-- Update notes field in order_tracking table
UPDATE order_tracking
SET notes = REPLACE(notes, 'Tétouan', 'Tetouan')
WHERE notes LIKE '%Tétouan%';

-- =============================================
-- UPDATE ANY OTHER TABLES WITH TEXT FIELDS
-- =============================================

-- Update products table if any descriptions contain Tétouan
UPDATE products 
SET description = REPLACE(description, 'Tétouan', 'Tetouan')
WHERE description LIKE '%Tétouan%';

-- Update categories table if any descriptions contain Tétouan
UPDATE categories 
SET description = REPLACE(description, 'Tétouan', 'Tetouan')
WHERE description LIKE '%Tétouan%';

-- =============================================
-- UPDATE AUDIT LOGS AND NOTIFICATIONS
-- =============================================

-- Update audit logs that might contain Tétouan in changes (JSONB field)
UPDATE audit_logs
SET changes = to_jsonb(REPLACE(changes::text, 'Tétouan', 'Tetouan'))
WHERE changes::text LIKE '%Tétouan%';

-- Update notifications that might contain Tétouan in message
UPDATE notifications
SET message = REPLACE(message, 'Tétouan', 'Tetouan')
WHERE message LIKE '%Tétouan%';

-- Update notification titles
UPDATE notifications
SET title = REPLACE(title, 'Tétouan', 'Tetouan')
WHERE title LIKE '%Tétouan%';

-- Update notification metadata (JSONB field)
UPDATE notifications
SET metadata = to_jsonb(REPLACE(metadata::text, 'Tétouan', 'Tetouan'))
WHERE metadata::text LIKE '%Tétouan%';

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Check if any Tétouan (with accent) still exists in database
SELECT 'users.city' as table_field, COUNT(*) as count
FROM users
WHERE city = 'Tétouan'
UNION ALL
SELECT 'users.company_city' as table_field, COUNT(*) as count
FROM users
WHERE company_city = 'Tétouan'
UNION ALL
SELECT 'branches.address' as table_field, COUNT(*) as count
FROM branches
WHERE address->>'city' = 'Tétouan'
UNION ALL
SELECT 'branches.contact' as table_field, COUNT(*) as count
FROM branches
WHERE contact->>'address' LIKE '%Tétouan%'
UNION ALL
SELECT 'customer_profiles.notes' as table_field, COUNT(*) as count
FROM customer_profiles
WHERE notes LIKE '%Tétouan%'
UNION ALL
SELECT 'orders.delivery_address' as table_field, COUNT(*) as count
FROM orders
WHERE delivery_address->>'city' = 'Tétouan'
UNION ALL
SELECT 'orders.billing_address' as table_field, COUNT(*) as count
FROM orders
WHERE billing_address->>'city' = 'Tétouan'
UNION ALL
SELECT 'suppliers.address' as table_field, COUNT(*) as count
FROM suppliers
WHERE address->>'city' = 'Tétouan'
UNION ALL
SELECT 'suppliers.name' as table_field, COUNT(*) as count
FROM suppliers
WHERE name LIKE '%Tétouan%'
UNION ALL
SELECT 'order_tracking.location' as table_field, COUNT(*) as count
FROM order_tracking
WHERE location LIKE '%Tétouan%'
UNION ALL
SELECT 'order_tracking.notes' as table_field, COUNT(*) as count
FROM order_tracking
WHERE notes LIKE '%Tétouan%'
UNION ALL
SELECT 'system_configs.value' as table_field, COUNT(*) as count
FROM system_configs
WHERE value::text LIKE '%Tétouan%'
UNION ALL
SELECT 'audit_logs.changes' as table_field, COUNT(*) as count
FROM audit_logs
WHERE changes::text LIKE '%Tétouan%'
UNION ALL
SELECT 'notifications.message' as table_field, COUNT(*) as count
FROM notifications
WHERE message LIKE '%Tétouan%'
UNION ALL
SELECT 'notifications.title' as table_field, COUNT(*) as count
FROM notifications
WHERE title LIKE '%Tétouan%'
UNION ALL
SELECT 'notifications.metadata' as table_field, COUNT(*) as count
FROM notifications
WHERE metadata::text LIKE '%Tétouan%';

-- Show all records that now have Tetouan (without accent)
SELECT 'users.city' as table_field, COUNT(*) as count
FROM users
WHERE city = 'Tetouan'
UNION ALL
SELECT 'users.company_city' as table_field, COUNT(*) as count
FROM users
WHERE company_city = 'Tetouan'
UNION ALL
SELECT 'branches.address' as table_field, COUNT(*) as count
FROM branches
WHERE address->>'city' = 'Tetouan'
UNION ALL
SELECT 'orders.delivery_address' as table_field, COUNT(*) as count
FROM orders
WHERE delivery_address->>'city' = 'Tetouan'
UNION ALL
SELECT 'orders.billing_address' as table_field, COUNT(*) as count
FROM orders
WHERE billing_address->>'city' = 'Tetouan'
UNION ALL
SELECT 'suppliers.address' as table_field, COUNT(*) as count
FROM suppliers
WHERE address->>'city' = 'Tetouan'
UNION ALL
SELECT 'order_tracking.location' as table_field, COUNT(*) as count
FROM order_tracking
WHERE location LIKE '%Tetouan%'
UNION ALL
SELECT 'system_configs.value' as table_field, COUNT(*) as count
FROM system_configs
WHERE value::text LIKE '%Tetouan%'
UNION ALL
SELECT 'notifications.message' as table_field, COUNT(*) as count
FROM notifications
WHERE message LIKE '%Tetouan%';

-- =============================================
-- COMPLETION MESSAGE
-- =============================================

DO $$
BEGIN
    RAISE NOTICE 'City name update completed successfully!';
    RAISE NOTICE 'All occurrences of "Tétouan" (with accent) have been replaced with "Tetouan" (without accent)';
    RAISE NOTICE 'Please verify the results using the verification queries above.';
END $$;
