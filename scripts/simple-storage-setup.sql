-- =============================================
-- SIMPLE STORAGE SETUP FOR PRODUCT IMAGES
-- Run this AFTER creating the bucket manually
-- =============================================

-- Step 1: Check if bucket exists
SELECT 'Checking if product-images bucket exists...' as step;
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'product-images') 
    THEN 'Bucket exists ✓' 
    ELSE 'Bucket NOT found ✗ - Please create it manually in Supabase Dashboard > Storage'
  END as bucket_status;

-- Step 2: Enable RLS on storage.objects (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Step 3: Drop any existing policies to avoid conflicts
DROP POLICY IF EXISTS "product_images_upload_policy" ON storage.objects;
DROP POLICY IF EXISTS "product_images_read_policy" ON storage.objects;
DROP POLICY IF EXISTS "product_images_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "product_images_delete_policy" ON storage.objects;

-- Step 4: Create simple storage policies
-- Allow authenticated users to upload images
CREATE POLICY "product_images_upload_policy" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
  );

-- Allow everyone to read images (public access)
CREATE POLICY "product_images_read_policy" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'product-images'
  );

-- Allow authenticated users to update images
CREATE POLICY "product_images_update_policy" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
  );

-- Allow authenticated users to delete images
CREATE POLICY "product_images_delete_policy" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
  );

-- Step 5: Verify setup
SELECT 'Storage policies created successfully!' as result;

-- Show bucket info
SELECT 
  'Bucket Information:' as info,
  id, 
  name, 
  public,
  file_size_limit,
  created_at
FROM storage.buckets 
WHERE id = 'product-images';

-- Show created policies
SELECT 
  'Created Policies:' as info,
  policyname,
  cmd as operation
FROM pg_policies 
WHERE schemaname = 'storage' 
  AND tablename = 'objects'
  AND policyname LIKE 'product_images_%'
ORDER BY policyname;
