-- YalaOffice Live Data Setup Script
-- This script populates the database with realistic production data
-- Run this after the main database schema is created

-- =============================================
-- LIVE USER DATA FOR TETOUAN BRANCHES
-- =============================================

-- Insert Admin Users
INSERT INTO users (id, email, full_name, user_type, phone, city, is_active, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '<PERSON><PERSON><PERSON>', 'admin', '+212 6 12 34 56 78', 'Tetouan', true, NOW()),
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '<PERSON><PERSON>', 'admin', '+212 6 87 65 43 21', 'Tetouan', true, NOW());

-- Insert Manager Users
INSERT INTO users (id, email, full_name, user_type, phone, city, is_active, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'Omar Tazi', 'manager', '+212 6 11 22 33 44', 'Tetouan', true, NOW()),
('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', 'Fatima Alaoui', 'manager', '+212 6 55 44 33 22', 'Tetouan', true, NOW());

-- Insert Client Users
INSERT INTO users (id, email, full_name, user_type, phone, city, is_active, is_company, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', 'Ahmed Bennani', 'client', '+212 6 77 88 99 00', 'Tetouan', true, false, NOW()),
('550e8400-e29b-41d4-a716-446655440006', '<EMAIL>', 'Khadija Amrani', 'client', '+212 6 33 44 55 66', 'Tetouan', true, false, NOW()),
('550e8400-e29b-41d4-a716-446655440007', '<EMAIL>', 'Hassan Idrissi', 'client', '+212 6 99 00 11 22', 'Tetouan', true, false, NOW()),
('550e8400-e29b-41d4-a716-446655440008', '<EMAIL>', 'Nadia Berrada', 'client', '+212 6 44 55 66 77', 'Tetouan', true, false, NOW());

-- Insert Reseller Users (Companies)
INSERT INTO users (id, email, full_name, user_type, phone, city, is_active, is_company, company_name, ice_number, company_address, company_phone, company_city, company_email, tax_id, legal_form, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440009', '<EMAIL>', 'Rachid Fassi', 'reseller', '+212 6 88 99 00 11', 'Tetouan', true, true, 'Papeterie Moderne SARL', '001234567890123', 'Avenue Mohammed V, Quartier Administratif, Tetouan', '+212 5 39 12 34 56', 'Tetouan', '<EMAIL>', 'IF12345678', 'SARL', NOW()),
('550e8400-e29b-41d4-a716-446655440010', '<EMAIL>', 'Laila Cherkaoui', 'reseller', '+212 6 22 33 44 55', 'Tetouan', true, true, 'Fournitures du Nord', '001234567890124', 'Rue Al Massira, Centre Ville, Tetouan', '+212 5 39 87 65 43', 'Tetouan', '<EMAIL>', 'IF87654321', 'SARL', NOW()),
('550e8400-e29b-41d4-a716-446655440011', '<EMAIL>', 'Mohamed Taibi', 'reseller', '+212 6 66 77 88 99', 'Tetouan', true, true, 'École Plus Distribution', '001234567890125', 'Boulevard Hassan II, Tetouan', '+212 5 39 55 44 33', 'Tetouan', '<EMAIL>', 'IF11223344', 'SARL', NOW()),
('550e8400-e29b-41d4-a716-446655440012', '<EMAIL>', 'Samira Benkirane', 'reseller', '+212 6 00 11 22 33', 'Tetouan', true, true, 'Bureau Express', '001234567890126', 'Place Al Jala, Tetouan', '+212 5 39 99 88 77', 'Tetouan', '<EMAIL>', 'IF55667788', 'SARL', NOW());

-- Insert Delivery Person Users
INSERT INTO users (id, email, full_name, user_type, phone, city, is_active, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440013', '<EMAIL>', 'Abdelkader Zouani', 'delivery_person', '+212 6 12 23 34 45', 'Tetouan', true, NOW()),
('550e8400-e29b-41d4-a716-446655440014', '<EMAIL>', 'Mustapha Rifi', 'delivery_person', '+212 6 56 67 78 89', 'Tetouan', true, NOW());

-- =============================================
-- TETOUAN BRANCHES SETUP
-- =============================================

-- Insert 3 Branches in Tetouan
INSERT INTO branches (id, name, code, address, contact, coordinates, is_active, is_main_branch, operating_hours, services, capacity, created_by) VALUES
(
    '550e8400-e29b-41d4-a716-446655440101',
    'YalaOffice Centre Ville',
    'TET-CV',
    '{"street": "Avenue Mohammed V, Immeuble Al Andalous", "city": "Tetouan", "state": "Tanger-Tetouan-Al Hoceima", "postal_code": "93000", "country": "Morocco"}',
    '{"phone": "+212 5 39 12 34 56", "email": "<EMAIL>", "manager": "Omar Tazi"}',
    '{"latitude": 35.5889, "longitude": -5.3626}',
    true,
    true,
    '{"monday": {"open": "08:00", "close": "19:00", "is_closed": false}, "tuesday": {"open": "08:00", "close": "19:00", "is_closed": false}, "wednesday": {"open": "08:00", "close": "19:00", "is_closed": false}, "thursday": {"open": "08:00", "close": "19:00", "is_closed": false}, "friday": {"open": "08:00", "close": "19:00", "is_closed": false}, "saturday": {"open": "09:00", "close": "17:00", "is_closed": false}, "sunday": {"open": "10:00", "close": "15:00", "is_closed": false}}',
    ARRAY['retail', 'wholesale', 'delivery', 'consultation'],
    '{"storage": 500, "staff": 8}',
    '550e8400-e29b-41d4-a716-446655440001'
),
(
    '550e8400-e29b-41d4-a716-446655440102',
    'YalaOffice Quartier Administratif',
    'TET-QA',
    '{"street": "Rue Hassan II, Résidence Al Manar", "city": "Tetouan", "state": "Tanger-Tetouan-Al Hoceima", "postal_code": "93000", "country": "Morocco"}',
    '{"phone": "+212 5 39 87 65 43", "email": "<EMAIL>", "manager": "Fatima Alaoui"}',
    '{"latitude": 35.5756, "longitude": -5.3681}',
    true,
    false,
    '{"monday": {"open": "08:30", "close": "18:30", "is_closed": false}, "tuesday": {"open": "08:30", "close": "18:30", "is_closed": false}, "wednesday": {"open": "08:30", "close": "18:30", "is_closed": false}, "thursday": {"open": "08:30", "close": "18:30", "is_closed": false}, "friday": {"open": "08:30", "close": "18:30", "is_closed": false}, "saturday": {"open": "09:00", "close": "16:00", "is_closed": false}, "sunday": {"open": "", "close": "", "is_closed": true}}',
    ARRAY['retail', 'wholesale', 'consultation'],
    '{"storage": 300, "staff": 5}',
    '550e8400-e29b-41d4-a716-446655440001'
),
(
    '550e8400-e29b-41d4-a716-446655440103',
    'YalaOffice Martil',
    'TET-MAR',
    '{"street": "Avenue Corniche, Complexe Al Bahja", "city": "Martil", "state": "Tanger-Tetouan-Al Hoceima", "postal_code": "93150", "country": "Morocco"}',
    '{"phone": "+212 5 39 55 44 33", "email": "<EMAIL>", "manager": "Omar Tazi"}',
    '{"latitude": 35.6167, "longitude": -5.2750}',
    true,
    false,
    '{"monday": {"open": "09:00", "close": "18:00", "is_closed": false}, "tuesday": {"open": "09:00", "close": "18:00", "is_closed": false}, "wednesday": {"open": "09:00", "close": "18:00", "is_closed": false}, "thursday": {"open": "09:00", "close": "18:00", "is_closed": false}, "friday": {"open": "09:00", "close": "18:00", "is_closed": false}, "saturday": {"open": "09:00", "close": "17:00", "is_closed": false}, "sunday": {"open": "10:00", "close": "14:00", "is_closed": false}}',
    ARRAY['retail', 'delivery', 'seasonal'],
    '{"storage": 200, "staff": 4}',
    '550e8400-e29b-41d4-a716-446655440001'
);

-- =============================================
-- PRODUCT CATEGORIES SETUP
-- =============================================

-- Insert 10 Main Categories for Office and School Supplies
INSERT INTO categories (id, name, description, level, parent_id, is_active, sort_order, icon, color, created_by) VALUES
('550e8400-e29b-41d4-a716-446655440201', 'Writing Instruments', 'Pens, pencils, markers, and all writing tools', 0, NULL, true, 1, 'pen', '#3B82F6', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440202', 'Paper & Notebooks', 'All types of paper, notebooks, and writing pads', 0, NULL, true, 2, 'book', '#10B981', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440203', 'School & Office Supplies', 'Essential supplies for schools and offices', 0, NULL, true, 3, 'briefcase', '#F59E0B', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440204', 'Art & Craft Supplies', 'Creative materials for art and craft projects', 0, NULL, true, 4, 'palette', '#EF4444', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440205', 'Filing & Organization', 'Storage and organization solutions', 0, NULL, true, 5, 'folder', '#8B5CF6', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440206', 'Greeting Cards & Gift Supplies', 'Cards, wrapping, and gift accessories', 0, NULL, true, 6, 'gift', '#EC4899', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440207', 'Office & Desk Accessories', 'Desk organizers, accessories, and tools', 0, NULL, true, 7, 'monitor', '#6B7280', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440208', 'Back-to-School Essentials', 'Complete school supply packages', 0, NULL, true, 8, 'backpack', '#059669', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440209', 'Eco-Friendly Stationery', 'Sustainable and environmentally friendly products', 0, NULL, true, 9, 'leaf', '#16A34A', '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440210', 'Specialty & Luxury Stationery', 'Premium and specialized stationery items', 0, NULL, true, 10, 'star', '#7C3AED', '550e8400-e29b-41d4-a716-446655440001');

-- =============================================
-- CUSTOMER PROFILES SETUP
-- =============================================

-- Insert customer profiles for all users
INSERT INTO customer_profiles (user_id, discount_rate, credit_limit, status, created_at) VALUES
-- Client profiles
('550e8400-e29b-41d4-a716-446655440005', 0.00, 1000.00, 'active', NOW()),
('550e8400-e29b-41d4-a716-446655440006', 0.00, 1500.00, 'active', NOW()),
('550e8400-e29b-41d4-a716-446655440007', 0.00, 800.00, 'active', NOW()),
('550e8400-e29b-41d4-a716-446655440008', 0.00, 1200.00, 'active', NOW()),
-- Reseller profiles with wholesale discounts
('550e8400-e29b-41d4-a716-446655440009', 15.00, 10000.00, 'active', NOW()),
('550e8400-e29b-41d4-a716-446655440010', 12.00, 8000.00, 'active', NOW()),
('550e8400-e29b-41d4-a716-446655440011', 18.00, 15000.00, 'active', NOW()),
('550e8400-e29b-41d4-a716-446655440012', 10.00, 6000.00, 'active', NOW());

-- =============================================
-- SYSTEM CONFIGURATIONS
-- =============================================

-- Update system configurations for Tetouan operations
INSERT INTO system_configs (category, key, value, description, data_type, updated_by) VALUES
('general', 'company_name', '"YalaOffice Tetouan"', 'Company name for invoices and documents', 'string', '550e8400-e29b-41d4-a716-446655440001'),
('general', 'company_address', '"Avenue Mohammed V, Tetouan, Morocco"', 'Main company address', 'string', '550e8400-e29b-41d4-a716-446655440001'),
('general', 'company_phone', '"+212 5 39 12 34 56"', 'Main company phone', 'string', '550e8400-e29b-41d4-a716-446655440001'),
('general', 'company_email', '"<EMAIL>"', 'Main company email', 'string', '550e8400-e29b-41d4-a716-446655440001'),
('general', 'tax_number', '"IF12345678"', 'Company tax identification number', 'string', '550e8400-e29b-41d4-a716-446655440001'),
('general', 'ice_number', '"001234567890123"', 'Morocco ICE number', 'string', '550e8400-e29b-41d4-a716-446655440001'),
('inventory', 'low_stock_threshold', '10', 'Default low stock alert threshold', 'number', '550e8400-e29b-41d4-a716-446655440001'),
('inventory', 'auto_reorder_enabled', 'true', 'Enable automatic reordering', 'boolean', '550e8400-e29b-41d4-a716-446655440001'),
('orders', 'default_delivery_fee', '25.00', 'Default delivery fee in MAD', 'number', '550e8400-e29b-41d4-a716-446655440001'),
('orders', 'free_delivery_threshold', '500.00', 'Minimum order for free delivery in MAD', 'number', '550e8400-e29b-41d4-a716-446655440001'),
('pricing', 'default_markup', '40.00', 'Default markup percentage', 'number', '550e8400-e29b-41d4-a716-446655440001'),
('pricing', 'reseller_discount_min', '10.00', 'Minimum reseller discount percentage', 'number', '550e8400-e29b-41d4-a716-446655440001'),
('pricing', 'reseller_discount_max', '25.00', 'Maximum reseller discount percentage', 'number', '550e8400-e29b-41d4-a716-446655440001')
ON CONFLICT (category, key) DO UPDATE SET
    value = EXCLUDED.value,
    updated_at = NOW(),
    updated_by = EXCLUDED.updated_by;

-- =============================================
-- BRANCH ASSIGNMENTS
-- =============================================

-- Assign managers to branches
INSERT INTO branch_assignments (branch_id, user_id, role, is_primary, created_by) VALUES
('550e8400-e29b-41d4-a716-446655440101', '550e8400-e29b-41d4-a716-446655440003', 'manager', true, '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440004', 'manager', true, '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440003', 'manager', false, '550e8400-e29b-41d4-a716-446655440001');

-- Assign delivery persons to branches
INSERT INTO branch_assignments (branch_id, user_id, role, is_primary, created_by) VALUES
('550e8400-e29b-41d4-a716-446655440101', '550e8400-e29b-41d4-a716-446655440013', 'delivery', true, '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440102', '550e8400-e29b-41d4-a716-446655440014', 'delivery', true, '550e8400-e29b-41d4-a716-446655440001'),
('550e8400-e29b-41d4-a716-446655440103', '550e8400-e29b-41d4-a716-446655440013', 'delivery', false, '550e8400-e29b-41d4-a716-446655440001');

-- =============================================
-- AUDIT LOG ENTRY
-- =============================================

INSERT INTO audit_logs (user_id, action, table_name, record_id, changes, ip_address, user_agent) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'SETUP', 'system', 'live-data-setup', '{"action": "Live data setup completed", "users_created": 14, "branches_created": 3, "categories_created": 10}', '127.0.0.1', 'YalaOffice Setup Script');

-- Setup completed successfully
-- Next step: Run the product population script
