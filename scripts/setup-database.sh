#!/bin/bash

# YalaOffice Database Setup Script
# This script sets up the complete database with live data for production use

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_DIR/database-setup.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC} $message" | tee -a "$LOG_FILE" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "INFO" "Checking prerequisites..."
    
    # Check if Supabase CLI is installed
    if ! command -v supabase &> /dev/null; then
        log "WARN" "Supabase CLI not found. Installing..."
        npm install -g supabase
    fi
    
    # Check if psql is available (for direct database operations)
    if ! command -v psql &> /dev/null; then
        log "WARN" "PostgreSQL client (psql) not found. Some operations may be limited."
    fi
    
    # Check environment variables
    if [[ -z "${SUPABASE_URL:-}" ]]; then
        error_exit "SUPABASE_URL environment variable is not set"
    fi
    
    if [[ -z "${SUPABASE_ANON_KEY:-}" ]]; then
        error_exit "SUPABASE_ANON_KEY environment variable is not set"
    fi
    
    if [[ -z "${SUPABASE_SERVICE_KEY:-}" ]]; then
        error_exit "SUPABASE_SERVICE_KEY environment variable is not set"
    fi
    
    log "SUCCESS" "Prerequisites check completed"
}

# Initialize Supabase project
init_supabase() {
    log "INFO" "Initializing Supabase project..."
    
    cd "$PROJECT_DIR"
    
    # Initialize Supabase if not already done
    if [[ ! -f "supabase/config.toml" ]]; then
        supabase init || error_exit "Failed to initialize Supabase project"
        log "SUCCESS" "Supabase project initialized"
    else
        log "INFO" "Supabase project already initialized"
    fi
    
    # Link to remote project
    log "INFO" "Linking to remote Supabase project..."
    supabase link --project-ref "${SUPABASE_URL##*/}" || log "WARN" "Failed to link project (may already be linked)"
}

# Create database schema
create_schema() {
    log "INFO" "Creating database schema..."
    
    # Check if schema file exists
    local schema_file="$SCRIPT_DIR/database-schema.sql"
    if [[ ! -f "$schema_file" ]]; then
        log "WARN" "Database schema file not found. Creating basic schema..."
        create_basic_schema
    else
        log "INFO" "Applying database schema from $schema_file"
        supabase db push || error_exit "Failed to apply database schema"
    fi
    
    log "SUCCESS" "Database schema created successfully"
}

# Create basic schema if file doesn't exist
create_basic_schema() {
    cat > "$SCRIPT_DIR/database-schema.sql" << 'EOF'
-- YalaOffice Database Schema
-- Basic schema for live data integration

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    user_type VARCHAR(50) NOT NULL CHECK (user_type IN ('admin', 'manager', 'client', 'reseller', 'delivery_person')),
    phone VARCHAR(20),
    city VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    is_company BOOLEAN DEFAULT false,
    company_name VARCHAR(255),
    ice_number VARCHAR(50),
    company_address TEXT,
    company_phone VARCHAR(20),
    company_city VARCHAR(100),
    company_email VARCHAR(255),
    tax_id VARCHAR(50),
    legal_form VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    level INTEGER DEFAULT 0,
    parent_id UUID REFERENCES categories(id),
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    icon VARCHAR(50),
    color VARCHAR(7),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    sku VARCHAR(100) UNIQUE NOT NULL,
    category_id UUID REFERENCES categories(id),
    brand VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    reseller_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    featured_image TEXT,
    stock INTEGER DEFAULT 0,
    min_stock INTEGER DEFAULT 0,
    weight DECIMAL(8,3),
    is_active BOOLEAN DEFAULT true,
    is_new BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Branches table
CREATE TABLE IF NOT EXISTS branches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    address JSONB,
    contact JSONB,
    coordinates JSONB,
    is_active BOOLEAN DEFAULT true,
    is_main_branch BOOLEAN DEFAULT false,
    operating_hours JSONB,
    services TEXT[],
    capacity JSONB,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer profiles table
CREATE TABLE IF NOT EXISTS customer_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) UNIQUE,
    discount_rate DECIMAL(5,2) DEFAULT 0.00,
    credit_limit DECIMAL(10,2) DEFAULT 0.00,
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(12,2) DEFAULT 0.00,
    last_order_date TIMESTAMP WITH TIME ZONE,
    loyalty_points INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID REFERENCES users(id),
    branch_id UUID REFERENCES branches(id),
    status VARCHAR(20) DEFAULT 'pending',
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    total DECIMAL(10,2) NOT NULL,
    payment_status VARCHAR(20) DEFAULT 'pending',
    payment_method VARCHAR(50),
    delivery_address JSONB,
    notes TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Branch stock table
CREATE TABLE IF NOT EXISTS branch_stock (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    branch_id UUID REFERENCES branches(id),
    product_id UUID REFERENCES products(id),
    stock_quantity INTEGER DEFAULT 0,
    reserved_quantity INTEGER DEFAULT 0,
    min_stock_level INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(branch_id, product_id)
);

-- Branch assignments table
CREATE TABLE IF NOT EXISTS branch_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    branch_id UUID REFERENCES branches(id),
    user_id UUID REFERENCES users(id),
    role VARCHAR(50) NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(branch_id, user_id, role)
);

-- System configurations table
CREATE TABLE IF NOT EXISTS system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(100) NOT NULL,
    key VARCHAR(100) NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    data_type VARCHAR(20) DEFAULT 'string',
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(category, key)
);

-- Audit logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id VARCHAR(100),
    changes JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku);
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_orders_customer ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_date ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_branch_stock_branch ON branch_stock(branch_id);
CREATE INDEX IF NOT EXISTS idx_branch_stock_product ON branch_stock(product_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_table ON audit_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_logs_date ON audit_logs(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (basic examples)
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND user_type = 'admin'
        )
    );

CREATE POLICY "Everyone can view active products" ON products
    FOR SELECT USING (is_active = true);

CREATE POLICY "Users can view their own orders" ON orders
    FOR SELECT USING (customer_id = auth.uid());

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON branches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
EOF

    log "INFO" "Basic schema file created"
}

# Populate live data
populate_live_data() {
    log "INFO" "Populating database with live data..."
    
    # Run live data setup script
    local live_data_script="$SCRIPT_DIR/setup-live-data.sql"
    if [[ -f "$live_data_script" ]]; then
        log "INFO" "Running live data setup script..."
        supabase db reset --linked || error_exit "Failed to reset database"
        psql "$DATABASE_URL" -f "$live_data_script" || error_exit "Failed to populate live data"
    else
        error_exit "Live data script not found: $live_data_script"
    fi
    
    # Run product population script
    local products_script="$SCRIPT_DIR/populate-products.sql"
    if [[ -f "$products_script" ]]; then
        log "INFO" "Running product population script..."
        psql "$DATABASE_URL" -f "$products_script" || error_exit "Failed to populate products"
    else
        error_exit "Product population script not found: $products_script"
    fi
    
    log "SUCCESS" "Live data populated successfully"
}

# Verify database setup
verify_setup() {
    log "INFO" "Verifying database setup..."
    
    # Check if tables exist and have data
    local tables=("users" "products" "categories" "branches" "orders")
    
    for table in "${tables[@]}"; do
        local count=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM $table;" 2>/dev/null || echo "0")
        count=$(echo "$count" | tr -d ' ')
        
        if [[ "$count" -gt 0 ]]; then
            log "SUCCESS" "Table '$table' has $count records"
        else
            log "WARN" "Table '$table' is empty or doesn't exist"
        fi
    done
    
    # Test basic queries
    log "INFO" "Testing basic database queries..."
    
    # Test user query
    local admin_count=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM users WHERE user_type = 'admin';" 2>/dev/null || echo "0")
    admin_count=$(echo "$admin_count" | tr -d ' ')
    log "INFO" "Admin users: $admin_count"
    
    # Test product query
    local product_count=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM products WHERE is_active = true;" 2>/dev/null || echo "0")
    product_count=$(echo "$product_count" | tr -d ' ')
    log "INFO" "Active products: $product_count"
    
    # Test category query
    local category_count=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM categories WHERE is_active = true;" 2>/dev/null || echo "0")
    category_count=$(echo "$category_count" | tr -d ' ')
    log "INFO" "Active categories: $category_count"
    
    log "SUCCESS" "Database verification completed"
}

# Generate connection info
generate_connection_info() {
    log "INFO" "Generating connection information..."
    
    cat > "$PROJECT_DIR/.env.database" << EOF
# YalaOffice Database Connection Information
# Generated on $(date)

# Supabase Configuration
SUPABASE_URL=${SUPABASE_URL}
SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}

# Database Direct Connection (if needed)
DATABASE_URL=${DATABASE_URL:-}

# Connection Status
DATABASE_SETUP_COMPLETED=true
DATABASE_SETUP_DATE=$(date -Iseconds)

# Live Data Statistics
TOTAL_USERS=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM users;" 2>/dev/null | tr -d ' ' || echo "0")
TOTAL_PRODUCTS=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM products;" 2>/dev/null | tr -d ' ' || echo "0")
TOTAL_CATEGORIES=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM categories;" 2>/dev/null | tr -d ' ' || echo "0")
TOTAL_BRANCHES=$(psql "$DATABASE_URL" -t -c "SELECT COUNT(*) FROM branches;" 2>/dev/null | tr -d ' ' || echo "0")
EOF

    log "SUCCESS" "Connection information saved to .env.database"
}

# Main setup function
main() {
    log "INFO" "Starting YalaOffice database setup..."
    
    # Load environment variables
    if [[ -f "$PROJECT_DIR/.env" ]]; then
        source "$PROJECT_DIR/.env"
    fi
    
    if [[ -f "$PROJECT_DIR/.env.local" ]]; then
        source "$PROJECT_DIR/.env.local"
    fi
    
    # Run setup steps
    check_prerequisites
    init_supabase
    create_schema
    populate_live_data
    verify_setup
    generate_connection_info
    
    log "SUCCESS" "Database setup completed successfully!"
    
    # Print summary
    echo
    echo "========================================="
    echo "Database Setup Summary:"
    echo "- Supabase project linked"
    echo "- Schema created and applied"
    echo "- Live data populated"
    echo "- 14 users created (2 admin, 2 manager, 4 client, 4 reseller, 2 delivery)"
    echo "- 3 branches in Tetouan created"
    echo "- 10 product categories created"
    echo "- 44+ products populated"
    echo "- Connection info saved to .env.database"
    echo "========================================="
    echo
    echo "Next steps:"
    echo "1. Update your application to use the live data service"
    echo "2. Test the database connection"
    echo "3. Run the migration script to replace mock data"
    echo "4. Deploy to production"
}

# Run main function
main "$@"
