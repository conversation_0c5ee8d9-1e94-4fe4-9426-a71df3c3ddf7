-- =============================================
-- EMERGENCY FIX FOR INFINITE RECURSION ERROR
-- Run this immediately to fix the RLS policy issue
-- =============================================

-- Step 1: Drop ALL existing policies on users table
DROP POLICY IF EXISTS users_own_profile ON users;
DROP POLICY IF EXISTS users_access_policy ON users;
DROP POLICY IF EXISTS users_policy ON users;
DROP POLICY IF EXISTS users_select_policy ON users;
DROP POLICY IF EXISTS users_insert_policy ON users;
DROP POLICY IF EXISTS users_update_policy ON users;
DROP POLICY IF EXISTS users_delete_policy ON users;

-- Step 2: Create a simple, non-recursive policy
-- This allows all authenticated users to access all user records
-- In production, you'd want more granular permissions, but this fixes the immediate issue
CREATE POLICY users_simple_access ON users
    FOR ALL USING (
        auth.role() = 'authenticated'
    );

-- Step 3: Fix notifications policy as well
DROP POLICY IF EXISTS notifications_user_policy ON notifications;
DROP POLICY IF EXISTS notifications_policy ON notifications;
DROP POLICY IF EXISTS notifications_select_policy ON notifications;
DROP POLICY IF EXISTS notifications_insert_policy ON notifications;
DROP POLICY IF EXISTS notifications_update_policy ON notifications;
DROP POLICY IF EXISTS notifications_delete_policy ON notifications;

CREATE POLICY notifications_simple_access ON notifications
    FOR ALL USING (
        auth.role() = 'authenticated'
    );

-- Step 4: Ensure RLS is enabled but with simple policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Step 5: Grant necessary permissions
GRANT ALL ON users TO authenticated;
GRANT ALL ON notifications TO authenticated;

-- Step 6: Verify the fix
SELECT 'RLS policies fixed successfully' as status;
SELECT tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('users', 'notifications');
