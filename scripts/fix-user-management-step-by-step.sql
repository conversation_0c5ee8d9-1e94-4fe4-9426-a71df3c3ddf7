-- =============================================
-- STEP-BY-STEP FIX FOR USER MANAGEMENT
-- Run each section separately to avoid conflicts
-- =============================================

-- STEP 1: Drop all existing policies on users table
-- =============================================
DROP POLICY IF EXISTS users_own_profile ON users;
DROP POLICY IF EXISTS users_access_policy ON users;
DROP POLICY IF EXISTS users_policy ON users;

-- STEP 2: Create new admin-friendly policy
-- =============================================
CREATE POLICY users_access_policy ON users
    FOR ALL USING (
        auth.uid() = id OR
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_type IN ('admin', 'manager')
            AND is_active = true
        )
    );

-- STEP 3: Create RPC function for admin user access
-- =============================================
CREATE OR REPLACE FUNCTION get_all_users_admin()
RETURNS TABLE (
    id UUID,
    email VARCHAR(255),
    full_name VARCHAR(255),
    user_type VARCHAR(20),
    phone VARCHAR(20),
    city VARCHAR(100),
    is_active BOOLEAN,
    is_company BOOLEAN,
    company_name VARCHAR(255),
    ice_number VARCHAR(20),
    company_address TEXT,
    company_phone VARCHAR(20),
    company_city VARCHAR(100),
    company_email VARCHAR(255),
    tax_id VARCHAR(50),
    legal_form VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_type VARCHAR(20);
BEGIN
    -- Check if current user is admin or manager
    SELECT u.user_type INTO current_user_type
    FROM users u
    WHERE u.id = auth.uid()
    AND u.is_active = true;
    
    -- Only allow admin and manager users to access all users
    IF current_user_type IN ('admin', 'manager') THEN
        RETURN QUERY
        SELECT u.id, u.email, u.full_name, u.user_type, u.phone, u.city,
               u.is_active, u.is_company, u.company_name, u.ice_number,
               u.company_address, u.company_phone, u.company_city,
               u.company_email, u.tax_id, u.legal_form,
               u.created_at, u.updated_at, u.last_login
        FROM users u
        ORDER BY u.created_at DESC;
    ELSE
        -- Regular users can only see their own profile
        RETURN QUERY
        SELECT u.id, u.email, u.full_name, u.user_type, u.phone, u.city,
               u.is_active, u.is_company, u.company_name, u.ice_number,
               u.company_address, u.company_phone, u.company_city,
               u.company_email, u.tax_id, u.legal_form,
               u.created_at, u.updated_at, u.last_login
        FROM users u
        WHERE u.id = auth.uid();
    END IF;
END;
$$;

-- STEP 4: Create user statistics function
-- =============================================
CREATE OR REPLACE FUNCTION get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_type VARCHAR(20);
    stats JSON;
BEGIN
    -- Check if current user is admin or manager
    SELECT u.user_type INTO current_user_type
    FROM users u
    WHERE u.id = auth.uid()
    AND u.is_active = true;
    
    -- Only allow admin and manager users to access statistics
    IF current_user_type IN ('admin', 'manager') THEN
        SELECT json_build_object(
            'total', COUNT(*),
            'admins', COUNT(*) FILTER (WHERE user_type = 'admin'),
            'managers', COUNT(*) FILTER (WHERE user_type = 'manager'),
            'clients', COUNT(*) FILTER (WHERE user_type = 'client'),
            'resellers', COUNT(*) FILTER (WHERE user_type = 'reseller'),
            'delivery', COUNT(*) FILTER (WHERE user_type = 'delivery_person'),
            'active', COUNT(*) FILTER (WHERE is_active = true),
            'inactive', COUNT(*) FILTER (WHERE is_active = false),
            'recentlyCreated', COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days')
        ) INTO stats
        FROM users;
        
        RETURN stats;
    ELSE
        -- Return empty stats for non-admin users
        RETURN json_build_object(
            'total', 0,
            'admins', 0,
            'managers', 0,
            'clients', 0,
            'resellers', 0,
            'delivery', 0,
            'active', 0,
            'inactive', 0,
            'recentlyCreated', 0
        );
    END IF;
END;
$$;

-- STEP 5: Grant permissions
-- =============================================
GRANT EXECUTE ON FUNCTION get_all_users_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_statistics() TO authenticated;

-- STEP 6: Sync existing auth users to users table
-- =============================================
CREATE OR REPLACE FUNCTION sync_existing_auth_users()
RETURNS TEXT AS $$
DECLARE
    auth_user RECORD;
    result_text TEXT := '';
    user_count INTEGER := 0;
BEGIN
    -- Loop through auth users that don't exist in users table
    FOR auth_user IN 
        SELECT au.id, au.email, au.raw_user_meta_data, au.created_at
        FROM auth.users au
        LEFT JOIN users u ON au.id = u.id
        WHERE u.id IS NULL
    LOOP
        -- Insert missing user
        INSERT INTO users (
            id,
            email,
            full_name,
            user_type,
            phone,
            city,
            is_active,
            created_at,
            updated_at
        ) VALUES (
            auth_user.id,
            auth_user.email,
            COALESCE(auth_user.raw_user_meta_data->>'full_name', auth_user.raw_user_meta_data->>'fullName', split_part(auth_user.email, '@', 1)),
            COALESCE(auth_user.raw_user_meta_data->>'user_type', auth_user.raw_user_meta_data->>'userType', 'admin'),
            COALESCE(auth_user.raw_user_meta_data->>'phone', '+212 6 12 34 56 78'),
            COALESCE(auth_user.raw_user_meta_data->>'city', 'Tetouan'),
            true,
            auth_user.created_at,
            NOW()
        );
        
        user_count := user_count + 1;
        result_text := result_text || 'Synced user: ' || auth_user.email || E'\n';
    END LOOP;
    
    result_text := result_text || 'Total users synced: ' || user_count;
    RETURN result_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Run the sync function
SELECT sync_existing_auth_users();

-- STEP 7: Verification
-- =============================================
SELECT 'Current users in database:' as test;
SELECT user_type, COUNT(*) as count, 
       COUNT(*) FILTER (WHERE is_active = true) as active_count
FROM users 
GROUP BY user_type 
ORDER BY user_type;

-- Check auth vs users sync
SELECT 'Auth vs Users sync check:' as test;
SELECT 
  (SELECT COUNT(*) FROM auth.users) as auth_users_count,
  (SELECT COUNT(*) FROM users) as users_table_count,
  (SELECT COUNT(*) FROM auth.users au LEFT JOIN users u ON au.id = u.id WHERE u.id IS NULL) as missing_users;
