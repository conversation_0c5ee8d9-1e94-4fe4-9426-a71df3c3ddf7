-- =============================================
-- SETUP STORAGE BUCKET AND POLICIES
-- Run this after creating the product-images bucket
-- =============================================

-- Create storage bucket for product images
-- Note: You should create the bucket manually in Supabase Dashboard > Storage first
-- This script only sets up the policies

-- Verify bucket exists (this will show an error if bucket doesn't exist, which is expected)
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'product-images') THEN
    RAISE NOTICE 'Bucket "product-images" does not exist. Please create it manually in Supabase Dashboard > Storage';
    RAISE NOTICE 'Bucket settings: Name=product-images, Public=true, File size limit=50MB, MIME types=image/*';
  ELSE
    RAISE NOTICE 'Bucket "product-images" found. Setting up policies...';
  END IF;
END $$;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated users to upload images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access to product images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete images" ON storage.objects;

-- Policy to allow authenticated users to upload files
CREATE POLICY "Allow authenticated users to upload images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
  );

-- Policy to allow public read access to images
CREATE POLICY "Allow public read access to product images" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'product-images'
  );

-- Policy to allow authenticated users to update their uploaded images
CREATE POLICY "Allow authenticated users to update images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
  );

-- Policy to allow authenticated users to delete images
CREATE POLICY "Allow authenticated users to delete images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'product-images' AND
    auth.role() = 'authenticated'
  );

-- Verify the setup
SELECT 'Storage policies created successfully' as status;

-- Check if bucket exists
SELECT
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets
WHERE id = 'product-images';

-- List storage policies (using correct table name)
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies
WHERE schemaname = 'storage' AND tablename = 'objects'
AND policyname LIKE '%product%' OR policyname LIKE '%image%';
