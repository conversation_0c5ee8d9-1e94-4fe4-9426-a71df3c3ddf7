/**
 * Migration Script: Replace Mock Data with Live Supabase Integration
 * This script identifies and updates all services to use live data
 */

import fs from 'fs';
import path from 'path';

interface MockDataLocation {
  file: string;
  type: 'service' | 'component' | 'hook';
  mockDataPatterns: string[];
  replacementNeeded: boolean;
}

class LiveDataMigration {
  private srcPath: string;
  private mockDataLocations: MockDataLocation[] = [];

  constructor() {
    this.srcPath = path.join(process.cwd(), 'src');
  }

  // Scan for mock data patterns
  scanForMockData(): void {
    console.log('🔍 Scanning for mock data patterns...');
    
    const mockPatterns = [
      /const\s+mock\w+\s*=\s*\[/g,
      /const\s+demo\w+\s*=\s*\[/g,
      /const\s+sample\w+\s*=\s*\[/g,
      /const\s+fake\w+\s*=\s*\[/g,
      /\/\*\*\s*Mock\s+data/g,
      /\/\/\s*Mock\s+data/g,
      /generateMock\w+/g,
      /createFake\w+/g,
    ];

    this.scanDirectory(this.srcPath, mockPatterns);
    
    console.log(`📊 Found ${this.mockDataLocations.length} files with mock data patterns`);
    this.mockDataLocations.forEach(location => {
      console.log(`   - ${location.file} (${location.type})`);
    });
  }

  private scanDirectory(dirPath: string, patterns: RegExp[]): void {
    const items = fs.readdirSync(dirPath);

    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        this.scanDirectory(fullPath, patterns);
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
        this.scanFile(fullPath, patterns);
      }
    });
  }

  private scanFile(filePath: string, patterns: RegExp[]): void {
    const content = fs.readFileSync(filePath, 'utf-8');
    const relativePath = path.relative(this.srcPath, filePath);
    
    const foundPatterns: string[] = [];
    patterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        foundPatterns.push(...matches);
      }
    });

    if (foundPatterns.length > 0) {
      let type: 'service' | 'component' | 'hook' = 'component';
      
      if (filePath.includes('/services/')) {
        type = 'service';
      } else if (filePath.includes('/hooks/')) {
        type = 'hook';
      }

      this.mockDataLocations.push({
        file: relativePath,
        type,
        mockDataPatterns: foundPatterns,
        replacementNeeded: true
      });
    }
  }

  // Generate replacement templates
  generateReplacements(): void {
    console.log('\n🔧 Generating replacement templates...');

    const serviceReplacements = this.generateServiceReplacements();
    const componentReplacements = this.generateComponentReplacements();
    const hookReplacements = this.generateHookReplacements();

    // Write replacement templates to files
    this.writeReplacementFile('service-replacements.ts', serviceReplacements);
    this.writeReplacementFile('component-replacements.ts', componentReplacements);
    this.writeReplacementFile('hook-replacements.ts', hookReplacements);

    console.log('✅ Replacement templates generated in scripts/replacements/');
  }

  private generateServiceReplacements(): string {
    return `
// Service Replacements for Live Data Integration
// Replace mock data services with Supabase integration

import { liveDataService } from '../src/services/liveDataService';

// =============================================
// USER SERVICE REPLACEMENT
// =============================================

// OLD: Mock user data
// const mockUsers = [{ id: '1', name: 'John Doe', ... }];

// NEW: Live user data
export const userService = {
  async getAllUsers() {
    return await liveDataService.getAllUsers();
  },
  
  async getUsersByType(userType: string) {
    return await liveDataService.getUsersByType(userType);
  },
  
  async createUser(userData: any) {
    return await liveDataService.createUser(userData);
  },
  
  async updateUser(id: string, updates: any) {
    return await liveDataService.updateUser(id, updates);
  },
  
  async deleteUser(id: string) {
    return await liveDataService.deleteUser(id);
  }
};

// =============================================
// PRODUCT SERVICE REPLACEMENT
// =============================================

// OLD: Mock product data
// const mockProducts = [{ id: '1', title: 'Product 1', ... }];

// NEW: Live product data
export const productService = {
  async getAllProducts() {
    return await liveDataService.getAllProducts();
  },
  
  async getProductsByCategory(categoryId: string) {
    return await liveDataService.getProductsByCategory(categoryId);
  },
  
  async searchProducts(query: string) {
    return await liveDataService.searchProducts(query);
  },
  
  async createProduct(productData: any) {
    return await liveDataService.createProduct(productData);
  },
  
  async updateProduct(id: string, updates: any) {
    return await liveDataService.updateProduct(id, updates);
  },
  
  async updateProductStock(id: string, newStock: number) {
    return await liveDataService.updateProductStock(id, newStock);
  }
};

// =============================================
// ORDER SERVICE REPLACEMENT
// =============================================

// OLD: Mock order data
// const mockOrders = [{ id: '1', customer: 'Customer 1', ... }];

// NEW: Live order data
export const orderService = {
  async getAllOrders() {
    return await liveDataService.getAllOrders();
  },
  
  async getOrdersByStatus(status: string) {
    return await liveDataService.getOrdersByStatus(status);
  },
  
  async getOrderById(id: string) {
    return await liveDataService.getOrderById(id);
  }
};

// =============================================
// CUSTOMER SERVICE REPLACEMENT
// =============================================

// OLD: Mock customer data
// const mockCustomers = [{ id: '1', name: 'Customer 1', ... }];

// NEW: Live customer data
export const customerService = {
  async getAllCustomers() {
    return await liveDataService.getAllCustomers();
  },
  
  async getCustomerById(id: string) {
    return await liveDataService.getCustomerById(id);
  }
};

// =============================================
// BRANCH SERVICE REPLACEMENT
// =============================================

// OLD: Mock branch data
// const mockBranches = [{ id: '1', name: 'Branch 1', ... }];

// NEW: Live branch data
export const branchService = {
  async getAllBranches() {
    return await liveDataService.getAllBranches();
  },
  
  async getBranchById(id: string) {
    return await liveDataService.getBranchById(id);
  },
  
  async getMainBranch() {
    return await liveDataService.getMainBranch();
  }
};

// =============================================
// CATEGORY SERVICE REPLACEMENT
// =============================================

// OLD: Mock category data
// const mockCategories = [{ id: '1', name: 'Category 1', ... }];

// NEW: Live category data
export const categoryService = {
  async getAllCategories() {
    return await liveDataService.getAllCategories();
  },
  
  async getCategoryById(id: string) {
    return await liveDataService.getCategoryById(id);
  }
};
`;
  }

  private generateComponentReplacements(): string {
    return `
// Component Replacements for Live Data Integration
// Update components to use live data hooks

import { useState, useEffect } from 'react';
import { liveDataService } from '../src/services/liveDataService';

// =============================================
// DASHBOARD COMPONENT REPLACEMENT
// =============================================

// OLD: Mock dashboard data
// const mockStats = { totalProducts: 100, totalOrders: 50, ... };

// NEW: Live dashboard data hook
export const useDashboardStats = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const dashboardStats = await liveDataService.getDashboardStats();
        setStats(dashboardStats);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return { stats, loading, error };
};

// =============================================
// PRODUCT LIST COMPONENT REPLACEMENT
// =============================================

// OLD: Mock product list
// const mockProducts = [{ id: '1', title: 'Product 1', ... }];

// NEW: Live product list hook
export const useProducts = (categoryId?: string) => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const productData = categoryId 
          ? await liveDataService.getProductsByCategory(categoryId)
          : await liveDataService.getAllProducts();
        setProducts(productData);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToProducts((payload) => {
      // Handle real-time updates
      fetchProducts();
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [categoryId]);

  return { products, loading, error };
};

// =============================================
// ORDER LIST COMPONENT REPLACEMENT
// =============================================

// OLD: Mock order list
// const mockOrders = [{ id: '1', customer: 'Customer 1', ... }];

// NEW: Live order list hook
export const useOrders = (status?: string) => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const orderData = status 
          ? await liveDataService.getOrdersByStatus(status)
          : await liveDataService.getAllOrders();
        setOrders(orderData);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToOrders((payload) => {
      // Handle real-time updates
      fetchOrders();
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [status]);

  return { orders, loading, error };
};

// =============================================
// CUSTOMER LIST COMPONENT REPLACEMENT
// =============================================

// OLD: Mock customer list
// const mockCustomers = [{ id: '1', name: 'Customer 1', ... }];

// NEW: Live customer list hook
export const useCustomers = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true);
        const customerData = await liveDataService.getAllCustomers();
        setCustomers(customerData);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchCustomers();

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToUsers((payload) => {
      // Handle real-time updates for customer users
      if (['client', 'reseller'].includes(payload.new?.user_type)) {
        fetchCustomers();
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return { customers, loading, error };
};
`;
  }

  private generateHookReplacements(): string {
    return `
// Hook Replacements for Live Data Integration
// Custom hooks for live data management

import { useState, useEffect, useCallback } from 'react';
import { liveDataService } from '../src/services/liveDataService';

// =============================================
// LIVE DATA HOOKS
// =============================================

// Generic live data hook
export const useLiveData = <T>(
  fetchFunction: () => Promise<T[]>,
  dependencies: any[] = []
) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await fetchFunction();
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch };
};

// Specific hooks for different data types
export const useLiveProducts = (categoryId?: string) => {
  return useLiveData(
    () => categoryId 
      ? liveDataService.getProductsByCategory(categoryId)
      : liveDataService.getAllProducts(),
    [categoryId]
  );
};

export const useLiveOrders = (status?: string) => {
  return useLiveData(
    () => status 
      ? liveDataService.getOrdersByStatus(status)
      : liveDataService.getAllOrders(),
    [status]
  );
};

export const useLiveCustomers = () => {
  return useLiveData(
    () => liveDataService.getAllCustomers(),
    []
  );
};

export const useLiveUsers = (userType?: string) => {
  return useLiveData(
    () => userType 
      ? liveDataService.getUsersByType(userType)
      : liveDataService.getAllUsers(),
    [userType]
  );
};

export const useLiveBranches = () => {
  return useLiveData(
    () => liveDataService.getAllBranches(),
    []
  );
};

export const useLiveCategories = () => {
  return useLiveData(
    () => liveDataService.getAllCategories(),
    []
  );
};

// Real-time subscription hooks
export const useRealtimeProducts = () => {
  const [products, setProducts] = useState([]);

  useEffect(() => {
    // Initial fetch
    liveDataService.getAllProducts().then(setProducts);

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToProducts((payload) => {
      // Handle real-time updates
      liveDataService.getAllProducts().then(setProducts);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return products;
};

export const useRealtimeOrders = () => {
  const [orders, setOrders] = useState([]);

  useEffect(() => {
    // Initial fetch
    liveDataService.getAllOrders().then(setOrders);

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToOrders((payload) => {
      // Handle real-time updates
      liveDataService.getAllOrders().then(setOrders);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return orders;
};
`;
  }

  private writeReplacementFile(filename: string, content: string): void {
    const replacementsDir = path.join(process.cwd(), 'scripts', 'replacements');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(replacementsDir)) {
      fs.mkdirSync(replacementsDir, { recursive: true });
    }

    const filePath = path.join(replacementsDir, filename);
    fs.writeFileSync(filePath, content);
    console.log(`   ✅ Generated ${filename}`);
  }

  // Execute migration
  async executeMigration(): Promise<void> {
    console.log('\n🚀 Starting live data migration...');
    
    // Step 1: Scan for mock data
    this.scanForMockData();
    
    // Step 2: Generate replacements
    this.generateReplacements();
    
    // Step 3: Create migration summary
    this.createMigrationSummary();
    
    console.log('\n✅ Migration preparation completed!');
    console.log('\n📋 Next steps:');
    console.log('   1. Review generated replacement files in scripts/replacements/');
    console.log('   2. Run database setup: npm run setup:database');
    console.log('   3. Apply replacements to your services and components');
    console.log('   4. Test the live data integration');
    console.log('   5. Remove old mock data files');
  }

  private createMigrationSummary(): void {
    const summary = {
      timestamp: new Date().toISOString(),
      mockDataLocations: this.mockDataLocations,
      totalFiles: this.mockDataLocations.length,
      serviceFiles: this.mockDataLocations.filter(l => l.type === 'service').length,
      componentFiles: this.mockDataLocations.filter(l => l.type === 'component').length,
      hookFiles: this.mockDataLocations.filter(l => l.type === 'hook').length,
      migrationSteps: [
        'Database setup with live data',
        'Service layer replacement',
        'Component hook updates',
        'Real-time subscription setup',
        'Mock data cleanup'
      ]
    };

    const summaryPath = path.join(process.cwd(), 'scripts', 'migration-summary.json');
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    console.log('   ✅ Generated migration-summary.json');
  }
}

// Execute migration if run directly
if (require.main === module) {
  const migration = new LiveDataMigration();
  migration.executeMigration().catch(console.error);
}

export default LiveDataMigration;
`;
  }

  private generateHookReplacements(): string {
    return `
// Hook Replacements for Live Data Integration
// Custom hooks for live data management

import { useState, useEffect, useCallback } from 'react';
import { liveDataService } from '../src/services/liveDataService';

// =============================================
// LIVE DATA HOOKS
// =============================================

// Generic live data hook
export const useLiveData = <T>(
  fetchFunction: () => Promise<T[]>,
  dependencies: any[] = []
) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await fetchFunction();
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch };
};

// Specific hooks for different data types
export const useLiveProducts = (categoryId?: string) => {
  return useLiveData(
    () => categoryId 
      ? liveDataService.getProductsByCategory(categoryId)
      : liveDataService.getAllProducts(),
    [categoryId]
  );
};

export const useLiveOrders = (status?: string) => {
  return useLiveData(
    () => status 
      ? liveDataService.getOrdersByStatus(status)
      : liveDataService.getAllOrders(),
    [status]
  );
};

export const useLiveCustomers = () => {
  return useLiveData(
    () => liveDataService.getAllCustomers(),
    []
  );
};

export const useLiveUsers = (userType?: string) => {
  return useLiveData(
    () => userType 
      ? liveDataService.getUsersByType(userType)
      : liveDataService.getAllUsers(),
    [userType]
  );
};

export const useLiveBranches = () => {
  return useLiveData(
    () => liveDataService.getAllBranches(),
    []
  );
};

export const useLiveCategories = () => {
  return useLiveData(
    () => liveDataService.getAllCategories(),
    []
  );
};

// Real-time subscription hooks
export const useRealtimeProducts = () => {
  const [products, setProducts] = useState([]);

  useEffect(() => {
    // Initial fetch
    liveDataService.getAllProducts().then(setProducts);

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToProducts((payload) => {
      // Handle real-time updates
      liveDataService.getAllProducts().then(setProducts);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return products;
};

export const useRealtimeOrders = () => {
  const [orders, setOrders] = useState([]);

  useEffect(() => {
    // Initial fetch
    liveDataService.getAllOrders().then(setOrders);

    // Set up real-time subscription
    const subscription = liveDataService.subscribeToOrders((payload) => {
      // Handle real-time updates
      liveDataService.getAllOrders().then(setOrders);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return orders;
};
`;
  }

  private writeReplacementFile(filename: string, content: string): void {
    const replacementsDir = path.join(process.cwd(), 'scripts', 'replacements');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(replacementsDir)) {
      fs.mkdirSync(replacementsDir, { recursive: true });
    }

    const filePath = path.join(replacementsDir, filename);
    fs.writeFileSync(filePath, content);
    console.log(`   ✅ Generated ${filename}`);
  }

  // Execute migration
  async executeMigration(): Promise<void> {
    console.log('\n🚀 Starting live data migration...');
    
    // Step 1: Scan for mock data
    this.scanForMockData();
    
    // Step 2: Generate replacements
    this.generateReplacements();
    
    // Step 3: Create migration summary
    this.createMigrationSummary();
    
    console.log('\n✅ Migration preparation completed!');
    console.log('\n📋 Next steps:');
    console.log('   1. Review generated replacement files in scripts/replacements/');
    console.log('   2. Run database setup: npm run setup:database');
    console.log('   3. Apply replacements to your services and components');
    console.log('   4. Test the live data integration');
    console.log('   5. Remove old mock data files');
  }

  private createMigrationSummary(): void {
    const summary = {
      timestamp: new Date().toISOString(),
      mockDataLocations: this.mockDataLocations,
      totalFiles: this.mockDataLocations.length,
      serviceFiles: this.mockDataLocations.filter(l => l.type === 'service').length,
      componentFiles: this.mockDataLocations.filter(l => l.type === 'component').length,
      hookFiles: this.mockDataLocations.filter(l => l.type === 'hook').length,
      migrationSteps: [
        'Database setup with live data',
        'Service layer replacement',
        'Component hook updates',
        'Real-time subscription setup',
        'Mock data cleanup'
      ]
    };

    const summaryPath = path.join(process.cwd(), 'scripts', 'migration-summary.json');
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    console.log('   ✅ Generated migration-summary.json');
  }
}

// Execute migration if run directly
if (require.main === module) {
  const migration = new LiveDataMigration();
  migration.executeMigration().catch(console.error);
}

export default LiveDataMigration;
