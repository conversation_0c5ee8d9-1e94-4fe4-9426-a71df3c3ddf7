/**
 * Fix User Email Confirmation Issue
 * Updates all test users to bypass email confirmation
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://umzikqwughlzkiarldoa.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtemlrcXd1Z2hsemtpYXJsZG9hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMjczMjgsImV4cCI6MjA2NTcwMzMyOH0.3YYeZWUp3c0IIwlORbgmrHlcPoyk5iasRnmGOEHTvoY';

// You'll need the service key for admin operations
// This should be set as an environment variable in production
const supabaseServiceKey = 'your-service-key-here'; // Replace with actual service key

const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function fixUserConfirmation() {
  console.log('🔧 Fixing User Email Confirmation Issues...\n');

  // Test users that need confirmation bypass
  const testUsers = [
    { email: '<EMAIL>', password: 'YalaAdmin2024!', type: 'Admin' },
    { email: '<EMAIL>', password: 'YalaAdmin2024!', type: 'Admin' },
    { email: '<EMAIL>', password: 'YalaManager2024!', type: 'Manager' },
    { email: '<EMAIL>', password: 'YalaManager2024!', type: 'Manager' },
    { email: '<EMAIL>', password: 'Client2024!', type: 'Client' },
    { email: '<EMAIL>', password: 'Client2024!', type: 'Client' },
    { email: '<EMAIL>', password: 'Client2024!', type: 'Client' },
    { email: '<EMAIL>', password: 'Client2024!', type: 'Client' },
    { email: '<EMAIL>', password: 'Reseller2024!', type: 'Reseller' },
    { email: '<EMAIL>', password: 'Reseller2024!', type: 'Reseller' },
    { email: '<EMAIL>', password: 'Reseller2024!', type: 'Reseller' },
    { email: '<EMAIL>', password: 'Reseller2024!', type: 'Reseller' }
  ];

  console.log('📋 Method 1: Recreating users with email confirmation disabled...\n');

  for (const user of testUsers) {
    try {
      console.log(`Processing ${user.type}: ${user.email}`);

      // Try to sign up the user with email confirmation disabled
      const { data, error } = await supabaseAdmin.auth.admin.createUser({
        email: user.email,
        password: user.password,
        email_confirm: true, // This bypasses email confirmation
        user_metadata: {
          full_name: getFullNameFromEmail(user.email),
          user_type: user.type.toLowerCase(),
          phone: '+212 6 12 34 56 78',
          city: 'Tetouan'
        }
      });

      if (error) {
        if (error.message.includes('already registered')) {
          console.log(`   ⚠️  User already exists, trying to update confirmation status...`);
          
          // Try to update existing user
          const { data: updateData, error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
            data?.user?.id || 'unknown',
            { email_confirm: true }
          );

          if (updateError) {
            console.log(`   ❌ Failed to update: ${updateError.message}`);
          } else {
            console.log(`   ✅ Updated existing user confirmation status`);
          }
        } else {
          console.log(`   ❌ Creation failed: ${error.message}`);
        }
      } else if (data.user) {
        console.log(`   ✅ User created successfully with confirmed email`);
        console.log(`      User ID: ${data.user.id}`);
        console.log(`      Email Confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`);
      }
    } catch (err) {
      console.log(`   ❌ Error: ${err.message}`);
    }
    
    console.log(''); // Empty line
  }

  console.log('🧪 Testing login with fixed users...\n');

  // Test login for a few users
  const testLogins = [
    { email: '<EMAIL>', password: 'YalaAdmin2024!', type: 'Admin' },
    { email: '<EMAIL>', password: 'YalaManager2024!', type: 'Manager' },
    { email: '<EMAIL>', password: 'Client2024!', type: 'Client' }
  ];

  for (const user of testLogins) {
    try {
      console.log(`Testing login for ${user.type}: ${user.email}`);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: user.email,
        password: user.password
      });

      if (error) {
        console.log(`   ❌ Login failed: ${error.message}`);
      } else if (data.user) {
        console.log(`   ✅ Login successful!`);
        console.log(`      User ID: ${data.user.id}`);
        console.log(`      Email Confirmed: ${data.user.email_confirmed_at ? 'Yes' : 'No'}`);
        
        // Sign out after test
        await supabase.auth.signOut();
      }
    } catch (err) {
      console.log(`   ❌ Error: ${err.message}`);
    }
    
    console.log(''); // Empty line
  }

  console.log('✅ User confirmation fix completed!');
  console.log('\n📋 Instructions:');
  console.log('1. If you have the Supabase service key, replace "your-service-key-here" above');
  console.log('2. Run this script again with the service key');
  console.log('3. Or manually confirm users in Supabase Dashboard:');
  console.log('   - Go to Authentication > Users');
  console.log('   - Click on each user');
  console.log('   - Set "Email Confirmed" to true');
  console.log('4. Alternative: Disable email confirmation in Supabase settings');
}

function getFullNameFromEmail(email) {
  const nameMap = {
    '<EMAIL>': 'Youssef El Mansouri',
    '<EMAIL>': 'Aicha Benali',
    '<EMAIL>': 'Omar Tazi',
    '<EMAIL>': 'Fatima Alaoui',
    '<EMAIL>': 'Ahmed Bennani',
    '<EMAIL>': 'Khadija Amrani',
    '<EMAIL>': 'Hassan Idrissi',
    '<EMAIL>': 'Nadia Berrada',
    '<EMAIL>': 'Rachid Fassi',
    '<EMAIL>': 'Laila Cherkaoui',
    '<EMAIL>': 'Mohamed Alami',
    '<EMAIL>': 'Samira Benali'
  };
  
  return nameMap[email] || email.split('@')[0];
}

// Alternative method: Disable email confirmation entirely
async function disableEmailConfirmation() {
  console.log('\n🔧 Alternative Method: Disable Email Confirmation');
  console.log('=====================================');
  console.log('To disable email confirmation entirely:');
  console.log('1. Go to your Supabase Dashboard');
  console.log('2. Navigate to Authentication > Settings');
  console.log('3. Under "User Signups", toggle OFF "Enable email confirmations"');
  console.log('4. Save the settings');
  console.log('5. Try logging in again');
  console.log('\nThis will allow all users to login without email confirmation.');
}

// Run the fix
fixUserConfirmation()
  .then(() => {
    disableEmailConfirmation();
  })
  .catch(console.error);
