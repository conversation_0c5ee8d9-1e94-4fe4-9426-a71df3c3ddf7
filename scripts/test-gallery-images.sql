-- =============================================
-- TEST GALLERY IMAGES FUNCTIONALITY
-- Check how gallery images are stored and displayed
-- =============================================

-- Check products with gallery images
SELECT 'Gallery Images Analysis' as analysis;

-- Count products with gallery images
SELECT 
  COUNT(*) as total_products,
  COUNT(CASE WHEN thumbnail_images IS NOT NULL AND array_length(thumbnail_images, 1) > 0 THEN 1 END) as products_with_gallery,
  AVG(array_length(thumbnail_images, 1)) as avg_gallery_images_per_product
FROM products;

-- Show sample gallery images data
SELECT 
  id,
  title,
  CASE 
    WHEN thumbnail_images IS NULL THEN 'NO GALLERY IMAGES'
    WHEN array_length(thumbnail_images, 1) = 0 THEN 'EMPTY GALLERY ARRAY'
    ELSE CONCAT(array_length(thumbnail_images, 1), ' gallery images')
  END as gallery_status,
  CASE 
    WHEN thumbnail_images IS NOT NULL AND array_length(thumbnail_images, 1) > 0 THEN
      CASE 
        WHEN thumbnail_images[1] LIKE 'data:image%' THEN 'BASE64 (database storage)'
        WHEN thumbnail_images[1] LIKE 'http%' THEN 'URL (Supabase storage)'
        ELSE 'UNKNOWN FORMAT'
      END
    ELSE 'N/A'
  END as storage_type,
  CASE 
    WHEN thumbnail_images IS NOT NULL AND array_length(thumbnail_images, 1) > 0 THEN
      CASE 
        WHEN LENGTH(thumbnail_images[1]) > 100 THEN 
          CONCAT(LEFT(thumbnail_images[1], 60), '... (', LENGTH(thumbnail_images[1]), ' chars)')
        ELSE thumbnail_images[1]
      END
    ELSE 'NULL'
  END as first_gallery_image_preview
FROM products 
WHERE thumbnail_images IS NOT NULL AND array_length(thumbnail_images, 1) > 0
ORDER BY array_length(thumbnail_images, 1) DESC
LIMIT 10;

-- Show detailed gallery images for specific products
SELECT 
  'Detailed Gallery Images:' as detail,
  id,
  title,
  array_length(thumbnail_images, 1) as gallery_count,
  thumbnail_images
FROM products 
WHERE thumbnail_images IS NOT NULL 
  AND array_length(thumbnail_images, 1) > 0
LIMIT 3;

-- Check if any products have both featured and gallery images
SELECT 
  'Products with Both Featured and Gallery Images:' as check,
  COUNT(*) as count
FROM products 
WHERE featured_image IS NOT NULL 
  AND featured_image != '/placeholder.svg'
  AND thumbnail_images IS NOT NULL 
  AND array_length(thumbnail_images, 1) > 0
  AND NOT (array_length(thumbnail_images, 1) = 1 AND thumbnail_images[1] = '/placeholder.svg');

-- Show storage usage analysis
SELECT 
  'Storage Usage Analysis:' as analysis,
  SUM(LENGTH(featured_image::text)) as featured_images_total_size,
  SUM(array_length(thumbnail_images, 1)) as total_gallery_images_count,
  AVG(LENGTH(thumbnail_images::text)) as avg_gallery_data_size
FROM products 
WHERE featured_image IS NOT NULL OR thumbnail_images IS NOT NULL;
