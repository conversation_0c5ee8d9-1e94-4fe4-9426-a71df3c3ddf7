/**
 * Blank Page Fix Script
 * Helps diagnose and fix blank page issues after data updates
 */

console.log('🔧 YalaOffice Blank Page Fix Script');
console.log('=====================================\n');

// Check if we're in a browser environment
if (typeof window !== 'undefined') {
  console.log('✅ Running in browser environment');
  
  // Check for common issues
  const checks = [
    {
      name: 'React Error Boundary',
      check: () => {
        const errorBoundaries = document.querySelectorAll('[data-error-boundary]');
        return errorBoundaries.length > 0;
      },
      fix: 'Error boundary is active. Check console for React errors.'
    },
    {
      name: 'Supabase Connection',
      check: () => {
        return window.supabase !== undefined;
      },
      fix: 'Supabase client not found. Check if Supabase is properly initialized.'
    },
    {
      name: 'Console <PERSON>rrors',
      check: () => {
        // This is a placeholder - actual implementation would need error tracking
        return true;
      },
      fix: 'Check browser console (F12) for JavaScript errors.'
    },
    {
      name: 'Local Storage',
      check: () => {
        try {
          localStorage.setItem('test', 'test');
          localStorage.removeItem('test');
          return true;
        } catch (e) {
          return false;
        }
      },
      fix: 'Local storage is not available. Clear browser data or try incognito mode.'
    },
    {
      name: 'Network Connectivity',
      check: () => {
        return navigator.onLine;
      },
      fix: 'No internet connection. Check your network connection.'
    }
  ];

  console.log('🔍 Running diagnostic checks...\n');

  checks.forEach((check, index) => {
    try {
      const result = check.check();
      const status = result ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${check.name}`);
      
      if (!result) {
        console.log(`   💡 Fix: ${check.fix}`);
      }
    } catch (error) {
      console.log(`${index + 1}. ⚠️  ${check.name} - Error: ${error.message}`);
    }
  });

  console.log('\n🛠️  Quick Fixes:');
  console.log('================');
  console.log('1. Hard refresh: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)');
  console.log('2. Clear browser cache and cookies');
  console.log('3. Try incognito/private browsing mode');
  console.log('4. Check browser console for errors (F12)');
  console.log('5. Disable browser extensions temporarily');
  
  console.log('\n🔄 Data Sync Issues:');
  console.log('====================');
  console.log('If blank page occurs after data updates:');
  console.log('1. Check if real-time subscriptions are working');
  console.log('2. Verify Supabase connection is stable');
  console.log('3. Look for React state update errors');
  console.log('4. Check if components are handling null/undefined data');

  // Provide manual fix functions
  window.yalaOfficeFix = {
    clearCache: () => {
      localStorage.clear();
      sessionStorage.clear();
      console.log('✅ Cache cleared. Please refresh the page.');
    },
    
    resetAuth: () => {
      localStorage.removeItem('yala_user');
      localStorage.removeItem('yala_session_expiry');
      console.log('✅ Auth data cleared. Please refresh and login again.');
    },
    
    checkSupabase: async () => {
      try {
        if (window.supabase) {
          const { data, error } = await window.supabase.auth.getSession();
          if (error) {
            console.log('❌ Supabase auth error:', error.message);
          } else {
            console.log('✅ Supabase connection OK');
            console.log('Session:', data.session ? 'Active' : 'None');
          }
        } else {
          console.log('❌ Supabase client not found');
        }
      } catch (error) {
        console.log('❌ Supabase check failed:', error.message);
      }
    },
    
    forceRefresh: () => {
      window.location.reload(true);
    }
  };

  console.log('\n🔧 Manual Fix Commands:');
  console.log('========================');
  console.log('Run these in browser console:');
  console.log('• yalaOfficeFix.clearCache() - Clear all cached data');
  console.log('• yalaOfficeFix.resetAuth() - Reset authentication');
  console.log('• yalaOfficeFix.checkSupabase() - Check Supabase connection');
  console.log('• yalaOfficeFix.forceRefresh() - Force page refresh');

} else {
  console.log('❌ Not running in browser environment');
  console.log('This script should be run in the browser console.');
}

// Export for Node.js environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    name: 'YalaOffice Blank Page Fix',
    description: 'Diagnostic script for blank page issues',
    fixes: [
      'Hard refresh the page',
      'Clear browser cache',
      'Check console for errors',
      'Verify Supabase connection',
      'Reset authentication data'
    ]
  };
}
