/**
 * Database Connection Test Script
 * Tests the Supabase connection and verifies live data integration
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config();
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL' | 'WARN';
  message: string;
  data?: any;
}

class DatabaseConnectionTest {
  private supabase: any;
  private results: TestResult[] = [];

  constructor() {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase configuration. Please check your environment variables.');
    }

    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  // Add test result
  private addResult(test: string, status: 'PASS' | 'FAIL' | 'WARN', message: string, data?: any): void {
    this.results.push({ test, status, message, data });
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} ${test}: ${message}`);
    
    if (data && process.env.DEBUG === 'true') {
      console.log('   Data:', JSON.stringify(data, null, 2));
    }
  }

  // Test basic connection
  async testConnection(): Promise<void> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('count', { count: 'exact', head: true });

      if (error) {
        this.addResult('Database Connection', 'FAIL', `Connection failed: ${error.message}`);
      } else {
        this.addResult('Database Connection', 'PASS', 'Successfully connected to Supabase');
      }
    } catch (error) {
      this.addResult('Database Connection', 'FAIL', `Connection error: ${error}`);
    }
  }

  // Test users table
  async testUsersTable(): Promise<void> {
    try {
      const { data, error, count } = await this.supabase
        .from('users')
        .select('*', { count: 'exact' })
        .limit(5);

      if (error) {
        this.addResult('Users Table', 'FAIL', `Query failed: ${error.message}`);
        return;
      }

      if (!data || data.length === 0) {
        this.addResult('Users Table', 'WARN', 'Users table is empty');
        return;
      }

      // Check for required user types
      const userTypes = data.map((user: any) => user.user_type);
      const requiredTypes = ['admin', 'manager', 'client', 'reseller'];
      const missingTypes = requiredTypes.filter(type => !userTypes.includes(type));

      if (missingTypes.length > 0) {
        this.addResult('Users Table', 'WARN', `Missing user types: ${missingTypes.join(', ')}`);
      } else {
        this.addResult('Users Table', 'PASS', `Found ${count} users with all required types`);
      }

      // Check for Tetouan users
      const tetouanUsers = data.filter((user: any) => user.city === 'Tetouan');
      if (tetouanUsers.length > 0) {
        this.addResult('Tetouan Users', 'PASS', `Found ${tetouanUsers.length} users in Tetouan`);
      } else {
        this.addResult('Tetouan Users', 'WARN', 'No users found in Tetouan');
      }

    } catch (error) {
      this.addResult('Users Table', 'FAIL', `Test error: ${error}`);
    }
  }

  // Test products table
  async testProductsTable(): Promise<void> {
    try {
      const { data, error, count } = await this.supabase
        .from('products')
        .select(`
          *,
          categories (
            id,
            name,
            color,
            icon
          )
        `, { count: 'exact' })
        .eq('is_active', true)
        .limit(10);

      if (error) {
        this.addResult('Products Table', 'FAIL', `Query failed: ${error.message}`);
        return;
      }

      if (!data || data.length === 0) {
        this.addResult('Products Table', 'WARN', 'Products table is empty');
        return;
      }

      // Check for products with categories
      const productsWithCategories = data.filter((product: any) => product.categories);
      if (productsWithCategories.length > 0) {
        this.addResult('Product Categories', 'PASS', `${productsWithCategories.length} products have category relationships`);
      } else {
        this.addResult('Product Categories', 'WARN', 'No products have category relationships');
      }

      // Check for Moroccan pricing (MAD)
      const productsWithPricing = data.filter((product: any) => product.price > 0);
      if (productsWithPricing.length > 0) {
        const avgPrice = productsWithPricing.reduce((sum: number, p: any) => sum + parseFloat(p.price), 0) / productsWithPricing.length;
        this.addResult('Product Pricing', 'PASS', `Found ${productsWithPricing.length} products with pricing (avg: ${avgPrice.toFixed(2)} MAD)`);
      } else {
        this.addResult('Product Pricing', 'WARN', 'No products have pricing information');
      }

      this.addResult('Products Table', 'PASS', `Found ${count} active products`);

    } catch (error) {
      this.addResult('Products Table', 'FAIL', `Test error: ${error}`);
    }
  }

  // Test categories table
  async testCategoriesTable(): Promise<void> {
    try {
      const { data, error, count } = await this.supabase
        .from('categories')
        .select('*', { count: 'exact' })
        .eq('is_active', true)
        .order('sort_order');

      if (error) {
        this.addResult('Categories Table', 'FAIL', `Query failed: ${error.message}`);
        return;
      }

      if (!data || data.length === 0) {
        this.addResult('Categories Table', 'WARN', 'Categories table is empty');
        return;
      }

      // Check for expected categories
      const categoryNames = data.map((cat: any) => cat.name);
      const expectedCategories = [
        'Writing Instruments',
        'Paper & Notebooks',
        'School & Office Supplies',
        'Art & Craft Supplies'
      ];

      const foundCategories = expectedCategories.filter(name => 
        categoryNames.some((catName: string) => catName.includes(name))
      );

      if (foundCategories.length >= 3) {
        this.addResult('Expected Categories', 'PASS', `Found ${foundCategories.length} expected categories`);
      } else {
        this.addResult('Expected Categories', 'WARN', `Only found ${foundCategories.length} expected categories`);
      }

      this.addResult('Categories Table', 'PASS', `Found ${count} active categories`);

    } catch (error) {
      this.addResult('Categories Table', 'FAIL', `Test error: ${error}`);
    }
  }

  // Test branches table
  async testBranchesTable(): Promise<void> {
    try {
      const { data, error, count } = await this.supabase
        .from('branches')
        .select('*', { count: 'exact' })
        .eq('is_active', true);

      if (error) {
        this.addResult('Branches Table', 'FAIL', `Query failed: ${error.message}`);
        return;
      }

      if (!data || data.length === 0) {
        this.addResult('Branches Table', 'WARN', 'Branches table is empty');
        return;
      }

      // Check for Tetouan branches
      const tetouanBranches = data.filter((branch: any) => 
        branch.name.includes('Tetouan') || 
        (branch.address && JSON.stringify(branch.address).includes('Tetouan'))
      );

      if (tetouanBranches.length >= 2) {
        this.addResult('Tetouan Branches', 'PASS', `Found ${tetouanBranches.length} branches in Tetouan area`);
      } else {
        this.addResult('Tetouan Branches', 'WARN', `Only found ${tetouanBranches.length} branches in Tetouan area`);
      }

      // Check for main branch
      const mainBranch = data.find((branch: any) => branch.is_main_branch);
      if (mainBranch) {
        this.addResult('Main Branch', 'PASS', `Main branch found: ${mainBranch.name}`);
      } else {
        this.addResult('Main Branch', 'WARN', 'No main branch designated');
      }

      this.addResult('Branches Table', 'PASS', `Found ${count} active branches`);

    } catch (error) {
      this.addResult('Branches Table', 'FAIL', `Test error: ${error}`);
    }
  }

  // Test orders table
  async testOrdersTable(): Promise<void> {
    try {
      const { data, error, count } = await this.supabase
        .from('orders')
        .select(`
          *,
          users!orders_customer_id_fkey (
            id,
            full_name,
            email
          )
        `, { count: 'exact' })
        .limit(5);

      if (error) {
        this.addResult('Orders Table', 'FAIL', `Query failed: ${error.message}`);
        return;
      }

      if (count === 0) {
        this.addResult('Orders Table', 'WARN', 'Orders table is empty (expected for new setup)');
      } else {
        this.addResult('Orders Table', 'PASS', `Found ${count} orders`);
      }

    } catch (error) {
      this.addResult('Orders Table', 'FAIL', `Test error: ${error}`);
    }
  }

  // Test customer profiles
  async testCustomerProfiles(): Promise<void> {
    try {
      const { data, error, count } = await this.supabase
        .from('customer_profiles')
        .select(`
          *,
          users (
            id,
            full_name,
            user_type
          )
        `, { count: 'exact' });

      if (error) {
        this.addResult('Customer Profiles', 'FAIL', `Query failed: ${error.message}`);
        return;
      }

      if (!data || data.length === 0) {
        this.addResult('Customer Profiles', 'WARN', 'Customer profiles table is empty');
        return;
      }

      // Check for reseller profiles with discounts
      const resellerProfiles = data.filter((profile: any) => 
        profile.users?.user_type === 'reseller' && profile.discount_rate > 0
      );

      if (resellerProfiles.length > 0) {
        this.addResult('Reseller Discounts', 'PASS', `Found ${resellerProfiles.length} resellers with discount rates`);
      } else {
        this.addResult('Reseller Discounts', 'WARN', 'No resellers have discount rates configured');
      }

      this.addResult('Customer Profiles', 'PASS', `Found ${count} customer profiles`);

    } catch (error) {
      this.addResult('Customer Profiles', 'FAIL', `Test error: ${error}`);
    }
  }

  // Test real-time subscriptions
  async testRealtimeSubscriptions(): Promise<void> {
    try {
      let subscriptionWorking = false;

      const channel = this.supabase
        .channel('test-channel')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'products' }, 
          (payload: any) => {
            subscriptionWorking = true;
          }
        )
        .subscribe();

      // Wait a moment for subscription to establish
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (channel.state === 'SUBSCRIBED') {
        this.addResult('Real-time Subscriptions', 'PASS', 'Real-time subscriptions are working');
      } else {
        this.addResult('Real-time Subscriptions', 'WARN', 'Real-time subscriptions may not be working properly');
      }

      // Clean up
      await this.supabase.removeChannel(channel);

    } catch (error) {
      this.addResult('Real-time Subscriptions', 'FAIL', `Test error: ${error}`);
    }
  }

  // Run all tests
  async runAllTests(): Promise<void> {
    console.log('🧪 Starting YalaOffice Database Connection Tests...\n');

    await this.testConnection();
    await this.testUsersTable();
    await this.testProductsTable();
    await this.testCategoriesTable();
    await this.testBranchesTable();
    await this.testOrdersTable();
    await this.testCustomerProfiles();
    await this.testRealtimeSubscriptions();

    this.printSummary();
  }

  // Print test summary
  private printSummary(): void {
    console.log('\n📊 Test Summary:');
    console.log('================');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const warnings = this.results.filter(r => r.status === 'WARN').length;

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️  Warnings: ${warnings}`);
    console.log(`📝 Total Tests: ${this.results.length}`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    if (warnings > 0) {
      console.log('\n⚠️  Warnings:');
      this.results
        .filter(r => r.status === 'WARN')
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }

    console.log('\n🎯 Recommendations:');
    if (failed > 0) {
      console.log('   - Fix failed tests before proceeding to production');
      console.log('   - Check your Supabase configuration and permissions');
    }
    if (warnings > 0) {
      console.log('   - Review warnings and populate missing data if needed');
      console.log('   - Run the database setup script if data is missing');
    }
    if (failed === 0 && warnings === 0) {
      console.log('   - All tests passed! Your database is ready for production');
      console.log('   - You can now migrate your application to use live data');
    }

    console.log('\n🚀 Next Steps:');
    console.log('   1. Run: npm run migrate:live-data');
    console.log('   2. Update your components to use the live data service');
    console.log('   3. Test your application with live data');
    console.log('   4. Deploy to production');
  }
}

// Run tests if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new DatabaseConnectionTest();
  tester.runAllTests().catch(console.error);
}

export default DatabaseConnectionTest;
