#!/bin/bash

# YalaOffice Production Backup Script
# This script performs comprehensive backups of the YalaOffice system

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="${BACKUP_DIR:-/backups}"
LOG_FILE="${BACKUP_DIR}/backup.log"
RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="yalaoffice_backup_${TIMESTAMP}"

# Supabase Configuration
SUPABASE_URL="${SUPABASE_URL}"
SUPABASE_SERVICE_KEY="${SUPABASE_SERVICE_KEY}"
SUPABASE_PROJECT_ID=$(echo "$SUPABASE_URL" | sed 's/.*\/\/\([^.]*\).*/\1/')

# AWS S3 Configuration (for remote backup storage)
S3_BUCKET="${BACKUP_S3_BUCKET}"
AWS_REGION="${AWS_REGION:-us-east-1}"

# Notification Configuration
SLACK_WEBHOOK_URL="${SLACK_WEBHOOK_URL:-}"
EMAIL_RECIPIENTS="${EMAIL_RECIPIENTS:-}"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    send_notification "❌ YalaOffice Backup Failed" "$1" "error"
    exit 1
}

# Success notification
success_notification() {
    log "SUCCESS: $1"
    send_notification "✅ YalaOffice Backup Completed" "$1" "success"
}

# Send notification function
send_notification() {
    local title="$1"
    local message="$2"
    local type="${3:-info}"
    
    # Slack notification
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        local color="good"
        [[ "$type" == "error" ]] && color="danger"
        [[ "$type" == "warning" ]] && color="warning"
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"title\":\"$title\",\"text\":\"$message\",\"ts\":$(date +%s)}]}" \
            "$SLACK_WEBHOOK_URL" || true
    fi
    
    # Email notification (if configured)
    if [[ -n "$EMAIL_RECIPIENTS" ]]; then
        echo "$message" | mail -s "$title" "$EMAIL_RECIPIENTS" || true
    fi
}

# Create backup directory
create_backup_dir() {
    log "Creating backup directory: ${BACKUP_DIR}/${BACKUP_NAME}"
    mkdir -p "${BACKUP_DIR}/${BACKUP_NAME}"
    cd "${BACKUP_DIR}/${BACKUP_NAME}"
}

# Backup Supabase database
backup_database() {
    log "Starting database backup..."
    
    # Create database dump using Supabase CLI
    if command -v supabase &> /dev/null; then
        log "Using Supabase CLI for database backup"
        supabase db dump --project-id "$SUPABASE_PROJECT_ID" > database_dump.sql || error_exit "Database backup failed"
    else
        log "Using pg_dump for database backup"
        # Alternative: Use pg_dump if Supabase CLI is not available
        # This requires database connection details
        PGPASSWORD="$SUPABASE_SERVICE_KEY" pg_dump \
            -h "$SUPABASE_HOST" \
            -U "$SUPABASE_USER" \
            -d "$SUPABASE_DB" \
            --no-password \
            --verbose \
            --clean \
            --if-exists \
            --create \
            > database_dump.sql || error_exit "Database backup failed"
    fi
    
    # Compress database dump
    gzip database_dump.sql
    log "Database backup completed: database_dump.sql.gz"
}

# Backup application files
backup_application() {
    log "Starting application backup..."
    
    # Backup application configuration
    mkdir -p app_config
    cp -r /opt/yalaoffice/docker-compose.prod.yml app_config/ 2>/dev/null || true
    cp -r /opt/yalaoffice/nginx.prod.conf app_config/ 2>/dev/null || true
    cp -r /opt/yalaoffice/.env app_config/ 2>/dev/null || true
    cp -r /opt/yalaoffice/ssl app_config/ 2>/dev/null || true
    
    # Backup monitoring configuration
    mkdir -p monitoring_config
    cp -r /opt/yalaoffice/monitoring monitoring_config/ 2>/dev/null || true
    
    # Backup logs (last 7 days)
    mkdir -p logs
    find /var/log -name "*yalaoffice*" -mtime -7 -exec cp {} logs/ \; 2>/dev/null || true
    find /opt/yalaoffice/logs -type f -mtime -7 -exec cp {} logs/ \; 2>/dev/null || true
    
    log "Application backup completed"
}

# Backup Redis data
backup_redis() {
    log "Starting Redis backup..."
    
    # Create Redis backup
    docker exec yalaoffice-redis redis-cli --rdb redis_dump.rdb || error_exit "Redis backup failed"
    docker cp yalaoffice-redis:/data/redis_dump.rdb . || error_exit "Failed to copy Redis dump"
    
    # Compress Redis dump
    gzip redis_dump.rdb
    log "Redis backup completed: redis_dump.rdb.gz"
}

# Backup system metrics and monitoring data
backup_monitoring() {
    log "Starting monitoring data backup..."
    
    # Backup Prometheus data (last 7 days)
    mkdir -p prometheus_data
    docker exec yalaoffice-prometheus tar czf - /prometheus | tar xzf - -C prometheus_data/ 2>/dev/null || true
    
    # Backup Grafana dashboards and settings
    mkdir -p grafana_data
    docker exec yalaoffice-grafana tar czf - /var/lib/grafana | tar xzf - -C grafana_data/ 2>/dev/null || true
    
    log "Monitoring data backup completed"
}

# Create backup metadata
create_metadata() {
    log "Creating backup metadata..."
    
    cat > backup_metadata.json << EOF
{
    "backup_name": "$BACKUP_NAME",
    "timestamp": "$TIMESTAMP",
    "date": "$(date -Iseconds)",
    "version": "$(cat /opt/yalaoffice/package.json | jq -r '.version' 2>/dev/null || echo 'unknown')",
    "environment": "production",
    "components": {
        "database": true,
        "application": true,
        "redis": true,
        "monitoring": true
    },
    "size": "$(du -sh . | cut -f1)",
    "files": $(find . -type f | wc -l)
}
EOF
    
    log "Backup metadata created"
}

# Compress backup
compress_backup() {
    log "Compressing backup..."
    
    cd "$BACKUP_DIR"
    tar czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
    rm -rf "$BACKUP_NAME"
    
    local backup_size=$(du -sh "${BACKUP_NAME}.tar.gz" | cut -f1)
    log "Backup compressed: ${BACKUP_NAME}.tar.gz (${backup_size})"
}

# Upload to S3
upload_to_s3() {
    if [[ -n "$S3_BUCKET" ]]; then
        log "Uploading backup to S3..."
        
        aws s3 cp "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" \
            "s3://${S3_BUCKET}/yalaoffice-backups/${BACKUP_NAME}.tar.gz" \
            --region "$AWS_REGION" \
            --storage-class STANDARD_IA || error_exit "S3 upload failed"
        
        log "Backup uploaded to S3: s3://${S3_BUCKET}/yalaoffice-backups/${BACKUP_NAME}.tar.gz"
    else
        log "S3 upload skipped (no bucket configured)"
    fi
}

# Cleanup old backups
cleanup_old_backups() {
    log "Cleaning up old backups (older than ${RETENTION_DAYS} days)..."
    
    # Local cleanup
    find "$BACKUP_DIR" -name "yalaoffice_backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete || true
    
    # S3 cleanup
    if [[ -n "$S3_BUCKET" ]]; then
        aws s3 ls "s3://${S3_BUCKET}/yalaoffice-backups/" | \
        while read -r line; do
            file_date=$(echo "$line" | awk '{print $1}')
            file_name=$(echo "$line" | awk '{print $4}')
            
            if [[ -n "$file_name" && "$file_name" == yalaoffice_backup_* ]]; then
                file_age=$(( ($(date +%s) - $(date -d "$file_date" +%s)) / 86400 ))
                if [[ $file_age -gt $RETENTION_DAYS ]]; then
                    aws s3 rm "s3://${S3_BUCKET}/yalaoffice-backups/$file_name" || true
                    log "Deleted old S3 backup: $file_name"
                fi
            fi
        done
    fi
    
    log "Cleanup completed"
}

# Verify backup integrity
verify_backup() {
    log "Verifying backup integrity..."
    
    # Test tar file
    tar tzf "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" > /dev/null || error_exit "Backup file is corrupted"
    
    # Test database dump
    tar xzf "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" -O database_dump.sql.gz | gunzip | head -n 10 | grep -q "PostgreSQL database dump" || error_exit "Database dump is invalid"
    
    log "Backup integrity verified"
}

# Main backup function
main() {
    log "Starting YalaOffice backup process..."
    
    # Check prerequisites
    command -v docker >/dev/null 2>&1 || error_exit "Docker is not installed"
    command -v aws >/dev/null 2>&1 || log "AWS CLI not found, S3 upload will be skipped"
    
    # Create backup directory
    create_backup_dir
    
    # Perform backups
    backup_database
    backup_application
    backup_redis
    backup_monitoring
    
    # Create metadata
    create_metadata
    
    # Compress backup
    compress_backup
    
    # Verify backup
    verify_backup
    
    # Upload to S3
    upload_to_s3
    
    # Cleanup old backups
    cleanup_old_backups
    
    local backup_size=$(du -sh "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" | cut -f1)
    success_notification "Backup completed successfully. Size: ${backup_size}"
    
    log "Backup process completed successfully"
}

# Run main function
main "$@"
