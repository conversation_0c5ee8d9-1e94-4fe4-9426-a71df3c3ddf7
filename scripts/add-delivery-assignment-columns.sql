-- Add delivery assignment columns to orders table
-- Run this script in Supabase SQL Editor to fix the "assigned_delivery_person" column error

-- =============================================
-- ADD MISSING DELIVERY ASSIGNMENT COLUMNS
-- =============================================

-- Add delivery assignment columns to orders table if they don't exist
DO $$ 
BEGIN
    -- Add assigned_delivery_person column (UUID reference to users table)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'assigned_delivery_person') THEN
        ALTER TABLE orders ADD COLUMN assigned_delivery_person UUID REFERENCES users(id);
        RAISE NOTICE 'Added assigned_delivery_person column to orders table';
    ELSE
        RAISE NOTICE 'assigned_delivery_person column already exists in orders table';
    END IF;
    
    -- Add assigned_delivery_person_id column (alternative naming convention)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'assigned_delivery_person_id') THEN
        ALTER TABLE orders ADD COLUMN assigned_delivery_person_id UUID REFERENCES users(id);
        RAISE NOTICE 'Added assigned_delivery_person_id column to orders table';
    ELSE
        RAISE NOTICE 'assigned_delivery_person_id column already exists in orders table';
    END IF;
    
    -- Add delivery_person_name column (for caching the delivery person's name)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'delivery_person_name') THEN
        ALTER TABLE orders ADD COLUMN delivery_person_name VARCHAR(255);
        RAISE NOTICE 'Added delivery_person_name column to orders table';
    ELSE
        RAISE NOTICE 'delivery_person_name column already exists in orders table';
    END IF;
    
    -- Add delivery_assigned_at column (timestamp when delivery was assigned)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'delivery_assigned_at') THEN
        ALTER TABLE orders ADD COLUMN delivery_assigned_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added delivery_assigned_at column to orders table';
    ELSE
        RAISE NOTICE 'delivery_assigned_at column already exists in orders table';
    END IF;
    
    -- Add delivery_notes column (optional notes for delivery)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'delivery_notes') THEN
        ALTER TABLE orders ADD COLUMN delivery_notes TEXT;
        RAISE NOTICE 'Added delivery_notes column to orders table';
    ELSE
        RAISE NOTICE 'delivery_notes column already exists in orders table';
    END IF;
    
    -- Add delivery_address column (specific delivery address if different from customer address)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'orders' AND column_name = 'delivery_address') THEN
        ALTER TABLE orders ADD COLUMN delivery_address TEXT;
        RAISE NOTICE 'Added delivery_address column to orders table';
    ELSE
        RAISE NOTICE 'delivery_address column already exists in orders table';
    END IF;
END $$;

-- =============================================
-- CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Create indexes on the new columns for better query performance
CREATE INDEX IF NOT EXISTS idx_orders_assigned_delivery_person 
ON orders(assigned_delivery_person) 
WHERE assigned_delivery_person IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_orders_assigned_delivery_person_id 
ON orders(assigned_delivery_person_id) 
WHERE assigned_delivery_person_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_orders_delivery_assigned_at 
ON orders(delivery_assigned_at) 
WHERE delivery_assigned_at IS NOT NULL;

-- =============================================
-- VERIFY THE COLUMNS WERE ADDED
-- =============================================

-- Check that all delivery-related columns exist in the orders table
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'orders' 
  AND column_name IN (
    'assigned_delivery_person', 
    'assigned_delivery_person_id', 
    'delivery_person_name', 
    'delivery_assigned_at', 
    'delivery_notes', 
    'delivery_address'
  )
ORDER BY column_name;

-- =============================================
-- SAMPLE QUERY TO TEST THE COLUMNS
-- =============================================

-- Test query to verify the columns work correctly
SELECT 
    id,
    customer_id,
    status,
    assigned_delivery_person,
    assigned_delivery_person_id,
    delivery_person_name,
    delivery_assigned_at,
    delivery_notes,
    delivery_address,
    total,
    created_at
FROM orders 
LIMIT 5;

-- =============================================
-- UPDATE RLS POLICIES (IF NEEDED)
-- =============================================

-- Update RLS policies to include the new delivery assignment columns
-- This ensures proper access control for delivery personnel

-- Allow delivery personnel to see orders assigned to them
DROP POLICY IF EXISTS "Delivery personnel can view assigned orders" ON orders;
CREATE POLICY "Delivery personnel can view assigned orders" ON orders
    FOR SELECT USING (
        auth.uid() IN (
            SELECT id FROM users 
            WHERE user_type = 'delivery_person' 
            AND is_active = true
            AND (
                id = assigned_delivery_person 
                OR id = assigned_delivery_person_id
            )
        )
    );

-- Allow delivery personnel to update status of assigned orders
DROP POLICY IF EXISTS "Delivery personnel can update assigned orders" ON orders;
CREATE POLICY "Delivery personnel can update assigned orders" ON orders
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT id FROM users 
            WHERE user_type = 'delivery_person' 
            AND is_active = true
            AND (
                id = assigned_delivery_person 
                OR id = assigned_delivery_person_id
            )
        )
    );

-- =============================================
-- SUCCESS MESSAGE
-- =============================================

DO $$
BEGIN
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'DELIVERY ASSIGNMENT COLUMNS SETUP COMPLETE!';
    RAISE NOTICE '==============================================';
    RAISE NOTICE 'The following columns have been added to the orders table:';
    RAISE NOTICE '- assigned_delivery_person (UUID)';
    RAISE NOTICE '- assigned_delivery_person_id (UUID)';
    RAISE NOTICE '- delivery_person_name (VARCHAR)';
    RAISE NOTICE '- delivery_assigned_at (TIMESTAMP)';
    RAISE NOTICE '- delivery_notes (TEXT)';
    RAISE NOTICE '- delivery_address (TEXT)';
    RAISE NOTICE '';
    RAISE NOTICE 'Indexes and RLS policies have been updated.';
    RAISE NOTICE 'The "Assign Delivery Person" feature should now work correctly!';
    RAISE NOTICE '==============================================';
END $$;
