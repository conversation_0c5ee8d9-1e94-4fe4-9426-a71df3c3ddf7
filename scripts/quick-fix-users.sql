-- =============================================
-- QUICK FIX FOR USER MANAGEMENT ISSUES
-- This is a minimal script to fix the immediate problems
-- =============================================

-- 1. Drop all existing policies and recreate
DROP POLICY IF EXISTS users_own_profile ON users;
DROP POLICY IF EXISTS users_access_policy ON users;
DROP POLICY IF EXISTS users_policy ON users;

-- 2. Create admin-friendly policy
CREATE POLICY users_access_policy ON users
    FOR ALL USING (
        auth.uid() = id OR
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_type IN ('admin', 'manager')
            AND is_active = true
        )
    );

-- 3. Sync current auth user to users table if missing
DO $$
DECLARE
    current_auth_user_id UUID;
    current_auth_email TEXT;
    user_exists BOOLEAN;
BEGIN
    -- Get current authenticated user
    SELECT auth.uid() INTO current_auth_user_id;
    
    IF current_auth_user_id IS NOT NULL THEN
        -- Get user email from auth.users
        SELECT email INTO current_auth_email 
        FROM auth.users 
        WHERE id = current_auth_user_id;
        
        -- Check if user exists in users table
        SELECT EXISTS(SELECT 1 FROM users WHERE id = current_auth_user_id) INTO user_exists;
        
        -- If user doesn't exist, create them
        IF NOT user_exists THEN
            INSERT INTO users (
                id,
                email,
                full_name,
                user_type,
                phone,
                city,
                is_active,
                created_at,
                updated_at
            ) VALUES (
                current_auth_user_id,
                current_auth_email,
                COALESCE(split_part(current_auth_email, '@', 1), 'User'),
                'admin', -- Default to admin for existing users
                '+212 6 12 34 56 78',
                'Tetouan',
                true,
                NOW(),
                NOW()
            );
            
            RAISE NOTICE 'Created user record for: %', current_auth_email;
        ELSE
            RAISE NOTICE 'User already exists: %', current_auth_email;
        END IF;
    ELSE
        RAISE NOTICE 'No authenticated user found';
    END IF;
END $$;

-- 4. Create simple RPC function for getting all users
CREATE OR REPLACE FUNCTION get_all_users_admin()
RETURNS TABLE (
    id UUID,
    email VARCHAR(255),
    full_name VARCHAR(255),
    user_type VARCHAR(20),
    phone VARCHAR(20),
    city VARCHAR(100),
    is_active BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT u.id, u.email, u.full_name, u.user_type, u.phone, u.city,
           u.is_active, u.created_at, u.updated_at
    FROM users u
    ORDER BY u.created_at DESC;
END;
$$;

-- 5. Create simple statistics function
CREATE OR REPLACE FUNCTION get_user_statistics()
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    stats JSON;
BEGIN
    SELECT json_build_object(
        'total', COUNT(*),
        'admins', COUNT(*) FILTER (WHERE user_type = 'admin'),
        'managers', COUNT(*) FILTER (WHERE user_type = 'manager'),
        'clients', COUNT(*) FILTER (WHERE user_type = 'client'),
        'resellers', COUNT(*) FILTER (WHERE user_type = 'reseller'),
        'delivery', COUNT(*) FILTER (WHERE user_type = 'delivery_person'),
        'active', COUNT(*) FILTER (WHERE is_active = true),
        'inactive', COUNT(*) FILTER (WHERE is_active = false),
        'recentlyCreated', COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days')
    ) INTO stats
    FROM users;
    
    RETURN stats;
END;
$$;

-- 6. Grant permissions
GRANT EXECUTE ON FUNCTION get_all_users_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_statistics() TO authenticated;

-- 7. Test the functions
SELECT 'Testing functions:' as status;
SELECT COUNT(*) as total_users FROM get_all_users_admin();
SELECT get_user_statistics() as stats;
