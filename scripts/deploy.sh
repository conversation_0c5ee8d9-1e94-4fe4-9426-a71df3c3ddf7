#!/bin/bash

# YalaOffice Production Deployment Script
# This script handles zero-downtime deployment of YalaOffice to production

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/var/log/yalaoffice-deploy.log"
DEPLOYMENT_ID=$(date +"%Y%m%d_%H%M%S")

# Environment
ENVIRONMENT="${ENVIRONMENT:-production}"
DOCKER_COMPOSE_FILE="docker-compose.${ENVIRONMENT}.yml"
BACKUP_BEFORE_DEPLOY="${BACKUP_BEFORE_DEPLOY:-true}"
RUN_HEALTH_CHECKS="${RUN_HEALTH_CHECKS:-true}"
ROLLBACK_ON_FAILURE="${ROLLBACK_ON_FAILURE:-true}"

# Notification settings
SLACK_WEBHOOK_URL="${SLACK_WEBHOOK_URL:-}"
DEPLOYMENT_CHANNEL="${DEPLOYMENT_CHANNEL:-#deployments}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC} $message" | tee -a "$LOG_FILE" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE" ;;
    esac
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Error handling
error_exit() {
    log "ERROR" "$1"
    send_notification "❌ Deployment Failed" "$1" "error"
    
    if [[ "$ROLLBACK_ON_FAILURE" == "true" ]]; then
        log "INFO" "Initiating rollback..."
        rollback_deployment
    fi
    
    exit 1
}

# Send notification
send_notification() {
    local title="$1"
    local message="$2"
    local type="${3:-info}"
    
    if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
        local color="good"
        local emoji="ℹ️"
        
        case "$type" in
            "error")   color="danger"; emoji="❌" ;;
            "warning") color="warning"; emoji="⚠️" ;;
            "success") color="good"; emoji="✅" ;;
        esac
        
        local payload=$(cat << EOF
{
    "channel": "$DEPLOYMENT_CHANNEL",
    "username": "YalaOffice Deploy Bot",
    "icon_emoji": ":rocket:",
    "attachments": [
        {
            "color": "$color",
            "title": "$emoji $title",
            "text": "$message",
            "fields": [
                {
                    "title": "Environment",
                    "value": "$ENVIRONMENT",
                    "short": true
                },
                {
                    "title": "Deployment ID",
                    "value": "$DEPLOYMENT_ID",
                    "short": true
                }
            ],
            "ts": $(date +%s)
        }
    ]
}
EOF
        )
        
        curl -X POST -H 'Content-type: application/json' \
            --data "$payload" \
            "$SLACK_WEBHOOK_URL" > /dev/null 2>&1 || true
    fi
}

# Pre-deployment checks
pre_deployment_checks() {
    log "INFO" "Running pre-deployment checks..."
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        error_exit "Docker is not running"
    fi
    
    # Check if docker-compose file exists
    if [[ ! -f "$PROJECT_DIR/$DOCKER_COMPOSE_FILE" ]]; then
        error_exit "Docker compose file not found: $DOCKER_COMPOSE_FILE"
    fi
    
    # Check if required environment variables are set
    local required_vars=("SUPABASE_URL" "SUPABASE_ANON_KEY")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error_exit "Required environment variable not set: $var"
        fi
    done
    
    # Check disk space
    local available_space=$(df / | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 1048576 ]]; then # Less than 1GB
        error_exit "Insufficient disk space for deployment"
    fi
    
    # Check if services are healthy
    if docker-compose -f "$DOCKER_COMPOSE_FILE" ps | grep -q "unhealthy"; then
        log "WARN" "Some services are unhealthy, proceeding with caution"
    fi
    
    log "SUCCESS" "Pre-deployment checks passed"
}

# Backup current deployment
backup_current_deployment() {
    if [[ "$BACKUP_BEFORE_DEPLOY" == "true" ]]; then
        log "INFO" "Creating backup before deployment..."
        
        # Run backup script
        if [[ -f "$SCRIPT_DIR/backup/backup.sh" ]]; then
            bash "$SCRIPT_DIR/backup/backup.sh" || error_exit "Backup failed"
        else
            log "WARN" "Backup script not found, skipping backup"
        fi
        
        log "SUCCESS" "Backup completed"
    else
        log "INFO" "Backup skipped (BACKUP_BEFORE_DEPLOY=false)"
    fi
}

# Pull latest images
pull_images() {
    log "INFO" "Pulling latest Docker images..."
    
    cd "$PROJECT_DIR"
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull || error_exit "Failed to pull Docker images"
    
    log "SUCCESS" "Docker images pulled successfully"
}

# Deploy application with zero downtime
deploy_application() {
    log "INFO" "Starting zero-downtime deployment..."
    
    cd "$PROJECT_DIR"
    
    # Scale up new instances
    log "INFO" "Scaling up new application instances..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d --scale yalaoffice-app=2 --no-recreate
    
    # Wait for new instances to be healthy
    log "INFO" "Waiting for new instances to be healthy..."
    local max_attempts=30
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        if docker-compose -f "$DOCKER_COMPOSE_FILE" ps yalaoffice-app | grep -q "healthy"; then
            log "SUCCESS" "New instances are healthy"
            break
        fi
        
        sleep 10
        ((attempt++))
        log "INFO" "Waiting for health check... (attempt $attempt/$max_attempts)"
    done
    
    if [[ $attempt -eq $max_attempts ]]; then
        error_exit "New instances failed to become healthy"
    fi
    
    # Scale down old instances
    log "INFO" "Scaling down old instances..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d --scale yalaoffice-app=1
    
    # Update other services
    log "INFO" "Updating other services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log "SUCCESS" "Zero-downtime deployment completed"
}

# Run health checks
run_health_checks() {
    if [[ "$RUN_HEALTH_CHECKS" == "true" ]]; then
        log "INFO" "Running post-deployment health checks..."
        
        local health_check_url="http://localhost/health"
        local max_attempts=10
        local attempt=0
        
        while [[ $attempt -lt $max_attempts ]]; do
            if curl -f -s "$health_check_url" > /dev/null; then
                log "SUCCESS" "Health check passed"
                break
            fi
            
            sleep 5
            ((attempt++))
            log "INFO" "Health check attempt $attempt/$max_attempts"
        done
        
        if [[ $attempt -eq $max_attempts ]]; then
            error_exit "Health checks failed"
        fi
        
        # Run smoke tests
        if [[ -f "$SCRIPT_DIR/smoke-tests.sh" ]]; then
            log "INFO" "Running smoke tests..."
            bash "$SCRIPT_DIR/smoke-tests.sh" || error_exit "Smoke tests failed"
        fi
        
        log "SUCCESS" "All health checks passed"
    else
        log "INFO" "Health checks skipped (RUN_HEALTH_CHECKS=false)"
    fi
}

# Cleanup old images and containers
cleanup() {
    log "INFO" "Cleaning up old Docker images and containers..."
    
    # Remove unused images
    docker image prune -f > /dev/null 2>&1 || true
    
    # Remove unused containers
    docker container prune -f > /dev/null 2>&1 || true
    
    # Remove unused volumes (be careful with this)
    # docker volume prune -f > /dev/null 2>&1 || true
    
    log "SUCCESS" "Cleanup completed"
}

# Rollback deployment
rollback_deployment() {
    log "WARN" "Rolling back deployment..."
    
    cd "$PROJECT_DIR"
    
    # Get previous image tags (this would need to be implemented based on your tagging strategy)
    local previous_tag=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep yalaoffice | head -2 | tail -1)
    
    if [[ -n "$previous_tag" ]]; then
        log "INFO" "Rolling back to: $previous_tag"
        
        # Update docker-compose to use previous tag
        # This is a simplified example - you'd need to implement proper tag management
        docker-compose -f "$DOCKER_COMPOSE_FILE" down
        docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
        
        log "SUCCESS" "Rollback completed"
        send_notification "🔄 Rollback Completed" "Deployment rolled back to previous version" "warning"
    else
        log "ERROR" "No previous version found for rollback"
    fi
}

# Update deployment status
update_deployment_status() {
    local status="$1"
    local message="$2"
    
    # Create deployment record
    cat > "/var/log/deployments/${DEPLOYMENT_ID}.json" << EOF
{
    "deployment_id": "$DEPLOYMENT_ID",
    "timestamp": "$(date -Iseconds)",
    "environment": "$ENVIRONMENT",
    "status": "$status",
    "message": "$message",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "git_branch": "$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo 'unknown')"
}
EOF
}

# Main deployment function
main() {
    log "INFO" "Starting YalaOffice deployment (ID: $DEPLOYMENT_ID)"
    send_notification "🚀 Deployment Started" "Starting deployment to $ENVIRONMENT environment"
    
    # Create deployment log directory
    mkdir -p "/var/log/deployments"
    
    # Update deployment status
    update_deployment_status "in_progress" "Deployment started"
    
    # Run deployment steps
    pre_deployment_checks
    backup_current_deployment
    pull_images
    deploy_application
    run_health_checks
    cleanup
    
    # Update deployment status
    update_deployment_status "success" "Deployment completed successfully"
    
    local deployment_time=$(($(date +%s) - $(date -d "$DEPLOYMENT_ID" +%s)))
    log "SUCCESS" "Deployment completed successfully in ${deployment_time}s"
    send_notification "✅ Deployment Successful" "Deployment completed successfully in ${deployment_time}s" "success"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback_deployment
        ;;
    "health-check")
        run_health_checks
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|health-check|cleanup}"
        exit 1
        ;;
esac
