-- Simple SQL to confirm all test users
-- Copy and paste this into Supabase SQL Editor

UPDATE auth.users SET email_confirmed_at = now() WHERE email = '<EMAIL>';
UPDATE auth.users SET email_confirmed_at = now() WHERE email = '<EMAIL>';
UPDATE auth.users SET email_confirmed_at = now() WHERE email = '<EMAIL>';
UPDATE auth.users SET email_confirmed_at = now() WHERE email = '<EMAIL>';

-- Check if it worked
SELECT email, email_confirmed_at FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');
