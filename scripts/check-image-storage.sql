-- =============================================
-- CHECK HOW IMAGES ARE CURRENTLY STORED
-- Run this to see your current image storage method
-- =============================================

-- Check if any products have images
SELECT 'Current Image Storage Analysis' as analysis;

-- Count products with images
SELECT 
  COUNT(*) as total_products,
  COUNT(featured_image) as products_with_featured_image,
  COUNT(thumbnail_images) as products_with_gallery_images
FROM products;

-- Show sample image data to determine storage method
SELECT 
  id,
  title,
  CASE 
    WHEN featured_image LIKE 'data:image%' THEN 'BASE64 (stored in database)'
    WHEN featured_image LIKE 'http%' THEN 'URL (stored in Supabase Storage)'
    WHEN featured_image IS NULL THEN 'NO IMAGE'
    ELSE 'UNKNOWN FORMAT'
  END as featured_image_type,
  CASE 
    WHEN featured_image IS NOT NULL THEN 
      CASE 
        WHEN LENGTH(featured_image) > 100 THEN CONCAT(LEFT(featured_image, 50), '... (', LENGTH(featured_image), ' characters)')
        ELSE featured_image
      END
    ELSE 'NULL'
  END as featured_image_preview,
  CASE 
    WHEN thumbnail_images IS NOT NULL AND array_length(thumbnail_images, 1) > 0 THEN
      CASE 
        WHEN thumbnail_images[1] LIKE 'data:image%' THEN 'BASE64 (stored in database)'
        WHEN thumbnail_images[1] LIKE 'http%' THEN 'URL (stored in Supabase Storage)'
        ELSE 'UNKNOWN FORMAT'
      END
    ELSE 'NO GALLERY IMAGES'
  END as gallery_images_type
FROM products 
WHERE featured_image IS NOT NULL OR thumbnail_images IS NOT NULL
LIMIT 5;

-- Check storage bucket status
SELECT 'Storage Bucket Status' as check;
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'product-images') 
    THEN 'product-images bucket EXISTS ✓' 
    ELSE 'product-images bucket NOT FOUND ✗'
  END as bucket_status;

-- If bucket exists, show its configuration
SELECT 
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets 
WHERE id = 'product-images';

-- Show storage policies if they exist
SELECT 
  'Storage Policies:' as info,
  policyname,
  cmd as operation,
  qual as condition
FROM pg_policies 
WHERE schemaname = 'storage' 
  AND tablename = 'objects'
  AND (policyname LIKE '%product%' OR policyname LIKE '%image%')
ORDER BY policyname;
