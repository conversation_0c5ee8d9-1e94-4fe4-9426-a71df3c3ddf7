-- Fix Orders RLS Policies for YalaOffice
-- Run this in Supabase SQL Editor to fix order creation and visibility issues

-- =============================================
-- DROP EXISTING POLICIES
-- =============================================

DROP POLICY IF EXISTS orders_customer_own ON orders;
DROP POLICY IF EXISTS orders_insert_policy ON orders;
DROP POLICY IF EXISTS orders_update_policy ON orders;
DROP POLICY IF EXISTS orders_delete_policy ON orders;

-- =============================================
-- CREATE COMPREHENSIVE ORDER POLICIES
-- =============================================

-- Orders SELECT policy - customers see their own orders, admins/managers see all
CREATE POLICY orders_select_policy ON orders
    FOR SELECT USING (
        -- Customers can see orders where they are the customer
        auth.uid() = customer_id OR
        -- Users can see orders they created
        auth.uid() = created_by OR
        -- Admins and managers can see all orders
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_type IN ('admin', 'manager')
        )
    );

-- Orders INSERT policy - authenticated users can create orders
CREATE POLICY orders_insert_policy ON orders
    FOR INSERT WITH CHECK (
        -- Must be authenticated
        auth.uid() IS NOT NULL AND
        -- Must set created_by to current user
        created_by = auth.uid() AND
        -- Customer must exist and be active
        EXISTS (
            SELECT 1 FROM users
            WHERE id = customer_id
            AND is_active = true
            AND user_type IN ('client', 'reseller')
        )
    );

-- Orders UPDATE policy - creators and admins can update orders
CREATE POLICY orders_update_policy ON orders
    FOR UPDATE USING (
        -- Order creator can update
        auth.uid() = created_by OR
        -- Customer can update their own orders (limited fields)
        auth.uid() = customer_id OR
        -- Admins and managers can update all orders
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_type IN ('admin', 'manager')
        )
    );

-- Orders DELETE policy - only admins can delete orders
CREATE POLICY orders_delete_policy ON orders
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND user_type = 'admin'
        )
    );

-- =============================================
-- ORDER ITEMS POLICIES
-- =============================================

-- Drop existing order items policies
DROP POLICY IF EXISTS order_items_policy ON order_items;

-- Order items SELECT policy - same as orders
CREATE POLICY order_items_select_policy ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders
            WHERE orders.id = order_items.order_id
            AND (
                auth.uid() = orders.customer_id OR
                auth.uid() = orders.created_by OR
                EXISTS (
                    SELECT 1 FROM users
                    WHERE id = auth.uid()
                    AND user_type IN ('admin', 'manager')
                )
            )
        )
    );

-- Order items INSERT policy - can insert if can create the parent order
CREATE POLICY order_items_insert_policy ON order_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM orders
            WHERE orders.id = order_items.order_id
            AND (
                auth.uid() = orders.created_by OR
                EXISTS (
                    SELECT 1 FROM users
                    WHERE id = auth.uid()
                    AND user_type IN ('admin', 'manager')
                )
            )
        )
    );

-- Order items UPDATE policy - same as orders
CREATE POLICY order_items_update_policy ON order_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM orders
            WHERE orders.id = order_items.order_id
            AND (
                auth.uid() = orders.created_by OR
                EXISTS (
                    SELECT 1 FROM users
                    WHERE id = auth.uid()
                    AND user_type IN ('admin', 'manager')
                )
            )
        )
    );

-- Order items DELETE policy - same as orders
CREATE POLICY order_items_delete_policy ON order_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM orders
            WHERE orders.id = order_items.order_id
            AND (
                auth.uid() = orders.created_by OR
                EXISTS (
                    SELECT 1 FROM users
                    WHERE id = auth.uid()
                    AND user_type = 'admin'
                )
            )
        )
    );

-- =============================================
-- HELPER FUNCTIONS
-- =============================================

-- Function to get orders with proper permissions
CREATE OR REPLACE FUNCTION get_orders_for_user()
RETURNS TABLE (
    id UUID,
    order_number VARCHAR(50),
    customer_id UUID,
    customer_name TEXT,
    customer_email TEXT,
    branch_id UUID,
    branch_name TEXT,
    status VARCHAR(20),
    payment_status VARCHAR(20),
    payment_method VARCHAR(20),
    subtotal DECIMAL(10,2),
    delivery_fee DECIMAL(10,2),
    discount_amount DECIMAL(10,2),
    tax_amount DECIMAL(10,2),
    total DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    created_by UUID,
    item_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_user_id UUID;
    current_user_type TEXT;
BEGIN
    -- Get current user info
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RAISE EXCEPTION 'User not authenticated';
    END IF;
    
    -- Get user type
    SELECT user_type INTO current_user_type
    FROM users
    WHERE users.id = current_user_id;
    
    -- Return orders based on user permissions
    IF current_user_type IN ('admin', 'manager') THEN
        -- Admins and managers see all orders
        RETURN QUERY
        SELECT 
            o.id,
            o.order_number,
            o.customer_id,
            u.full_name as customer_name,
            u.email as customer_email,
            o.branch_id,
            b.name as branch_name,
            o.status,
            o.payment_status,
            o.payment_method,
            o.subtotal,
            o.delivery_fee,
            o.discount_amount,
            o.tax_amount,
            o.total,
            o.created_at,
            o.updated_at,
            o.created_by,
            COALESCE(item_counts.item_count, 0) as item_count
        FROM orders o
        LEFT JOIN users u ON o.customer_id = u.id
        LEFT JOIN branches b ON o.branch_id = b.id
        LEFT JOIN (
            SELECT order_id, COUNT(*) as item_count
            FROM order_items
            GROUP BY order_id
        ) item_counts ON o.id = item_counts.order_id
        ORDER BY o.created_at DESC;
    ELSE
        -- Regular users see only their own orders (as customer or creator)
        RETURN QUERY
        SELECT 
            o.id,
            o.order_number,
            o.customer_id,
            u.full_name as customer_name,
            u.email as customer_email,
            o.branch_id,
            b.name as branch_name,
            o.status,
            o.payment_status,
            o.payment_method,
            o.subtotal,
            o.delivery_fee,
            o.discount_amount,
            o.tax_amount,
            o.total,
            o.created_at,
            o.updated_at,
            o.created_by,
            COALESCE(item_counts.item_count, 0) as item_count
        FROM orders o
        LEFT JOIN users u ON o.customer_id = u.id
        LEFT JOIN branches b ON o.branch_id = b.id
        LEFT JOIN (
            SELECT order_id, COUNT(*) as item_count
            FROM order_items
            GROUP BY order_id
        ) item_counts ON o.id = item_counts.order_id
        WHERE o.customer_id = current_user_id OR o.created_by = current_user_id
        ORDER BY o.created_at DESC;
    END IF;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_orders_for_user() TO authenticated;

-- =============================================
-- ENABLE RLS
-- =============================================

-- Ensure RLS is enabled on orders and order_items tables
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON orders TO authenticated;
GRANT SELECT, INSERT, UPDATE ON order_items TO authenticated;
GRANT DELETE ON orders TO authenticated; -- Will be restricted by RLS policy
GRANT DELETE ON order_items TO authenticated; -- Will be restricted by RLS policy

-- =============================================
-- CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_created_by ON orders(created_by);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Test the function (uncomment to test)
-- SELECT * FROM get_orders_for_user() LIMIT 5;
