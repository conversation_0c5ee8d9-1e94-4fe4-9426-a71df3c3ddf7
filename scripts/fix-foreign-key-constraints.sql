-- =============================================
-- FIX FOREIGN KEY CONSTRAINTS FOR USER DELETION
-- Run this in Supabase SQL Editor to fix foreign key constraints
-- =============================================

-- Step 1: Drop existing foreign key constraints that don't have CASCADE
-- =============================================

-- Check existing constraints first
SELECT 
    tc.table_name, 
    tc.constraint_name, 
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
LEFT JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND ccu.table_name = 'users'
ORDER BY tc.table_name, tc.constraint_name;

-- Step 2: Fix notifications table foreign key constraint
-- =============================================

-- Drop existing constraint if it exists
ALTER TABLE notifications 
DROP CONSTRAINT IF EXISTS notifications_user_id_fkey;

-- Add new constraint with CASCADE
ALTER TABLE notifications 
ADD CONSTRAINT notifications_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Step 3: Fix other tables that reference users
-- =============================================

-- Fix wishlists table
ALTER TABLE wishlists 
DROP CONSTRAINT IF EXISTS wishlists_customer_id_fkey;

ALTER TABLE wishlists 
ADD CONSTRAINT wishlists_customer_id_fkey 
FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE;

-- Fix product_reviews table
ALTER TABLE product_reviews 
DROP CONSTRAINT IF EXISTS product_reviews_customer_id_fkey;

ALTER TABLE product_reviews 
ADD CONSTRAINT product_reviews_customer_id_fkey 
FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE;

-- Fix customer_behavior table
ALTER TABLE customer_behavior 
DROP CONSTRAINT IF EXISTS customer_behavior_customer_id_fkey;

ALTER TABLE customer_behavior 
ADD CONSTRAINT customer_behavior_customer_id_fkey 
FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE;

-- Fix order_templates table
ALTER TABLE order_templates 
DROP CONSTRAINT IF EXISTS order_templates_customer_id_fkey;

ALTER TABLE order_templates 
ADD CONSTRAINT order_templates_customer_id_fkey 
FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE;

-- Step 4: Handle orders table (SET NULL instead of CASCADE to preserve order history)
-- =============================================

-- Fix orders customer_id constraint
ALTER TABLE orders 
DROP CONSTRAINT IF EXISTS orders_customer_id_fkey;

ALTER TABLE orders 
ADD CONSTRAINT orders_customer_id_fkey 
FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE SET NULL;

-- Fix orders created_by constraint
ALTER TABLE orders 
DROP CONSTRAINT IF EXISTS orders_created_by_fkey;

ALTER TABLE orders 
ADD CONSTRAINT orders_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;

-- Step 5: Handle order_tracking table (SET NULL to preserve tracking history)
-- =============================================

ALTER TABLE order_tracking 
DROP CONSTRAINT IF EXISTS order_tracking_updated_by_fkey;

ALTER TABLE order_tracking 
ADD CONSTRAINT order_tracking_updated_by_fkey 
FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL;

-- Step 6: Handle system_configs table (SET NULL)
-- =============================================

ALTER TABLE system_configs 
DROP CONSTRAINT IF EXISTS system_configs_updated_by_fkey;

ALTER TABLE system_configs 
ADD CONSTRAINT system_configs_updated_by_fkey 
FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL;

-- Step 7: Handle audit_logs table (SET NULL to preserve audit trail)
-- =============================================

ALTER TABLE audit_logs 
DROP CONSTRAINT IF EXISTS audit_logs_user_id_fkey;

ALTER TABLE audit_logs 
ADD CONSTRAINT audit_logs_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;

-- Step 8: Verify the changes
-- =============================================

-- Check all foreign key constraints to users table
SELECT 
    tc.table_name, 
    tc.constraint_name,
    kcu.column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
LEFT JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
    AND tc.table_schema = rc.constraint_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND ccu.table_name = 'users'
ORDER BY tc.table_name, tc.constraint_name;

-- Success message
SELECT 'Foreign key constraints fixed successfully! User deletion should now work properly.' as status;
