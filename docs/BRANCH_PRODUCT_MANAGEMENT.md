# Branch-Specific Product Management System

## Overview

The YalaOffice system now supports comprehensive branch-specific product management with real-time synchronization between Product Management and Branch Management interfaces. This system allows products to be assigned to specific branches while maintaining inventory synchronization across all locations.

## Key Features

### 1. Branch Selection in Product Forms
- **Add Product Form**: Mandatory branch selection dropdown
- **Edit Product Form**: Update branch assignment for existing products
- **Branch Information**: Display branch name and location for clear identification
- **Active Branches Only**: Dropdown populated with active branches from database

### 2. Database Integration
- **Branch Inventory Table**: `branch_inventory` table links products to branches
- **Automatic Creation**: Branch inventory records created when products are assigned
- **Stock Synchronization**: Product stock updates sync across all branch locations
- **Referential Integrity**: Proper foreign key relationships maintained

### 3. Real-time Synchronization
- **Cross-Interface Updates**: Changes in Product Management immediately reflect in Branch Management
- **Live Data Sync**: Real-time events ensure UI updates without page refresh
- **Multi-user Support**: Changes visible to all connected users instantly
- **Event-driven Architecture**: Comprehensive event system for data consistency

### 4. Enhanced UI/UX
- **Branch Column**: Product Management displays branch information for each product
- **Visual Indicators**: Clear branch identification with name and location
- **Loading States**: Proper loading indicators during branch data fetching
- **Error Handling**: Robust error handling for branch operations

## Technical Implementation

### Database Schema

#### Products Table
```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    sku VARCHAR(100) UNIQUE NOT NULL,
    category_id UUID REFERENCES categories(id),
    price DECIMAL(10,2) NOT NULL,
    stock INTEGER DEFAULT 0,
    min_stock INTEGER DEFAULT 0,
    -- ... other fields
);
```

#### Branch Inventory Table
```sql
CREATE TABLE branch_inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    branch_id UUID REFERENCES branches(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    stock INTEGER DEFAULT 0,
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER,
    reserved_stock INTEGER DEFAULT 0,
    last_restocked TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(branch_id, product_id)
);
```

### Service Layer

#### Branch Inventory Service (`branchInventoryService.ts`)
```typescript
// Get branch inventory for a specific branch
export const getBranchInventory = async (branchId: string): Promise<BranchInventory[]>

// Update branch inventory
export const updateBranchInventory = async (update: BranchInventoryUpdate): Promise<boolean>

// Sync product stock across all branches
export const syncProductStockAcrossBranches = async (
  productId: string, 
  newStock: number, 
  newMinStock: number
): Promise<boolean>
```

#### Enhanced Inventory Service
```typescript
// Create product with branch assignment
export const createProduct = async (
  productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'> & { branchId?: string }
): Promise<Product | null>

// Update product with branch synchronization
export const updateProduct = async (
  id: string, 
  updates: Partial<Product> & { branchId?: string }, 
  userId?: string
): Promise<Product | null>
```

### Component Integration

#### ProductForm Component
- **Branch Selection Field**: Mandatory dropdown for branch assignment
- **Branch Loading**: Async loading of active branches
- **Form Validation**: Ensures branch selection for new products
- **Default Selection**: Auto-selects first available branch

#### ProductManagement Component
- **Branch Column**: Displays branch name and location
- **Enhanced Product Data**: Products include branch information
- **Real-time Updates**: Immediate UI updates when products change
- **Branch Filtering**: Filter products by specific branch

#### BranchManagement Component
- **Enhanced Inventory Display**: Shows detailed product information
- **Real-time Sync**: Updates when products are modified
- **Branch-specific View**: Displays only products assigned to selected branch

## API Endpoints

### Product Operations
```typescript
// Create product with branch assignment
POST /api/products
{
  "title": "Product Name",
  "price": 29.99,
  "stock": 100,
  "branchId": "branch-uuid"
}

// Update product with branch sync
PUT /api/products/:id
{
  "stock": 150,
  "branchId": "branch-uuid"
}
```

### Branch Inventory Operations
```typescript
// Get branch inventory
GET /api/branches/:branchId/inventory

// Update branch inventory
PUT /api/branches/:branchId/inventory/:productId
{
  "stock": 75,
  "minStock": 10
}
```

## Real-time Events

### Event Types
- `branch-inventory-updated`: When branch inventory is modified
- `branch-inventory-created`: When new branch inventory is created
- `branch-inventory-synced`: When stock is synchronized across branches
- `product-updated`: When product information changes
- `product-added`: When new product is created

### Event Handling
```typescript
// Subscribe to branch inventory updates
realTimeService.subscribe('branch-inventory-updated', (event) => {
  if (event.branchId === selectedBranch) {
    refreshBranchInventory();
  }
});
```

## Usage Examples

### Creating a Product with Branch Assignment
```typescript
const productData = {
  title: "Premium Notebook Set",
  description: "High-quality notebooks for professionals",
  sku: "NB-PREM-001",
  category: "Notebooks",
  brand: "YalaOffice",
  price: 45.99,
  stock: 200,
  minStock: 20,
  branchId: "branch-casablanca-001" // Assign to Casablanca branch
};

const result = await createProduct(productData);
```

### Updating Product Stock Across Branches
```typescript
// Update stock for all branches carrying this product
await syncProductStockAcrossBranches(
  "product-uuid",
  300, // new stock level
  25   // new minimum stock
);
```

### Filtering Products by Branch
```typescript
// Get products for specific branch
const branchProducts = await getProducts({ 
  branchId: "branch-rabat-001" 
});
```

## Best Practices

### 1. Data Consistency
- Always use the provided service methods for inventory operations
- Ensure branch inventory is updated when product stock changes
- Use transactions for operations affecting multiple tables

### 2. Real-time Updates
- Subscribe to relevant real-time events in components
- Unsubscribe from events when components unmount
- Handle event errors gracefully

### 3. Error Handling
- Validate branch existence before creating products
- Handle network failures in branch data loading
- Provide user feedback for failed operations

### 4. Performance
- Use pagination for large product lists
- Implement debouncing for search operations
- Cache branch data to reduce API calls

## Testing

### Unit Tests
- Test product creation with branch assignment
- Test inventory synchronization across branches
- Test real-time event handling

### Integration Tests
- Test complete product-to-branch workflow
- Test cross-interface synchronization
- Test error scenarios and recovery

### Example Test
```typescript
it('should create product and assign to branch', async () => {
  const productData = {
    title: 'Test Product',
    branchId: testBranchId,
    stock: 100
  };
  
  const product = await createProduct(productData);
  const inventory = await getBranchInventory(testBranchId);
  
  expect(inventory.find(i => i.productId === product.id)).toBeTruthy();
});
```

## Troubleshooting

### Common Issues
1. **Branch not loading**: Check network connection and branch service
2. **Inventory not syncing**: Verify real-time event subscriptions
3. **Product not appearing in branch**: Confirm branch assignment in database

### Debug Steps
1. Check browser console for error messages
2. Verify database constraints and relationships
3. Test real-time event emission and reception
4. Validate service method responses

## Future Enhancements

### Planned Features
- **Multi-branch Assignment**: Allow products to be assigned to multiple branches
- **Stock Transfer Interface**: Direct stock transfers between branches
- **Branch Performance Analytics**: Track sales and inventory by branch
- **Automated Reordering**: Automatic stock replenishment based on branch needs

### Technical Improvements
- **Optimistic Updates**: Immediate UI updates with rollback on failure
- **Offline Support**: Cache branch data for offline operation
- **Advanced Filtering**: Complex queries for branch-product relationships
- **Bulk Operations**: Mass assignment and updates for multiple products
