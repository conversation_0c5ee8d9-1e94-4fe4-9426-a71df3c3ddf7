# YalaOffice Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying YalaOffice to production environments with high availability, security, and performance optimization.

## Prerequisites

### System Requirements

- **Operating System**: Ubuntu 20.04 LTS or CentOS 8+
- **CPU**: Minimum 4 cores, Recommended 8+ cores
- **RAM**: Minimum 8GB, Recommended 16GB+
- **Storage**: Minimum 100GB SSD, Recommended 500GB+ SSD
- **Network**: Stable internet connection with static IP

### Software Requirements

- Docker 20.10+
- Docker Compose 2.0+
- Git 2.30+
- Node.js 18+ (for build process)
- Nginx (for reverse proxy)
- SSL certificates (Let's Encrypt recommended)

### External Services

- **Supabase**: PostgreSQL database and authentication
- **Redis**: Caching and session storage
- **AWS S3**: File storage and backups
- **Email Service**: SMTP for notifications
- **Monitoring**: Prometheus, Grafana, Loki

## Pre-Deployment Setup

### 1. Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application directory
sudo mkdir -p /opt/yalaoffice
sudo chown $USER:$USER /opt/yalaoffice
```

### 2. SSL Certificate Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot certonly --standalone -d yalaoffice.com -d www.yalaoffice.com

# Copy certificates to application directory
sudo cp /etc/letsencrypt/live/yalaoffice.com/fullchain.pem /opt/yalaoffice/ssl/cert.pem
sudo cp /etc/letsencrypt/live/yalaoffice.com/privkey.pem /opt/yalaoffice/ssl/key.pem
sudo chown $USER:$USER /opt/yalaoffice/ssl/*
```

### 3. Environment Configuration

```bash
# Clone repository
cd /opt/yalaoffice
git clone https://github.com/your-org/yalaoffice.git .

# Copy production environment file
cp .env.production .env

# Edit environment variables
nano .env
```

### 4. Database Setup (Supabase)

1. Create Supabase project at https://supabase.com
2. Configure database schema using provided SQL files
3. Set up Row Level Security (RLS) policies
4. Configure authentication providers
5. Update environment variables with Supabase credentials

## Deployment Process

### 1. Build and Deploy

```bash
# Navigate to application directory
cd /opt/yalaoffice

# Build production images
docker-compose -f docker-compose.prod.yml build

# Start services
docker-compose -f docker-compose.prod.yml up -d

# Verify deployment
docker-compose -f docker-compose.prod.yml ps
```

### 2. Health Checks

```bash
# Check application health
curl -f http://localhost/health

# Check service logs
docker-compose -f docker-compose.prod.yml logs -f yalaoffice-app

# Monitor resource usage
docker stats
```

### 3. Load Balancer Configuration

```bash
# Install and configure Nginx
sudo apt install nginx

# Copy Nginx configuration
sudo cp nginx-lb.conf /etc/nginx/sites-available/yalaoffice
sudo ln -s /etc/nginx/sites-available/yalaoffice /etc/nginx/sites-enabled/

# Test and reload Nginx
sudo nginx -t
sudo systemctl reload nginx
```

## Monitoring Setup

### 1. Prometheus Configuration

```bash
# Create monitoring directories
mkdir -p /opt/yalaoffice/monitoring/{prometheus,grafana,loki}

# Start monitoring stack
docker-compose -f docker-compose.prod.yml up -d prometheus grafana loki promtail
```

### 2. Grafana Dashboard Setup

1. Access Grafana at http://your-server:3000
2. Login with admin credentials from environment
3. Import provided dashboard configurations
4. Configure data sources (Prometheus, Loki)
5. Set up alerting rules and notification channels

### 3. Log Aggregation

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/yalaoffice

# Content:
/var/log/yalaoffice/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 www-data www-data
}
```

## Security Hardening

### 1. Firewall Configuration

```bash
# Configure UFW firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. Docker Security

```bash
# Create non-root user for containers
sudo useradd -r -s /bin/false yalaoffice

# Set proper file permissions
sudo chown -R yalaoffice:yalaoffice /opt/yalaoffice
sudo chmod -R 750 /opt/yalaoffice
```

### 3. Database Security

- Enable SSL connections to Supabase
- Configure Row Level Security (RLS)
- Use strong passwords and rotate regularly
- Enable audit logging
- Implement backup encryption

## Backup and Recovery

### 1. Automated Backups

```bash
# Make backup script executable
chmod +x scripts/backup/backup.sh

# Set up cron job for daily backups
crontab -e

# Add line:
0 2 * * * /opt/yalaoffice/scripts/backup/backup.sh
```

### 2. Backup Verification

```bash
# Test backup script
./scripts/backup/backup.sh

# Verify backup integrity
tar -tzf /backups/yalaoffice_backup_*.tar.gz

# Test restore process (on staging environment)
./scripts/restore.sh /backups/yalaoffice_backup_latest.tar.gz
```

## Performance Optimization

### 1. Database Optimization

- Configure connection pooling
- Optimize query performance
- Set up read replicas if needed
- Monitor slow queries
- Implement proper indexing

### 2. Caching Strategy

```bash
# Redis configuration optimization
echo "maxmemory 2gb" >> /opt/yalaoffice/redis.conf
echo "maxmemory-policy allkeys-lru" >> /opt/yalaoffice/redis.conf
```

### 3. CDN Setup

- Configure AWS CloudFront or similar CDN
- Set up proper cache headers
- Optimize static asset delivery
- Implement image optimization

## Scaling Considerations

### 1. Horizontal Scaling

```bash
# Scale application containers
docker-compose -f docker-compose.prod.yml up -d --scale yalaoffice-app=3

# Configure load balancer for multiple instances
# Update nginx-lb.conf with additional upstream servers
```

### 2. Database Scaling

- Implement read replicas
- Consider database sharding for large datasets
- Monitor connection pool usage
- Optimize query performance

### 3. Monitoring Scaling

- Set up resource usage alerts
- Monitor response times
- Track error rates
- Implement auto-scaling policies

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Check environment variables
   - Verify database connectivity
   - Review container logs

2. **High memory usage**
   - Monitor memory leaks
   - Optimize database queries
   - Adjust container limits

3. **Slow response times**
   - Check database performance
   - Review caching strategy
   - Analyze network latency

### Log Analysis

```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f yalaoffice-app

# Search for errors
docker-compose -f docker-compose.prod.yml logs yalaoffice-app | grep ERROR

# Monitor real-time logs
tail -f /var/log/yalaoffice/app.log
```

## Maintenance Procedures

### 1. Regular Updates

```bash
# Update application
git pull origin main
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# Update system packages
sudo apt update && sudo apt upgrade -y
```

### 2. SSL Certificate Renewal

```bash
# Renew certificates
sudo certbot renew --dry-run
sudo certbot renew

# Update application certificates
sudo cp /etc/letsencrypt/live/yalaoffice.com/fullchain.pem /opt/yalaoffice/ssl/cert.pem
sudo cp /etc/letsencrypt/live/yalaoffice.com/privkey.pem /opt/yalaoffice/ssl/key.pem
docker-compose -f docker-compose.prod.yml restart nginx-lb
```

### 3. Database Maintenance

- Regular VACUUM and ANALYZE operations
- Monitor table sizes and growth
- Update statistics for query optimization
- Review and optimize slow queries

## Support and Documentation

### Resources

- **Application Logs**: `/var/log/yalaoffice/`
- **Monitoring**: `http://your-server:3000` (Grafana)
- **Metrics**: `http://your-server:9090` (Prometheus)
- **Health Check**: `http://your-server/health`

### Emergency Contacts

- **System Administrator**: <EMAIL>
- **Database Administrator**: <EMAIL>
- **Security Team**: <EMAIL>
- **On-call Support**: +212-XXX-XXXXXX

### Documentation Links

- [API Documentation](https://docs.yalaoffice.com/api)
- [User Manual](https://docs.yalaoffice.com/user-guide)
- [Admin Guide](https://docs.yalaoffice.com/admin)
- [Troubleshooting](https://docs.yalaoffice.com/troubleshooting)
