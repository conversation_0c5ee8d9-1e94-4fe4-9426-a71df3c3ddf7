# YalaOffice Live Data Migration Guide

## 🎯 Overview

This guide walks you through transitioning YalaOffice from demo/mock data to a production-ready system with live data synchronized with Supabase database.

## 📋 What's Included

### **Live Data Structure**

#### **👥 Users (14 Total)**
- **2 Admin Users**: Full system access
  - <PERSON><PERSON><PERSON> (<EMAIL>)
  - <PERSON><PERSON> (<EMAIL>)

- **2 Manager Users**: Branch management
  - <PERSON> (<EMAIL>)
  - <PERSON><PERSON> (<EMAIL>)

- **4 Client Users**: Regular customers
  - <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>

- **4 Reseller Users**: Wholesale accounts
  - Papeterie Moderne SARL (15% discount, 10,000 MAD credit)
  - Fournitures du Nord (12% discount, 8,000 MAD credit)
  - École Plus Distribution (18% discount, 15,000 MAD credit)
  - Bureau Express (10% discount, 6,000 MAD credit)

- **2 Delivery Personnel**: Order fulfillment
  - <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

#### **🏢 Branches (3 in Tetouan)**
1. **YalaOffice Centre Ville** (Main Branch)
   - Address: Avenue Mohammed V, <PERSON><PERSON><PERSON><PERSON>, Tetouan
   - Manager: <PERSON>zi
   - Hours: 8:00-19:00 (Mon-Fri), 9:00-17:00 (Sat), 10:00-15:00 (Sun)

2. **YalaOffice Quartier Administratif**
   - Address: Rue Hassan II, Résidence Al Manar, Tetouan
   - Manager: <PERSON>ima Alaoui
   - Hours: 8:30-18:30 (Mon-Fri), 9:00-16:00 (Sat), Closed (Sun)

3. **YalaOffice Martil**
   - Address: Avenue Corniche, Complexe Al Bahja, Martil
   - Manager: Omar Tazi (Secondary)
   - Hours: 9:00-18:00 (Mon-Fri), 9:00-17:00 (Sat), 10:00-14:00 (Sun)

#### **📦 Product Categories (10 Total)**
1. **Writing Instruments** - Pens, pencils, markers
2. **Paper & Notebooks** - All paper products and notebooks
3. **School & Office Supplies** - Essential supplies
4. **Art & Craft Supplies** - Creative materials
5. **Filing & Organization** - Storage solutions
6. **Greeting Cards & Gift Supplies** - Cards and wrapping
7. **Office & Desk Accessories** - Desk organizers and tools
8. **Back-to-School Essentials** - Complete school packages
9. **Eco-Friendly Stationery** - Sustainable products
10. **Specialty & Luxury Stationery** - Premium items

#### **🛍️ Products (44+ Items)**
- Realistic products with Moroccan pricing (MAD)
- Stock distributed across 3 branches
- Supplier information and specifications
- Product variants and images

## 🚀 Migration Process

### **Step 1: Environment Setup**

```bash
# 1. Ensure you have Supabase credentials
cp .env.example .env
# Edit .env with your Supabase credentials:
# VITE_SUPABASE_URL=your-supabase-url
# VITE_SUPABASE_ANON_KEY=your-anon-key
# SUPABASE_SERVICE_KEY=your-service-key

# 2. Install dependencies
npm install
```

### **Step 2: Database Setup**

```bash
# Run the complete database setup
npm run setup:database

# This will:
# - Initialize Supabase project
# - Create database schema
# - Populate live data
# - Verify setup
```

### **Step 3: Test Database Connection**

```bash
# Test the database connection and data integrity
npm run test:database

# This will verify:
# - Database connectivity
# - Data population
# - Table relationships
# - Real-time subscriptions
```

### **Step 4: Migrate Application Code**

```bash
# Scan for mock data and generate replacements
npm run migrate:live-data

# This will:
# - Identify all mock data locations
# - Generate replacement templates
# - Create migration summary
```

### **Step 5: Update Services**

Replace mock data services with live data integration:

```typescript
// OLD: Mock data service
const mockProducts = [
  { id: '1', title: 'Product 1', price: 100 }
];

// NEW: Live data service
import { liveDataService } from '@/services/liveDataService';

const products = await liveDataService.getAllProducts();
```

### **Step 6: Update Components**

Use live data hooks in components:

```typescript
// OLD: Mock data in component
const [products] = useState(mockProducts);

// NEW: Live data hook
import { useLiveProducts } from '@/hooks/useLiveData';
const { data: products, loading, error } = useLiveProducts();
```

### **Step 7: Test Integration**

```bash
# Start development server
npm run dev

# Test all functionality:
# - User authentication
# - Product browsing
# - Order creation
# - Inventory management
# - Analytics dashboard
```

## 📁 Files Created/Modified

### **New Files**
- `scripts/setup-live-data.sql` - Live data population
- `scripts/populate-products.sql` - Product data
- `scripts/setup-database.sh` - Database setup script
- `scripts/test-database-connection.ts` - Connection testing
- `scripts/migrate-to-live-data.ts` - Migration script
- `src/services/liveDataService.ts` - Live data service
- `docs/LIVE_DATA_MIGRATION.md` - This guide

### **Modified Files**
- `package.json` - Added database scripts
- Components using mock data (to be updated)
- Services with hardcoded data (to be updated)

## 🔧 Live Data Service API

### **User Management**
```typescript
// Get all users
const users = await liveDataService.getAllUsers();

// Get users by type
const admins = await liveDataService.getUsersByType('admin');

// Create user
const newUser = await liveDataService.createUser(userData);
```

### **Product Management**
```typescript
// Get all products
const products = await liveDataService.getAllProducts();

// Get products by category
const categoryProducts = await liveDataService.getProductsByCategory(categoryId);

// Search products
const searchResults = await liveDataService.searchProducts('pen');
```

### **Order Management**
```typescript
// Get all orders
const orders = await liveDataService.getAllOrders();

// Get orders by status
const pendingOrders = await liveDataService.getOrdersByStatus('pending');
```

### **Real-time Subscriptions**
```typescript
// Subscribe to product changes
const subscription = liveDataService.subscribeToProducts((payload) => {
  console.log('Product updated:', payload);
});

// Unsubscribe
subscription.unsubscribe();
```

## 🎯 Business Configuration

### **Moroccan Business Settings**
- **Currency**: Moroccan Dirham (MAD)
- **Tax System**: Morocco VAT (TVA) ready
- **Location**: Tetouan, Tanger-Tetouan-Al Hoceima region
- **Business Hours**: Adapted to Moroccan business culture
- **Language Support**: Arabic, French, English

### **Pricing Structure**
- **Retail Prices**: End customer pricing in MAD
- **Reseller Prices**: Wholesale pricing with discounts (10-18%)
- **Cost Prices**: Internal cost tracking
- **Delivery Fees**: 25 MAD default, free over 500 MAD

### **User Roles & Permissions**
- **Admin**: Full system access, user management
- **Manager**: Branch management, inventory, orders
- **Client**: Browse products, place orders, view history
- **Reseller**: Wholesale access, special pricing, bulk orders
- **Delivery**: Order fulfillment, delivery tracking

## 🔍 Troubleshooting

### **Common Issues**

1. **Database Connection Failed**
   ```bash
   # Check environment variables
   echo $VITE_SUPABASE_URL
   echo $VITE_SUPABASE_ANON_KEY
   
   # Test connection
   npm run test:database
   ```

2. **Empty Tables**
   ```bash
   # Re-run database setup
   npm run setup:database
   
   # Populate products manually
   npm run populate:products
   ```

3. **Real-time Not Working**
   - Check Supabase project settings
   - Verify Row Level Security policies
   - Test with simple subscription

4. **Authentication Issues**
   - Verify Supabase auth configuration
   - Check user roles and permissions
   - Test with admin user first

### **Getting Help**

1. **Check Logs**
   ```bash
   # Database setup logs
   cat database-setup.log
   
   # Migration logs
   cat scripts/migration-summary.json
   ```

2. **Verify Data**
   ```bash
   # Test database connection
   npm run test:database
   
   # Check specific tables
   psql $DATABASE_URL -c "SELECT COUNT(*) FROM users;"
   ```

3. **Reset Database**
   ```bash
   # Complete reset (use with caution)
   supabase db reset --linked
   npm run setup:database
   ```

## ✅ Success Criteria

Your migration is successful when:

- ✅ All database tests pass
- ✅ 14 users created with proper roles
- ✅ 3 Tetouan branches configured
- ✅ 10 product categories populated
- ✅ 44+ products with realistic data
- ✅ Real-time subscriptions working
- ✅ Application loads without mock data
- ✅ All CRUD operations functional
- ✅ Authentication working properly
- ✅ Role-based access control active

## 🚀 Next Steps

After successful migration:

1. **Production Deployment**
   ```bash
   npm run build:prod
   npm run deploy:production
   ```

2. **User Training**
   - Train staff on new system
   - Import existing customer data
   - Configure business-specific settings

3. **Go Live**
   - Switch from demo to production
   - Monitor system performance
   - Collect user feedback

4. **Ongoing Maintenance**
   - Regular database backups
   - Performance monitoring
   - Security updates

---

**🎉 Congratulations!** You've successfully migrated YalaOffice to live data integration. Your system is now ready for production use with real Moroccan business data!
