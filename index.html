
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>YalaOffice - Office & School Supplies Management System</title>
    <meta name="description" content="YalaOffice - Complete stock and order management system for office and school supplies in Morocco. Multi-branch support, delivery tracking, and wholesale pricing." />
    <meta name="author" content="YalaOffice" />
    <meta name="keywords" content="office supplies, school supplies, Morocco, inventory management, wholesale, retail, stationery" />

    <meta property="og:title" content="YalaOffice - Office & School Supplies Management" />
    <meta property="og:description" content="Complete stock and order management system for office and school supplies in Morocco" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/favicon.ico" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="YalaOffice - Smart Supply Management" />
    <meta name="twitter:description" content="Complete stock and order management system for office and school supplies in Morocco" />

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="YalaOffice" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="YalaOffice" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-TileColor" content="#3B82F6" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="theme-color" content="#3B82F6" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />

    <!-- PWA Icons -->
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="96x96" href="/icons/icon-96x96.png" />
    <link rel="apple-touch-icon" sizes="128x128" href="/icons/icon-128x128.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="384x384" href="/icons/icon-384x384.png" />
    <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png" />

    <!-- Moroccan Dirham Currency Support -->
    <meta name="currency" content="MAD" />
    <meta name="locale" content="en_MA" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Service Worker Registration with Cache Clearing -->
    <script>
      if ('serviceWorker' in navigator) {
        // Clear all caches first
        if ('caches' in window) {
          caches.keys().then(function(cacheNames) {
            return Promise.all(
              cacheNames.map(function(cacheName) {
                console.log('Clearing cache:', cacheName);
                return caches.delete(cacheName);
              })
            );
          }).then(function() {
            console.log('All caches cleared');
            // Register service worker after clearing caches
            navigator.serviceWorker.register('/sw.js')
              .then(function(registration) {
                console.log('Service Worker registered:', registration);
                // Force update if there's a waiting service worker
                if (registration.waiting) {
                  registration.waiting.postMessage({command: 'skipWaiting'});
                }
              })
              .catch(function(error) {
                console.log('Service Worker registration failed:', error);
              });
          });
        } else {
          // Fallback if caches API is not available
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('Service Worker registered:', registration);
            })
            .catch(function(error) {
              console.log('Service Worker registration failed:', error);
            });
        }
      }
    </script>
  </body>
</html>
