# YalaOffice - Smart Supply Management System

## Project Description

YalaOffice is a comprehensive supply chain management platform designed for office and school supplies. It provides multi-branch operations, real-time tracking, intelligent analytics, and automated workflows.

## Features

- **Smart Inventory Management** - AI-powered stock management with predictive alerts
- **Order Management** - Streamlined workflow with real-time tracking
- **Role-Based Access Control** - Granular permissions for different user types
- **Multi-Branch Operations** - Centralized control with location-specific customization
- **Smart Delivery Tracking** - GPS tracking and route optimization
- **Advanced Analytics** - AI-driven insights and custom reporting

## Getting Started

### Prerequisites

- Node.js (v18 or higher) - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- npm or yarn package manager

### Installation

1. Clone the repository:
```sh
git clone <YOUR_GIT_URL>
cd yala-office
```

2. Install dependencies:
```sh
npm install
```

3. Start the development server:
```sh
npm run dev
```

The application will be available at `http://localhost:8080`

### Development

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint for code quality

## Technology Stack

This project is built with modern web technologies:

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + shadcn/ui components
- **Backend**: Supabase
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router DOM
- **Authentication**: Custom auth with Supabase

## Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── services/           # API services and business logic
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── lib/                # Library configurations
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is proprietary software. All rights reserved.
